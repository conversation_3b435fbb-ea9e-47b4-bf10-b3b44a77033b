# Service的名字，必选，需自定义修改
Name = "zone"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 5000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 15000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 150000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 2

[Strategy]
    # 资源使用策略，非必选，默认使用 RoundRobin
    # RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
    Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "logic-zone.bj.internal-qasandbox.baidu-int.com"
        Port = 8795
    [[Resource.Manual.minicloud]]
        Host = "**************"
        Port = 8795
    [[Resource.Manual.onlinebj]]
        Host = "zone.bj.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinebd]]
        Host = "zone.bdbl.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinefsh]]
        Host = "zone.fsh.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinegz]]
        Host = "zone.gz.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinehkg]]
        Host = "zone.hkg.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinesu]]
        Host = "zone.su.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinewh]]
        Host = "zone.fwh.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlineyq]]
        Host = "zone.yq.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinecd]]
        Host = "zone.cd.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinenj]]
        Host = "zone.nj.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.preonline]]
        Host = "gzhxy-y32-preonline02.gzhxy.baidu.com"
        Port = 8795
    [[Resource.Manual.bjtest]]
        Host = "zone.bj.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.bdtest]]
        Host = "zone.bdbl.bce.baidu-int.com"
        Port = 80

[zone]
    Product = "vdb"