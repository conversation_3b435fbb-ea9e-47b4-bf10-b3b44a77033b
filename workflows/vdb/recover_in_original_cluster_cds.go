/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* upgrade_cluster_app.go - the controller of upgrade cluster app  */
/*
modification history
--------------------
2024/09/2, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
执行通过cds恢复实例

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"AppBatchID":  ..
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	mountdisk "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/mount_disk"
	restartmochow "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/restart_mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/recover"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowRecoverInOriginalClusterCDS                  = "vdb-recover-in-original-cluster-cds-app"
	StepRecoverInOriginalClusterCDSCloseHa               = "vdb-recover-in-original-cluster-cds-close-ha"
	StepRecoverInOriginalClusterCDSShutdownMochow        = "vdb-recover-in-original-cluster-cds-shutdown-mochow"
	StepRecoverInOriginalClusterCDSRemoveMasterData      = "vdb-recover-in-original-cluster-cds-remove-master-data"
	StepRecoverInOriginalClusterCDSUmount                = "vdb-recover-in-original-cluster-cds-umount"
	StepRecoverInOriginalClusterCDSDeleteOldCDS          = "vdb-recover-in-original-cluster-cds-delete-old-cds"
	StepRecoverInOriginalClusterCDSCreateNewCDS          = "vdb-recover-in-original-cluster-cds-create-new-cds"
	StepRecoverInOriginalClusterCDSMountNewCDS           = "vdb-recover-in-original-cluster-cds-mount-new-cds"
	StepRecoverInOriginalClusterCDSSetMasterAddr         = "vdb-recover-in-original-cluster-cds-set-master-addr"
	StepRecoverInOriginalClusterCDSLeaderMasterRecover   = "vdb-recover-in-original-cluster-cds-leader-master-recover"
	StepRecoverInOriginalClusterCDSSetMasterSingleMode   = "vdb-recover-in-original-cluster-cds-set-master-single-mode"
	StepRecoverInOriginalClusterCDSRelocateDatanodeAddr  = "vdb-recover-in-original-cluster-cds-relocate-datanode-addr"
	StepRecoverInOriginalClusterCDSFollowerMasterRecover = "vdb-recover-in-original-cluster-cds-follower-master-recover"
	StepRecoverInOriginalClusterCDSAddFollowerMaster     = "vdb-recover-in-original-cluster-cds-add-follower-master"
	StepRecoverInOriginalClusterCDSUpdateMasterAddr      = "vdb-recover-in-original-cluster-cds-update-master-addr"
	StepRecoverInOriginalClusterCDSDatanodeRecover       = "vdb-recover-in-original-cluster-cds-datanode-recover"
	StepRecoverInOriginalClusterCDSProxyRecover          = "vdb-recover-in-original-cluster-cds-proxy-recover"
	StepRecoverInOriginalClusterCDSSetTabletPeers        = "vdb-recover-in-original-cluster-cds-set-tablet-peers"
	StepRecoverInOriginalClusterCDSCheckStatus           = "vdb-recover-in-original-cluster-cds-check-status"
	StepRecoverInOriginalClusterCDSRecoverHa             = "vdb-recover-in-original-cluster-cds-recover-ha"
	StepRecoverInOriginalClusterCDSCallback              = "vdb-recover-in-original-cluster-cds-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// Step-1 关闭HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSCloseHa,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     xmaster.ProcessCloseAppHaStatusInXmaster,
		SuccessNextStep: StepRecoverInOriginalClusterCDSShutdownMochow,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSCloseHa,
	})

	// Step-2 关闭内核进程
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSShutdownMochow,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     restartmochow.ProcessStopClusterMochow,
		SuccessNextStep: StepRecoverInOriginalClusterCDSRemoveMasterData,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSShutdownMochow},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetAllNeedShutdownNodeIDs))

	// Step-3 删除follower master数据，master只恢复leader节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSRemoveMasterData,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     restartmochow.ProcessRemoveClusterMochowData,
		SuccessNextStep: StepRecoverInOriginalClusterCDSUmount,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSRemoveMasterData},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetNeedRemoveDataMasterIDs))

	// Step-4 卸载旧CDS
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSUmount,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     mountdisk.ProcessUmountClusterDisk,
		SuccessNextStep: StepRecoverInOriginalClusterCDSDeleteOldCDS,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSUmount},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetAllNeedOperateCDSNodeIDs))

	// Step-5 删除旧CDS
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSDeleteOldCDS,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterDeleteOldCDS,
		SuccessNextStep: StepRecoverInOriginalClusterCDSCreateNewCDS,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSDeleteOldCDS},
		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetAllNeedOperateCDSNodeIDs))

	// Step-6 使用snapshot创建新CDS
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSCreateNewCDS,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterCreateNewCDS,
		SuccessNextStep: StepRecoverInOriginalClusterCDSMountNewCDS,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSCreateNewCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetAllNeedOperateCDSNodeIDs))

	// Step-7 挂载新CDS
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSMountNewCDS,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     mountdisk.ProcessMountClusterDisk,
		SuccessNextStep: StepRecoverInOriginalClusterCDSSetMasterAddr,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSMountNewCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetAllNeedOperateCDSNodeIDs))

	// Step-8 更新leader master的配置文件地址
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSSetMasterAddr,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverSetSingleMasterAddr,
		SuccessNextStep: StepRecoverInOriginalClusterCDSLeaderMasterRecover,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSSetMasterAddr,
	})

	// Step-9 恢复leader master
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSLeaderMasterRecover,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterRecoverLeaderMaster,
		SuccessNextStep: StepRecoverInOriginalClusterCDSSetMasterSingleMode,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSLeaderMasterRecover},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-10 设置leader master为单节点模式
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSSetMasterSingleMode,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterSetLeaderMasterSingleMode,
		SuccessNextStep: StepRecoverInOriginalClusterCDSRelocateDatanodeAddr,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSSetMasterSingleMode},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-11 迁移datanode地址
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSRelocateDatanodeAddr,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterRelocateDatanodeAddr,
		SuccessNextStep: StepRecoverInOriginalClusterCDSFollowerMasterRecover,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSRelocateDatanodeAddr},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-12 恢复follower master
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSFollowerMasterRecover,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterRecoverFollowerMaster,
		SuccessNextStep: StepRecoverInOriginalClusterCDSAddFollowerMaster,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSFollowerMasterRecover},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(recover.GetRecoverFollowerMasterIDs))

	// Step-13 将follower master恢复为集群
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSAddFollowerMaster,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterAddFollowerMasterPeer,
		SuccessNextStep: StepRecoverInOriginalClusterCDSUpdateMasterAddr,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSAddFollowerMaster},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-14 更新leader master的配置文件地址
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSUpdateMasterAddr,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverUpdateMasterAddr,
		SuccessNextStep: StepRecoverInOriginalClusterCDSDatanodeRecover,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSUpdateMasterAddr,
	})

	// Step-15 恢复datanode
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSDatanodeRecover,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterRecoverDataNode,
		SuccessNextStep: StepRecoverInOriginalClusterCDSProxyRecover,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSDatanodeRecover},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-16 恢复proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSProxyRecover,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverInOriginalClusterRecoverProxy,
		SuccessNextStep: StepRecoverInOriginalClusterCDSSetTabletPeers,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSProxyRecover},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-17 设置tabletPeers
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSSetTabletPeers,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     recover.ProcessRecoverSetAppTabletPeers,
		SuccessNextStep: StepRecoverInOriginalClusterCDSCheckStatus,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSSetTabletPeers},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-18 检查状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSCheckStatus,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     mochow.ProcessCheckAllStatus,
		SuccessNextStep: StepRecoverInOriginalClusterCDSRecoverHa,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSCheckStatus},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-19 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSRecoverHa,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: StepRecoverInOriginalClusterCDSCallback,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSRecoverHa,
	})

	// Step-20 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCDSCallback,
		Workflow:        WorkflowRecoverInOriginalClusterCDS,
		StepProcess:     callback.ProcessRecoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverInOriginalClusterCDSCallback,
	})
}
