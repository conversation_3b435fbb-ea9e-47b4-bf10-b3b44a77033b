# 项目名称

该项目基于 GDP2 框架开发：http://gdp.baidu-int.com/

此模块相关文档：
1. 设计文档：「请补充」 
2. API文档：「请补充」 
3. 监控报表：「请补充」 


## 1 快速开始
如何构建、安装、运行

### 1.1 配置文件目录说明：
| 目录名         | 配置内容   | 编译说明                  |
|-------------|--------|-----------------------|
| conf        | 开发时的配置 | 编译时会忽略                |
| conf_online | 线上配置文件 | 编译时会打包到产出文件，并命令为 conf |
| conf_qa     | 测试的配置  | 编译时会忽略                |

主要配置：
1. 端口区间： conf/port.conf 。在 Pandora 平台上，会自动生成该文件。
2. 各个 Server 的端口、超时配置：conf/server.toml
3. 日志配置： conf/logit/*.toml, 每个日志一个配置文件
4. 下游服务配置： conf/servicer/*.toml, 每个下游一个配置文件

### 1.2 运行：


```bash
> go run main.go                        # 使用 conf 目录的配置运行
> go run main.go -conf conf/app.toml    # 使用 conf 目录的配置运行
> go run main.go -conf conf_qa/app.toml # 使用 conf_qa 目录的配置运行
```

### 1.3 更新 GDP 的所有组件到最新：
```bash
gdp get

# 或者

go get icode.baidu.com/baidu/gdp/...
```

[升级注意事项](https://gdp.baidu-int.com/gdp2/docs/upgrade/)

## 2 测试
```bash
go test -race -v -cover ./...
```
或者
```bash
make test
```

## 3 如何贡献
贡献 patch 流程、质量要求：
* 代码编写要符合[百度 Go 代码规范](http://gdp.baidu-int.com/go_style_guide?from=icode.baidu.com/baidu/vdb/x1-task-manager)
* 有对应的单测代码
* 代码是格式化好的

推荐代码格式化工具：
https://github.com/fsgo/go_fmt

## 4 讨论
GDP-用户交流群：1612141
