/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/01/13 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file dba_util.go
 * <AUTHOR>
 * @date 2023/01/13 14:20:47
 * @brief
 *
 **/

package util

import (
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/component/repo"
)

const (
	packagePrefix = "scs-dba-package"
	repoName      = "package_bos"
)

func GetDBAPackageName(version string) string {
	return fmt.Sprintf("%s_%s.tar.gz", packagePrefix, version)
}

func GetPackageURI(packageName string) string {
	return repo.GetRepo(repoName).Client.GetFileUrl(packageName)
}
