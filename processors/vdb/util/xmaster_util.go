package util

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xmaster"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/errors"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
)

const (
	AppAttributeKeyClusterType        = "cluster_type"
	AppAttributeValueClusterTypeShard = "cluster_shard"
	AppAttributeValueClusterTypeApp   = "cluster_app"

	AppAttributeKeyShardIDList = "shard_id_list"
	AppAttributeKeyUserID      = "user_id"

	InstanceAttributeKeyClusterType = "role"
	InstanceAttributeValueMaster    = "master"
	InstanceAttributeValueSlave     = "slave"
	InstanceAttributeValueProxy     = "proxy"
)

var (
	XmasterEndpoint = map[string]string{
		"bdtest":            "100.67.230.70:8900",
		"onlinebj":          "100.67.186.214:8900",
		"onlinebd":          "100.67.226.22:8900",
		"onlinegz":          "100.67.233.218:8900",
		"onlinesu":          "100.67.0.148:8900",
		"dbstackadaptorbcc": "127.0.0.1:8900",
	}
)

func GetXmasterEndpoint() string {
	if env.IDC() == conf.XmasterHAConfIns.IdcName {
		return conf.XmasterHAConfIns.XmasterEndpoint
	}

	if endpoint, ok := XmasterEndpoint[env.IDC()]; ok {
		return endpoint
	}
	return XmasterEndpoint["default"]
}

func buildXMaterDataNodeTopology(app *vdbmodel.Application) *xmaster.ClusterTopology {
	instances := make([]*xmaster.Instance, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusInUse || (node.Status == vdbmodel.NodeOrProxyStatusToCreate) {
				instance := &xmaster.Instance{
					InstanceId:     node.NodeID,
					LastActiveTime: time.Now().Unix(),
					Attributes: map[string]string{
						"node_short_id": base_utils.ToString(node.ID),
						"role":          InstanceAttributeValueProxy,
						"is_read_only":  base_utils.ToString(false),
					},
				}
				instances = append(instances, instance)
			}
		}
	}

	t := &xmaster.ClusterTopology{
		Instances: instances,
	}

	return t
}

func buildXMaterProxyTopology(app *vdbmodel.Application) *xmaster.ClusterTopology {
	instances := make([]*xmaster.Instance, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusInUse || (proxy.Status == vdbmodel.NodeOrProxyStatusToCreate) {
				instance := &xmaster.Instance{
					InstanceId:     proxy.ProxyID,
					LastActiveTime: time.Now().Unix(),
					Attributes: map[string]string{
						"node_short_id": base_utils.ToString(proxy.ID),
						"role":          InstanceAttributeValueProxy,
						"is_read_only":  base_utils.ToString(false),
					},
				}
				instances = append(instances, instance)
			}
		}
	}

	t := &xmaster.ClusterTopology{
		Instances: instances,
	}

	return t
}

func buildXMaterMasterTopology(app *vdbmodel.Application) *xmaster.ClusterTopology {
	instances := make([]*xmaster.Instance, 0)
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusInUse || (master.Status == vdbmodel.NodeOrProxyStatusToCreate) {
			instance := &xmaster.Instance{
				InstanceId:     master.MasterID,
				LastActiveTime: time.Now().Unix(),
				Attributes: map[string]string{
					"node_short_id": base_utils.ToString(master.ID),
					"role":          InstanceAttributeValueProxy,
					"is_read_only":  base_utils.ToString(false),
				},
			}
			instances = append(instances, instance)
		}

	}

	t := &xmaster.ClusterTopology{
		Instances: instances,
	}

	return t
}

func buildXMaterAppTypeClusterTopology(app *vdbmodel.Application) *xmaster.ClusterTopology {
	instances := make([]*xmaster.Instance, 0)

	topoOfProxy := buildXMaterProxyTopology(app)
	instances = append(instances, topoOfProxy.Instances...)

	topoOfDataNode := buildXMaterDataNodeTopology(app)
	instances = append(instances, topoOfDataNode.Instances...)

	topoOfMaster := buildXMaterMasterTopology(app)
	instances = append(instances, topoOfMaster.Instances...)

	t := &xmaster.ClusterTopology{
		Instances: instances,
		Attributes: map[string]string{
			"shard_short_id":           base_utils.ToString(app.ID), // NOTE: 使用AppShortID作为 app-cluster 的 shard_short_id
			"app_id":                   app.AppID,
			"region":                   env.IDC(),
			"engine":                   vdbmodel.ProductVDB,
			"type":                     "cluster",
			AppAttributeKeyClusterType: AppAttributeValueClusterTypeApp,
			AppAttributeKeyShardIDList: "",
			AppAttributeKeyUserID:      app.UserID,
		},
	}
	return t
}

func CreateXMasterApplicationTopology(ctx context.Context, appID string) error {
	if err := PutAppTypeClusterTopology(ctx, appID); err != nil {
		return err
	}

	return nil
}

func UpdateXMasterApplicationTopology(ctx context.Context, appID string) error {
	//对vdb全部节点注册为app类型的proxy
	if err := PutAppTypeClusterTopology(ctx, appID); err != nil {
		return err
	}

	return nil
}

func PutAppTypeClusterTopology(ctx context.Context, appID string) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appID))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appID)
	}

	// create or update clusters
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
		ClusterId: app.AppID, // NOTE: 使用 AppId 作为 app-cluster 的 cluster_id
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("appId", appID),
			logit.String("clusterId", app.AppID),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	opFunc := s.CreateCluster
	if queryRsp.Code != xmaster.HttpErrClusterNotFound {
		opFunc = s.UpdateCluster
	}

	// create or update
	opRsp, err := opFunc(ctx, &xmaster.CreateClusterRequest{
		ClusterId: app.AppID,
		Topo:      buildXMaterAppTypeClusterTopology(app),
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster create/update cluster fail", logit.String("appId", appID),
			logit.String("clusterId", app.AppID),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster create/update cluster fail", logit.String("appId", appID),
			logit.String("clusterId", app.AppID),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster create/update cluster fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}

	return nil
}

func DeleteXMasterApplicationTopology(ctx context.Context, appID string) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appID))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appID)
	}

	// 删除 app type cluster topo
	if err = DeleteAppTypeClusterTopology(ctx, appID); err != nil {
		return err
	}

	return nil
}

func DeleteAppTypeClusterTopology(ctx context.Context, appID string) error {
	return DeleteClusterTopologyInXmaster(ctx, appID)
}

func DeleteClusterTopologyInXmaster(ctx context.Context, clusterID string) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
		ClusterId: clusterID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("clusterId", clusterID), logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}

	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "delete cluster topology, target cluster is not exist ", logit.String("clusterId", clusterID))
		return nil
	}
	// delete
	opRsp, err := s.DeleteCluster(ctx, &xmaster.DeleteClusterRequest{
		ClusterId: clusterID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster delete cluster fail", logit.String("clusterId", clusterID), logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster delete cluster fail", logit.String("clusterId", clusterID), logit.String("code", opRsp.Code),
			logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster delete cluster fail, code:%s, message:%s", opRsp.Code, opRsp.Message)
	}
	return nil
}

func SetXMasterDefaultMonitorStrategy(ctx context.Context, appID string) error {
	var (
		mc  *xmaster.MonitorConfig
		err error
	)

	mc, err = GenXMasterClusterDefaultMonitorConfig(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, get monitor strategy fail", logit.Error("err", err))
		return err
	}

	if err = SetXMasterClusterMonitorStrategy(ctx, appID, mc); err != nil {
		return err
	}

	return nil
}

func SetXMasterClusterMonitorStrategy(ctx context.Context, clusterID string, mc *xmaster.MonitorConfig) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetMonitorStrategy(ctx, &xmaster.GetMonitorStrategyRequest{
		ClusterId: clusterID,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query monitor strategy", logit.String("clusterID", clusterID),
		logit.String("clusterID", clusterID),
		logit.AutoField("monitor strategy queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query monitor strategy fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, target cluster is not exist ", logit.String("clusterID", clusterID))
		return nil
	}

	if len(queryRsp.Rules) > 0 {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, target cluster has default monitor strategy already ", logit.String("clusterID", clusterID))
		return nil
	}

	opRsp, err := s.SetMonitorStrategy(ctx, &xmaster.SetMonitorStrategyRequest{
		ClusterId: clusterID,
		Rule:      mc,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set monitor fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}

	return nil
}

func GenXMasterClusterDefaultMonitorConfig(ctx context.Context) (*xmaster.MonitorConfig, error) {
	var (
		err            error
		defaultRuleStr = conf.XmasterHAConfIns.DefaultMonitorRules
		defaultRules   = make([]*xmaster.MonitorItem, 0)
	)
	if err = json.Unmarshal([]byte(defaultRuleStr), &defaultRules); len(defaultRuleStr) == 0 || err != nil {
		resource.LoggerTask.Warning(ctx, "fail go get xmaster default monitor rules from config, use default",
			logit.String("defaultRuleStr", defaultRuleStr), logit.Error("error", err))
		defaultRules = []*xmaster.MonitorItem{
			{
				Name:      "instance_alive",
				Cond:      "1==1",
				Expr:      "((time()-$lastActiveTime)>10) || ($instance_alive==0)", // NOTE: 经沟通，采用保守判断，监控项不存在时按健康处理
				Filter:    "repeat_time()>=2",
				MutexList: []*xmaster.MutexInfo{},
				Level:     1,
				Timeout:   300,
				Tag:       map[string]string{},
			},
		}
	}
	return &xmaster.MonitorConfig{Rules: defaultRules}, nil
}

func SetXMasterMonitorSwitch(ctx context.Context, appID string, enable bool) error {
	if err := SetXMasterClusterMonitorSwitch(ctx, appID, enable); err != nil {
		return err
	}

	return nil
}

func SetXMasterClusterMonitorSwitch(ctx context.Context, clusterID string, enable bool) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetMonitorSwitch(ctx, &xmaster.GetMonitorSwitchRequest{
		ClusterId: clusterID,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query monitor switch", logit.String("appId", clusterID),
		logit.String("clusterId", clusterID),
		logit.AutoField("monitor switch queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query monitor switch fail", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor switch, target cluster is not exist ", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID))
		return nil
	}

	opRsp, err := s.SetMonitorSwitch(ctx, &xmaster.SetMonitorSwitchRequest{
		ClusterId: clusterID,
		Enable:    enable,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor switch fail", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor switch fail", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set monitor switch fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}
	return nil
}

func QueryClusterTopoFromXmaster(ctx context.Context, clusterID string) (*xmaster.QueryClusterResponse, error) {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
		ClusterId: clusterID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster fail",
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return nil, err
	}
	return queryRsp, err
}

func SetXMasterTaskFakeSwitch(ctx context.Context, appID string, enable bool) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appID))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appID)
	}

	if err = SetXMasterClusterTaskFakeSwitch(ctx, appID, enable); err != nil {
		return err
	}

	return nil
}

func SetXMasterClusterTaskFakeSwitch(ctx context.Context, clusterID string, enable bool) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetTaskFakeSwitch(ctx, &xmaster.GetTaskFakeSwitchRequest{
		ClusterId: clusterID,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query task fake switch", logit.String("appId", clusterID),
		logit.String("clusterId", clusterID),
		logit.AutoField("task fake switch queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query task fake switch fail", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster set task fake switch, target cluster is not exist ",
			logit.String("appId", clusterID),
			logit.String("clusterId", clusterID))
		return nil
	}

	opRsp, err := s.SetTaskFakeSwitch(ctx, &xmaster.SetTaskFakeSwitchRequest{
		ClusterId: clusterID,
		Enable:    enable,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set task fake switch fail", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set task fake switch fail", logit.String("appId", clusterID),
			logit.String("clusterId", clusterID),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set task fake switch fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}
	return nil
}
