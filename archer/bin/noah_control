#!/bin/bash
TARGET_BIN="start.sh"
NOT_CHECK=0
RETRY_LIMIT=60
cd `dirname $0`
work_dir=$( dirname `pwd`)
bin_exe="${work_dir}/bin/${TARGET_BIN}"
pid_file="${work_dir}/x1-task.pid"
bin_exe_test="${work_dir}/${TARGET_BIN}.test"

echo "work_dir: ${work_dir}"
echo "bin_exe: ${bin_exe}"
echo "pid_file: ${pid_file}"

start()
{
    if [ -f "${pid_file}" ]; then
        pid=`cat ${pid_file} | head -n 1`
        if [ ! -d "/proc/${pid}" ]; then
            echo "x1-task.pid exist, pid: ${pid}. The process isn't running, remove pid file."
            rm -f ${pid_file}
        else
            if [ -h "/proc/${pid}/exe" ]; then
                pid_path=`readlink -f /proc/${pid}/exe`
                if [ "X${pid_path}" == "X/home/<USER>/vdb/x1-task-manager/bin/x1-task-manager" ]; then
                    echo "x1-task.pid exist, pid[${pid}]. The process[${pid_path}] is running!"
                    exit 2
                else
                    echo "x1-task.pid exist, pid[${pid}]. The process[${pid_path}] is running, but not x1-task-manager, remove pid file."
                    rm -f ${pid_file}
                fi
            fi
        fi
    fi

    mkdir -p ${work_dir}/log
    nohup ${bin_exe} </dev/null &>${work_dir}/x1-task.out & sleep 1

    if [[ ${NOT_CHECK} -eq 1 ]];then
        return 0
    fi

    ((retry_count=0))
    while [[ retry_count -lt ${RETRY_LIMIT} ]];do
        sleep 1
        if [ -f ${pid_file} ];then
            echo "Start successfully!"
            return 0
        fi
        ((retry_count=retry_count+1))
    done

    echo "Not found ${pid_file}. Start failed."
    exit 1
}

start_e2e_test()
{
    if [ -f "${pid_file}" ]; then
        pid=`cat ${pid_file} | head -n 1`
        if [ ! -d "/proc/${pid}" ]; then
            echo "x1-task.pid exist, pid: ${pid}. The process isn't running, remove pid file."
            rm -f ${pid_file}
        else
            if [ -h "/proc/${pid}/exe" ]; then
                pid_path=`readlink -f /proc/${pid}/exe`
                if [ "X${pid_path}" == "X/home/<USER>/vdb/x1-task-manager/x1-task" ]; then
                    echo "x1-task.pid exist, pid[${pid}]. The process[${pid_path}] is running!"
                    exit 2
                else
                    echo "x1-task.pid exist, pid[${pid}]. The process[${pid_path}] is running, but not x1-task, remove pid file."
                    rm -f ${pid_file}
                fi
            fi
        fi
    fi

    mkdir -p ${work_dir}/log
    #nohup ${bin_exe_test} </dev/null &>/dev/null & sleep 1
    nohup ${bin_exe_test} -test.coverprofile "${work_dir}/x1-task.test.coverage.cov" </dev/null &>${work_dir}/x1-task.out & sleep 1

    if [[ ${NOT_CHECK} -eq 1 ]];then
        return 0
    fi

    ((retry_count=0))
    while [[ retry_count -lt ${RETRY_LIMIT} ]];do
        sleep 1
        if [ -f ${pid_file} ];then
            echo "Start successfully!"
            return 0
        fi
        ((retry_count=retry_count+1))
    done

    echo "Not found ${pid_file}. Start failed."
    exit 1
}

clean()
{
    # check if process is running
    echo "" > ${work_dir}/x1-task.out
    rm -rf ${work_dir}/log/*
    echo "Clean successfully!"
}

stop()
{
    if [ -f ${pid_file} ];then
        kill `cat ${pid_file}`
    else
        echo "Not found ${pid_file}."
        return 0
    fi

    if [ -h "/proc/${pid}/exe" ]; then
        echo "Stop failed!"
        return -1
    else
        echo "Stop successfully!"
        rm ${pid_file}
        return 0
    fi
}

stop_e2e_test()
{
    if [ -f ${pid_file} ];then
        #kill `cat ${pid_file}`
        curl -s '127.0.0.1:9999/'
    else
        echo "Not found ${pid_file}."
        return 0
    fi

    if [ -h "/proc/${pid}/exe" ]; then
        echo "Stop failed!"
        return -1
    else
        echo "Stop successfully!"
        rm ${pid_file}
        return 0
    fi
}

showpid()
{
    if [ -f ${pid_file} ];then
        pid=`cat ${pid_file}`
        echo $pid
        return $pid
    else
        return
    fi
}

replace()
{
    src=${bin_exe}
    dest=${bin_exe}.new
    if [[ x$2 != x"" ]];then
        dest=${1}
    fi

    if [[ -f ${dest} ]];then
        stop
        if [[ $? != 0 ]];then
            echo "replicing failed"
            return -1
        fi
        mv ${src} ${src}.bak.$(date +"%Y%m%d%H%M%S")
        mv ${dest} ${src}
        start
        return
    else
        echo "Can not found ${dest} binary file for update"
        return
    fi
}

status(){
    process_num=`ps aux | grep ${bin_exe}|grep -v grep| wc -l`;
    if [ "${process_num}" -gt "0" ]; then
        echo "x1-task-manager still running"
        return 0
    else
        return 2
    fi
}
if [[ x$2 == x"--not_check" ]];then
    NOT_CHECK=1
fi

case C"$1" in
    C)
        start
        ;;
    Cstart)
        start
        ;;
    Cstart_e2e_test)
        start_e2e_test
        ;;
    Cstop)
        stop
        ;;
    Cstop_e2e_test)
        stop_e2e_test
        ;;
    Crestart)
        stop
        sleep 2
        start
        ;;
    Cpid)
        showpid
        ;;
    Creplace)
        replace
        ;;
    Cclean)
       clean
        ;;
    Cstatus)
      status
        ;;
    C*)
        echo "Usage: $0 {start|stop|restart|clean|status|pid]}"
        ;;
esac
