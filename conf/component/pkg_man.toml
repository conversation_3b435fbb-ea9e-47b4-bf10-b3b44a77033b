BosAK = "ALTAKgXkaYMoheChVpauc3LGtO"
BosSK = "294cdd14473343b5a6fbed7956e04d88"

[IDC_SPEC.onlinebd]
BosEndpoint = "bd.bcebos.com"
BosBucket = "vdb-package-manager-onlinebd"
[IDC_SPEC.onlinebj]
BosEndpoint = "bj.bcebos.com"
BosBucket = "vdb-package-manager-onlinebj"
[IDC_SPEC.onlinefsh]
BosEndpoint = "fsh.bcebos.com"
BosBucket = "vdb-package-manager-onlinefsh"
[IDC_SPEC.onlinegz]
BosEndpoint = "gz.bcebos.com"
BosBucket = "vdb-package-manager-onlinegz"
[IDC_SPEC.onlinehkg]
BosEndpoint = "hkg.bcebos.com"
BosBucket = "vdb-package-manager-onlinehkg"
[IDC_SPEC.onlinesu]
BosEndpoint = "su.bcebos.com"
BosBucket = "vdb-package-manager-onlinesu"
[IDC_SPEC.onlinewh]
BosEndpoint = "fwh.bcebos.com"
BosBucket = "vdb-package-manager-onlinewh"
[IDC_SPEC.onlineyq]
BosEndpoint = "yq.bcebos.com"
BosBucket = "vdb-package-manager-onlineyq"
[IDC_SPEC.onlinecd]
BosEndpoint = "cd.bcebos.com"
BosBucket = "vdb-package-manager-onlinecd"
[IDC_SPEC.onlinenj]
BosEndpoint = "nj.bcebos.com"
BosBucket = "vdb-package-manager-onlinenj"
[IDC_SPEC.bdtest]
BosEndpoint = "bd.bcebos.com"
BosBucket = "vdb-package-manager-bdtest"
[IDC_SPEC.dbstackadaptorbcc]
BosEndpoint = "http://127.0.0.1:8060/vdb/package"
BosBucket = "fake-bucket"
