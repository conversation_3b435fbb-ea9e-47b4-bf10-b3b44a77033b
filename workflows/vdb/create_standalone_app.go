/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* create_standalone_app.go - the processes of create standalone instance  */
/*
modification history
--------------------
2024/02/23, by <PERSON>(fang<PERSON><PERSON>@baidu.com), create
*/

/*
DESCRIPTION
创建VDB单机版实例WORKFLOW

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"Name": "vdb-kx42u7p4",
	"Type": "standalone",
	"UserID": "680cdfe705434d53b13a156284ed973b",
	"Port": 6379,
	"VpcID": "5c03c3e6-c38e-44e8-a50c-0648cff93faf"
	"AzInfos": [{
		"Zone": "zoneA",
		"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
	}],
	"NodeSpec": "cache.n1.small"
}
*/

package workflows

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/applyresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	checksubnets "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/check_subnets"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/delresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/deploy"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/dns"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/endpoint"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/pushflag"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/opmonitor"
	securitygroup "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/security_group"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/specification"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/tls"
)

const (
	WorkflowCreateStandalone                                          = "vdb-create-standalone-app"
	StepCreateStandaloneBuildMeta                                     = "vdb-create-standalone-app-build-meta"
	StepCreateStandaloneFillSpec                                      = "vdb-create-standalone-fill-spec"
	StepCreateStandaloneCheckSubnetsIpv6                              = "vdb-create-standalone-check-subnets-ipv6"
	StepCreateStandaloneCheckSubnetsEnoughIps                         = "vdb-create-standalone-check-subnets-enough-ips"
	StepCreateStandaloneStartInitBlbAndEndpoint                       = "vdb-create-standalone-start-init-blb-and-endpoint"
	StepCreateStandaloneCreateSecurityGroups                          = "vdb-create-standalone-create-security-groups"
	StepCreateStandaloneApplyResources                                = "vdb-create-standalone-apply-resources"
	StepCreateStandaloneApplyResourcesStartSubTasksAfterApplyResource = "vdb-create-standalone-apply-resources-start-sub-tasks-after-apply-resource"
	StepCreateStandaloneDeploy                                        = "vdb-create-standalone-apply-deploy"
	StepCreateStandaloneInitAppInXmaster                              = "vdb-create-standalone-init-app-in-xmaster"
	StepCreateStandaloneUpdatePushFlagAllTrue                         = "vdb-create-standalone-update-push-flag-all-true"
	StepCreateStandaloneInitStandalone                                = "vdb-create-standalone-init-standalone"
	StepCreateStandaloneCreateCallback                                = "vdb-create-standalone-create-cb"

	StepCreateStandaloneRollbackCallback             = "vdb-create-standalone-rollback-callback"
	StepCreateStandaloneRollbackDeleteAppFromXmaster = "vdb-create-standalone-rollback-delete-app-from-xmaster"
	StepCreateStandaloneRollbackDeleteDNS            = "vdb-create-standalone-rollback-delete-dns"
	StepCreateStandaloneRollbackDeleteEndpoint       = "vdb-create-standalone-rollback-delete-endpoint"
	StepCreateStandaloneRollbackDeleteBLB            = "vdb-create-standalone-rollback-delete-blb"
	StepCreateStandaloneRollbackReleaseResources     = "vdb-create-standalone-rollback-release-resource"

	WorkflowCreateStandaloneInitBlbAndEndpoint            = "vdb-create-standalone-init-blb-and-endpoint"
	StepCreateStandaloneInitBlbAndEndpointInitBLB         = "vdb-create-standalone-init-blb-and-endpoint-init-blb"
	StepCreateStandaloneInitBlbAndEndpointCreateEndpoint  = "vdb-create-standalone-init-blb-and-endpoint-create-endpoint"
	StepCreateStandaloneInitBlbAndEndpointCreateAppDomain = "vdb-create-standalone-init-blb-and-endpoint-create-app-domain"

	WorkflowCreateStandaloneUpdateConfig     = "vdb-create-standalone-update-config"
	StepCreateStandaloneUpdateConfigUpdateSg = "vdb-create-standalone-update-config-update-sg"

	WorkflowCreateStandaloneInitBlbRs  = "vdb-create-standalone-init-blb-rs"
	StepCreateStandaloneInitBlbRsSetRs = "vdb-create-standalone-init-blb-rs-set-rs"

	WorkflowCreateStandaloneInitOpMonitor              = "vdb-create-standalone-init-op-monitor"
	StepCreateStandaloneInitOpMonitorCreateBnsService  = "vdb-create-standalone-init-op-monitor-create-bns-service"
	StepCreateStandaloneInitOpMonitorCreateBnsInstance = "vdb-create-standalone-init-op-monitor-create-bns-instance"
)

func init() {
	// Step-01 初始化x1数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneBuildMeta,
		Workflow: WorkflowCreateStandalone,

		StepProcess: buildmeta.ProcessBuildMetaForCreatingStandalone,

		SuccessNextStep: StepCreateStandaloneFillSpec,
		ErrorNextStep:   StepCreateStandaloneBuildMeta,
	})

	// Step-02 计算规格并填入
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneFillSpec,
		Workflow: WorkflowCreateStandalone,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepCreateStandaloneCheckSubnetsIpv6,
		ErrorNextStep:   StepCreateStandaloneFillSpec,
	})

	// Step-03 检查子网是否为ipv6
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneCheckSubnetsIpv6,
		Workflow: WorkflowCreateStandalone,

		StepProcess: checksubnets.CheckIPV6,

		SuccessNextStep: StepCreateStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepCreateStandaloneCheckSubnetsIpv6,
	})

	// Step-04 检查子网中ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneCheckSubnetsEnoughIps,
		Workflow: WorkflowCreateStandalone,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepCreateStandaloneStartInitBlbAndEndpoint,
		ErrorNextStep:   StepCreateStandaloneCheckSubnetsEnoughIps,
	})

	// Step-04-1 加快创建流程，并行进行blb和endpoint的创建
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneStartInitBlbAndEndpoint,
		Workflow: WorkflowCreateStandalone,

		StepProcess: ProcessCreateStandaloneStartWorkflowInitBlbAndEndpoint,

		SuccessNextStep: StepCreateStandaloneCreateSecurityGroups,
		ErrorNextStep:   StepCreateStandaloneStartInitBlbAndEndpoint,
	})

	// Step-05 创建安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneCreateSecurityGroups,
		Workflow: WorkflowCreateStandalone,

		StepProcess: securitygroup.ProcessInitSecurityGroupStandalone,

		SuccessNextStep: StepCreateStandaloneApplyResources,
		ErrorNextStep:   StepCreateStandaloneCreateSecurityGroups,
	})

	// 创建证书
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateOrUpdateCertificate,
		Workflow: WorkflowCreateCluster,

		StepProcess: tls.ProcessCreateOrUpdateCertificate,

		SuccessNextStep: StepCreateClusterApplyResources,
		ErrorNextStep:   StepCreateOrUpdateCertificate,
	})

	// Step-06 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneApplyResources,
		Workflow: WorkflowCreateStandalone,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepCreateStandaloneApplyResourcesStartSubTasksAfterApplyResource,
		ErrorNextStep:   StepCreateStandaloneApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneApplyResourcesStartSubTasksAfterApplyResource,
		Workflow: WorkflowCreateStandalone,

		StepProcess: ProcessCreateStandaloneStartWorkflowsAfterApplyResources,

		SuccessNextStep: StepCreateStandaloneDeploy,
		ErrorNextStep:   StepCreateStandaloneApplyResourcesStartSubTasksAfterApplyResource,
	})

	// Step-07 部署standalone节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneDeploy,
		Workflow: WorkflowCreateStandalone,

		StepProcess: deploy.ProcessDeployAllForNewCreate,

		SuccessNextStep: StepCreateStandaloneInitAppInXmaster,
		ErrorNextStep:   StepCreateStandaloneDeploy},

		workflow.WithStepTimeout(15*time.Minute),
	)

	// Step-8 集群注册到xmaster：拓扑信息、监控策略、自愈开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneInitAppInXmaster,
		Workflow: WorkflowCreateStandalone,

		StepProcess: xmaster.ProcessInitAppInXmaster,

		SuccessNextStep: StepCreateStandaloneUpdatePushFlagAllTrue,
		ErrorNextStep:   StepCreateStandaloneInitAppInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneUpdatePushFlagAllTrue,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     pushflag.ProcessUpdatePushFlagAllTrue,
		SuccessNextStep: StepCreateStandaloneInitStandalone,
		ErrorNextStep:   StepCreateStandaloneUpdatePushFlagAllTrue,
	})

	// Step-9 初始化集群:设置root密码
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitStandalone,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     mochow.ProcessInitStandalone,
		SuccessNextStep: StepCreateStandaloneCreateCallback,
		ErrorNextStep:   StepCreateStandaloneInitStandalone,
	})

	// Step-10 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneCreateCallback,
		Workflow: WorkflowCreateStandalone,

		StepProcess: callback.ProcessCreateSuccessCb,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneCreateCallback,
	})

	// Step-Error-01 创建失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneRollbackCallback,
		Workflow: WorkflowCreateStandalone,

		StepProcess: callback.ProcessCreateErrorCb,

		SuccessNextStep: StepCreateStandaloneRollbackDeleteAppFromXmaster,
		ErrorNextStep:   StepCreateStandaloneRollbackCallback,
	})

	// Step-Error-02 创建失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneRollbackDeleteAppFromXmaster,
		Workflow: WorkflowCreateStandalone,

		StepProcess: xmaster.ProcessDeleteAppFromXmaster,

		SuccessNextStep: StepCreateStandaloneRollbackDeleteDNS,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteAppFromXmaster,
	})

	// 删除 Dns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackDeleteDNS,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     dns.ProcessDeleteAppDomain,
		SuccessNextStep: StepCreateStandaloneRollbackDeleteEndpoint,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteDNS},

		workflow.WithMaxReentry(2, StepCreateStandaloneRollbackDeleteEndpoint))

	// Step-Error 回滚 Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneRollbackDeleteEndpoint,
		Workflow: WorkflowCreateStandalone,

		StepProcess: endpoint.ProcessDeleteRwEndpoint,

		SuccessNextStep: StepCreateStandaloneRollbackDeleteBLB,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteEndpoint,
	})

	// Step-Error-03 回滚资源申请
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneRollbackDeleteBLB,
		Workflow: WorkflowCreateStandalone,

		StepProcess: blb.ProcessDelBLB,

		SuccessNextStep: StepCreateStandaloneRollbackReleaseResources,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteBLB,
	})

	// Step-Error-04 回滚BLB资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneRollbackReleaseResources,
		Workflow: WorkflowCreateStandalone,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepCreateStandaloneRollbackReleaseResources,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitBlbAndEndpointInitBLB,
		Workflow:        WorkflowCreateStandaloneInitBlbAndEndpoint,
		StepProcess:     blb.ProcessInitAppBLB,
		SuccessNextStep: StepCreateStandaloneInitBlbAndEndpointCreateEndpoint,
		ErrorNextStep:   StepCreateStandaloneInitBlbAndEndpointInitBLB,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitBlbAndEndpointCreateEndpoint,
		Workflow:        WorkflowCreateStandaloneInitBlbAndEndpoint,
		StepProcess:     endpoint.ProcessCreateRwEndpoint,
		SuccessNextStep: StepCreateStandaloneInitBlbAndEndpointCreateAppDomain,
		ErrorNextStep:   StepCreateStandaloneInitBlbAndEndpointCreateEndpoint,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitBlbAndEndpointCreateAppDomain,
		Workflow:        WorkflowCreateStandaloneInitBlbAndEndpoint,
		StepProcess:     dns.ProcessCreateAppDomain,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneInitBlbAndEndpointCreateAppDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitBlbRsSetRs,
		Workflow:        WorkflowCreateStandaloneInitBlbRs,
		StepProcess:     blb.ProcessSetStandaloneRs,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneInitBlbRsSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitOpMonitorCreateBnsService,
		Workflow:        WorkflowCreateStandaloneInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorBnsService,
		SuccessNextStep: StepCreateStandaloneInitOpMonitorCreateBnsInstance,
		ErrorNextStep:   StepCreateStandaloneInitOpMonitorCreateBnsService,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitOpMonitorCreateBnsInstance,
		Workflow:        WorkflowCreateStandaloneInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneInitOpMonitorCreateBnsInstance,
	})

}

func ProcessCreateStandaloneStartWorkflowInitBlbAndEndpoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateStandaloneInitBlbAndEndpoint,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_and_endpoint",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}})
}

func ProcessCreateStandaloneStartWorkflowsAfterApplyResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateStandaloneInitBlbRs,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_rs",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}}

	if !privatecloud.IsPrivateENV() {
		params = append(params, &iface.CreateTaskParams{
			WorkFlow:   WorkflowCreateStandaloneInitOpMonitor,
			Schedule:   time.Now(),
			Mutex:      teu.Entity + "_init_op_monitor",
			Entity:     teu.Entity,
			Parameters: teu.Parameters,
		})
	}

	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, params)
}
