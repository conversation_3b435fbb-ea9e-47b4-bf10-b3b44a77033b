[ParentConfig]
#Path = "BAIDU_DBA_DBA-BDRP_redis_raoalong-dev-test"
#Token = "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN"
Path="BAIDU_INF_BCE_SCS_SCS-service-online"
Token="Tp9uLlkoIKaEjJOKMTAf4ghcJ23qx2rF"

[GroupConfig]
DeleteLimit = false
ConfIsJson = 1
ConstrainGroup = 0
OpenDeadhostCheck = 1

[InstanceConfig]
DefaultStatus = 999
DefaultRunUser = "work"

[ServiceConfig]
UseDynamicNode = false

[[IDCMap]]
OriginalIDC = "sin"
TargetIDC = "sin01"

[[IDCMap]]
OriginalIDC = "preonline"
TargetIDC = "bj"

[[IDCMap]]
OriginalIDC = "fsh"
TargetIDC = "sh"

[[IDCMap]]
OriginalIDC = "su"
TargetIDC = "sz"

[bns]
    Product = "vdb"
