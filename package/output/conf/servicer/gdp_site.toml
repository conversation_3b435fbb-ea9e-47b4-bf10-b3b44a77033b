# Service的名字，必选
Name = "gdp_site"

# 配置说明： http://gdp.baidu-int.com/gdp2/docs/examples/client/30_servicer/

## 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 连接超时
ConnTimeOut = 145
# 写数据超时
WriteTimeOut = 750
# 读数据超时
ReadTimeOut = 750
# 请求失败后的重试次数：总请求次数 = Retry + 1
Retry = 2

# HTTP 协议专有
[Headers]
Host = "gdp.baidu-int.com"

# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin: 依次轮询
[Strategy] 
Name="RoundRobin"
# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"

# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
[[Resource.Manual.default]]
Host = "gdp.baidu-int.com"
Port = 80
