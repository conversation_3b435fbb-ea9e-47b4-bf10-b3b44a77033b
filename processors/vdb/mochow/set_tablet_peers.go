/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func SetDataNodeTabletPeers(ctx context.Context, node *vdbmodel.Node, pTabletID mochow.PTabletID,
	TabletPeers []mochow.RaftPeerID) error {
	req := mochow.SetTabletPeersRequest{
		Token:       "default_token",
		TabletID:    pTabletID,
		TabletPeers: TabletPeers,
	}
	rsp, err := mochow.SetTabletPeers(ctx, node, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "set tablet peers failed", logit.Error("error", err))
		return err
	}

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "set tablet peers failed", logit.Int32("status_code", rsp.Status.Code))
		return fmt.Errorf(rsp.Status.Msg)
	}

	resource.LoggerTask.Trace(ctx, "set tablet peers success", logit.String("pTabletID", base_utils.Format(pTabletID)),
		logit.String("tabletPeers", base_utils.Format(TabletPeers)))

	return nil
}

func GetDBInfos(ctx context.Context, master *vdbmodel.Master) ([]mochow.PDatabase, error) {
	req := mochow.ListDatabaseInternalRequest{
		Token: "default_token",
	}
	rsp, err := mochow.ListDatabaseInternal(ctx, master, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list database internal failed", logit.Error("error", err))
		return nil, err
	}

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list database internal failed", logit.Int32("status_code", rsp.Status.Code))
		return nil, fmt.Errorf(rsp.Status.Msg)
	}

	resource.LoggerTask.Trace(ctx, "list database internal success", logit.String("dbInfos", base_utils.Format(rsp.DBInfos)))

	return rsp.DBInfos, nil
}

func GetDBTableIDs(ctx context.Context, master *vdbmodel.Master, dbName string) ([]uint64, error) {
	req := mochow.ListTableInternalRequest{
		Token:  "default_token",
		DBName: dbName,
	}
	rsp, err := mochow.ListTableInternal(ctx, master, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list table internal failed", logit.Error("error", err))
		return nil, err
	}

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list table internal failed", logit.Int32("status_code", rsp.Status.Code))
		return nil, fmt.Errorf(rsp.Status.Msg)
	}

	resource.LoggerTask.Trace(ctx, "list table internal success", logit.String("dbInfos", base_utils.Format(rsp.TableInfos)))

	TableIDs := make([]uint64, 0)
	for _, tableInfo := range rsp.TableInfos {
		TableIDs = append(TableIDs, tableInfo.Schema.TableID)
	}

	return TableIDs, nil
}

func getNodeFromReplica(app *vdbmodel.Application, replica *mochow.Replica) (*vdbmodel.Node, error) {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if int32(util.LittleEndianInetAtoN(node.IP)) == replica.NodeAddr.IP {
				return node, nil
			}
		}
	}

	return nil, cerrs.ErrInvalidParams
}

func getRaftPeersFromTabletInfo(tabletInfo *mochow.TabletInfo) []mochow.RaftPeerID {
	raftPeerIDs := make([]mochow.RaftPeerID, 0)

	if tabletInfo.Leader.State == "NORMAL" {
		raftPeerIDs = append(raftPeerIDs, mochow.RaftPeerID{
			//Addr:  tabletInfo.Leader.NodeAddr,
			Addr: mochow.NodeID{
				IP:   tabletInfo.Leader.NodeAddr.IP,
				Port: tabletInfo.Leader.NodeAddr.Port + 3,
			},
			Index: tabletInfo.Leader.RelicaIndex,
		})
	}

	for _, follower := range tabletInfo.Followers {
		raftPeerIDs = append(raftPeerIDs, mochow.RaftPeerID{
			//Addr:  follower.NodeAddr,
			Addr: mochow.NodeID{
				IP:   follower.NodeAddr.IP,
				Port: follower.NodeAddr.Port + 3,
			},
			Index: follower.RelicaIndex,
		})
	}

	return raftPeerIDs
}

func SetOneTabletPeers(ctx context.Context, app *vdbmodel.Application, master *vdbmodel.Master,
	dbID uint64, tableID uint64) error {
	req := mochow.ListTabletRequest{
		Token:   "default_token",
		TableID: tableID,
	}
	rsp, err := mochow.ListTablet(ctx, master, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list  tablet failed", logit.Uint64("table_id", tableID), logit.Error("error", err))
		return err
	}

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list tablet failed", logit.Uint64("table_id", tableID),
			logit.Int32("status_code", rsp.Status.Code))
		return fmt.Errorf(rsp.Status.Msg)
	}

	resource.LoggerTask.Trace(ctx, "list tablet success", logit.String("dbInfos", base_utils.Format(rsp.Tablets)))

	for _, tablet := range rsp.Tablets {
		TPID := tablet.TPID
		tabletID := mochow.PTabletID{
			DBID:    dbID,
			TableID: tableID,
			TPID:    TPID,
		}

		toSetNodes := make([]*vdbmodel.Node, 0)

		if tablet.Leader.State == "NORMAL" {
			leaderNode, err := getNodeFromReplica(app, &tablet.Leader)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get node from replica failed", logit.String("replica", base_utils.Format(tablet.Leader)),
					logit.String("error", err.Error()))
				return err
			}

			toSetNodes = append(toSetNodes, leaderNode)
		}

		for _, follower := range tablet.Followers {
			node, err := getNodeFromReplica(app, &follower)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get node from replica failed", logit.String("replica", base_utils.Format(follower)),
					logit.String("error", err.Error()))
				return err
			}
			toSetNodes = append(toSetNodes, node)
		}

		if len(toSetNodes) == 0 {
			resource.LoggerTask.Trace(ctx, "no node to set", logit.String("dbID", base_utils.Format(dbID)),
				logit.String("tableID", base_utils.Format(tableID)))
			return nil
		}

		raftPeerIDs := getRaftPeersFromTabletInfo(&tablet)

		resource.LoggerTask.Trace(ctx, "set tablet peers", logit.String("dbID", base_utils.Format(dbID)),
			logit.String("tablet_id", base_utils.Format(tablet)), logit.String("raft_peers", base_utils.Format(raftPeerIDs)),
			logit.String("node_ids", base_utils.Format(toSetNodes)))

		for _, node := range toSetNodes {
			err = SetDataNodeTabletPeers(ctx, node, tabletID, raftPeerIDs)
			if err != nil {
				resource.LoggerTask.Error(ctx, "set tablet peers failed", logit.String("node_id", node.NodeID),
					logit.String("error", err.Error()), logit.String("tablet_id", base_utils.Format(tablet)),
					logit.String("raft_peers", base_utils.Format(raftPeerIDs)))
				return err
			}
			resource.LoggerTask.Trace(ctx, "set tablet peers success", logit.String("dbID", base_utils.Format(dbID)),
				logit.String("tablet_id", base_utils.Format(tablet)), logit.String("raft_peers", base_utils.Format(raftPeerIDs)),
				logit.String("node_id", node.NodeID))
		}
	}

	return nil
}

func SetAppTabletPeers(ctx context.Context, app *vdbmodel.Application) error {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	dbInfos, err := GetDBInfos(ctx, masterServer)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get db infos failed", logit.String("error", err.Error()))
		return err
	}

	for _, dbInfo := range dbInfos {
		tableIDs, err := GetDBTableIDs(ctx, masterServer, dbInfo.DBName)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get table ids failed", logit.String("db_id", base_utils.Format(dbInfo.DBID)),
				logit.String("error", err.Error()))
			return err
		}
		for _, tableID := range tableIDs {
			err = SetOneTabletPeers(ctx, app, masterServer, dbInfo.DBID, tableID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "set one tablet peers failed", logit.Uint64("db_id", dbInfo.DBID),
					logit.Uint64("table_id", tableID), logit.String("error", err.Error()))
				return err
			}
		}
	}
	return nil
}
