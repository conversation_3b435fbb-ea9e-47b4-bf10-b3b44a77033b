APPNAME = x1-task-manager
#初始化项目目录变量
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output
GOPKGS  := $$(go list ./...| grep -vE "vendor")
GOMOD   := go mod
GOBUILD := CGO_ENABLED=0 go build

# 设置编译时所需要的 Go 环境
export GOENV = $(HOMEDIR)/go.env

#执行编译，可使用命令 make 或 make all 执行， 顺序执行 prepare -> compile -> test -> package 几个阶段
all: prepare compile test package

# prepare阶段， 使用 bcloud 下载非 Go 依赖，可单独执行命令: make prepare
prepare:
	go env          # 打印出 go 环境信息，可用于排查问题
	$(GOMOD) tidy
	$(GOMOD) download || $(GOMOD) mod download -x # 下载 Go 依赖

# complile 阶段，执行编译命令，可单独执行命令: make compile
compile:build
build: prepare
	$(GOBUILD) -o $(HOMEDIR)/bin/$(APPNAME)

# test 阶段，进行单元测试， 可单独执行命令: make test
# cover 平台会优先执行此命令
test: test-case
test-case: prepare
	go test -race -timeout=120s -v -cover $(GOPKGS) -coverprofile=coverage.out | tee unittest.txt

# package 阶段，对编译产出进行打包，输出到 output 目录， 可单独执行命令: make package
package: package-bin
package-bin:
	$(shell rm -rf $(OUTDIR))
	$(shell mkdir -p $(OUTDIR))
	$(shell cp -a bin $(OUTDIR)/bin)
	$(shell cp -a conf $(OUTDIR)/conf)
	$(shell cp -a hestia $(OUTDIR)/hestia)
	$(shell cp -a archer/bin/noah_control $(OUTDIR)/bin)
	$(shell cp -a archer/noahdes $(OUTDIR)/noahdes)
	tree $(OUTDIR)


# clean 阶段，清除过程中的输出， 可单独执行命令: make clean
clean:
	rm -rf $(OUTDIR)

# avoid filename conflict and speed up build
.PHONY: all prepare compile test package  clean build
