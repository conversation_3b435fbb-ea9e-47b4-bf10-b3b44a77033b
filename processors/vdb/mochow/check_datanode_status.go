/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessCheckDataNodeStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Notice(ctx, "only check datanode status in cluster mode")
		return nil
	}

	for {
		complete := true
		if err := checkDataNodeCanDropAndNotAlive(ctx, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "datanode status check failed", logit.Error("err", err))
		}
		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			continue
		}
	}
}

/*
真正开始自愈任务前，去master查询节点是否可以删除；从master中查询节点is_alive是false，确保节点已经被master判死
*/
func checkDataNodeCanDropAndNotAlive(ctx context.Context, app *vdbmodel.Application) error {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	//对所有的准备drop的datanode节点进行检查
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			resource.LoggerTask.Trace(ctx, "check datanode", logit.String("node :", base_utils.Format(node)))
			if node.Status != vdbmodel.NodeOrProxyStatusToDelete && node.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}

			/*先检查master是否允许删除*/
			req := mochow.CheckDropDataNodeRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
			}
			rsp, err := mochow.CheckDropDataNode(ctx, masterServer, &req)
			if err != nil {
				resource.LoggerTask.Error(ctx, "check data node failed", logit.Error("error", err))
				return err
			}

			resource.LoggerTask.Trace(ctx, "check drop datanode", logit.String("rsp :", base_utils.Format(rsp)))

			if rsp.Status.Code != 0 {
				resource.LoggerTask.Error(ctx, "cannot drop data node", logit.Int32("status_code", rsp.Status.Code))
				return fmt.Errorf(rsp.Status.Msg)
			}

			/*再检查datanode是否已经被master判定为下线*/
			showReq := mochow.ShowDataNodeRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
			}

			showRsp, err := mochow.ShowDataNode(ctx, masterServer, &showReq)
			if err != nil {
				resource.LoggerTask.Error(ctx, "list data node failed", logit.Error("error", err))
				return err
			}

			resource.LoggerTask.Trace(ctx, "list datanode", logit.String("rsp :", base_utils.Format(showRsp)))

			//多az场景，内核会自动删除datanode，所以这里需要判断一下状态码是否为8051
			if showRsp.Status.Code == 8051 {
				resource.LoggerTask.Error(ctx, "data node is not found", logit.String("node_info", base_utils.Format(showRsp)))
				return nil
			}

			if showRsp.Status.Code != 0 {
				resource.LoggerTask.Error(ctx, "show data node failed", logit.Int32("status_code", rsp.Status.Code))
				return fmt.Errorf(rsp.Status.Msg)
			}

			if showRsp.NodeInfo.IsAlive {
				resource.LoggerTask.Error(ctx, "data node is alive", logit.String("node_info", base_utils.Format(showRsp)))
				return fmt.Errorf("cannot drop data node")
			}
		}
	}

	return nil
}

func ProcessCheckDataNodeCanBeShrink(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Notice(ctx, "only check datanode status in cluster mode")
		return nil
	}

	for {
		complete := true
		if err := checkDataNodeCanDropAndTabletPerfect(ctx, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "datanode status check failed", logit.Error("err", err))
		}
		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			continue
		}
	}
}

func checkDataNodeCanDropAndTabletPerfect(ctx context.Context, app *vdbmodel.Application) error {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	//对所有的准备drop的datanode节点进行检查
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			resource.LoggerTask.Trace(ctx, "check datanode", logit.String("node :", base_utils.Format(node)))
			if node.Status != vdbmodel.NodeOrProxyStatusToDelete && node.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}

			/*先检查datanoe是否允许drop*/
			req := mochow.CheckDropDataNodeRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
			}
			rsp, err := mochow.CheckDropDataNode(ctx, masterServer, &req)
			if err != nil {
				resource.LoggerTask.Error(ctx, "check data node failed", logit.Error("error", err))
				return err
			}

			resource.LoggerTask.Trace(ctx, "check drop datanode", logit.String("rsp :", base_utils.Format(rsp)))

			if rsp.Status.Code != 0 {
				resource.LoggerTask.Error(ctx, "cannot drop data node", logit.Int32("status_code", rsp.Status.Code))
				return fmt.Errorf(rsp.Status.Msg)
			}

			/*再检查tablet是否正常*/
			checkreq := mochow.CheckDataNodeTabletPerfectionRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
				NeedCheckNodeCount: true,
			}
			checkrsp, err := mochow.CheckDataNodeTabletPerfection(ctx, masterServer, &checkreq)
			if err != nil {
				resource.LoggerTask.Error(ctx, "check data node tablet failed", logit.Error("error", err),
					logit.String("node_id :", base_utils.Format(node.NodeID)))
				return err
			}

			resource.LoggerTask.Trace(ctx, "check drop datanode tablet", logit.String("rsp :", base_utils.Format(checkrsp)))

			if checkrsp.Status.Code != 0 {
				resource.LoggerTask.Error(ctx, "datanode tablet is not perfect", logit.Int32("status_code", checkrsp.Status.Code),
					logit.String("node_id :", base_utils.Format(node.NodeID)))
				return fmt.Errorf(checkrsp.Status.Msg)
			}
		}
	}

	return nil
}
