/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func SetMasterPeers(ctx context.Context, master *vdbmodel.Master) error {
	var MasterAddrs []mochow.NodeID
	MasterAddrs = append(MasterAddrs, mochow.NodeID{
		IP:   int32(util.LittleEndianInetAtoN(master.IP)),
		Port: int32(master.Port),
	})
	req := mochow.SetMasterPeerRequest{
		Token:       "default_token",
		MasterAddrs: MasterAddrs,
	}

	rsp, err := mochow.SetMasterPeer(ctx, master, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "set master peer failed", logit.String("MasterAddrs", base_utils.Format(MasterAddrs)),
			logit.Error("error", err))
		return err
	}

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "set master peer failed", logit.String("MasterAddrs", base_utils.Format(MasterAddrs)),
			logit.Int32("status_code", rsp.Status.Code))
		return fmt.Errorf(rsp.Status.Msg)
	}

	resource.LoggerTask.Trace(ctx, "set master peer success", logit.String("MasterAddrs", base_utils.Format(MasterAddrs)))

	return nil
}

func AddMasterPeers(ctx context.Context, master *vdbmodel.Master, IP string, port int) error {
	req := mochow.AddMasterPeerRequest{
		Token: "default_token",
		MasterAddr: mochow.NodeID{
			IP:   int32(util.LittleEndianInetAtoN(IP)),
			Port: int32(port),
		},
	}

	rsp, err := mochow.AddMasterPeer(ctx, master, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "add master peer failed", logit.String("IP", base_utils.Format(master.IP)),
			logit.Error("error", err))
		return err
	}

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "add master peer failed", logit.String("IP", base_utils.Format(master.IP)),
			logit.Int32("status_code", rsp.Status.Code))
		return fmt.Errorf(rsp.Status.Msg)
	}

	resource.LoggerTask.Trace(ctx, "add master peer success", logit.String("IP", base_utils.Format(master.IP)))

	return nil
}
