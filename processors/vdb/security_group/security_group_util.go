/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建虚机安全组、初始化安全组规则
*/

package securitygroup

import (
	"context"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func GetSecurityGrouptInnerPorts(ctx context.Context, app *vdbmodel.Application) []int32 {
	var ports []int32
	/*
		proxy 服务端口
		master rpc http
		datanode 控制面rpc 数据面rpc http server raft server
	*/
	for _, cluster := range app.Clusters {
		ports = append(ports, cast.ToInt32(cluster.Port), cast.ToInt32(cluster.Port+1),
			cast.ToInt32(cluster.Port+2), cast.ToInt32(cluster.Port+3))
		break
	}
	for _, itf := range app.Interfaces {
		ports = append(ports, cast.ToInt32(itf.Port))
		break
	}

	for _, master := range app.Masters {
		ports = append(ports, cast.ToInt32(master.Port), cast.ToInt32(master.Port+1))
		break
	}

	resource.LoggerTask.Trace(ctx, "GetSecurityGrouptInnerPorts", logit.String("ports", base_utils.Format(ports)))

	return ports
}
