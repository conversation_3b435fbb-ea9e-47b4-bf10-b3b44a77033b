package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/backup"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
)

const (
	WorkflowClusterManualBackup              = "vdb-cluster-manual-backup"
	StepManualBackupCheckAndUpdateBackupTask = "vdb-manual-backup-check-and-update-backup-task"
	StepManualBackupCb                       = "vdb-manual-backup-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualBackupCheckAndUpdateBackupTask,
		Workflow:        WorkflowClusterManualBackup,
		StepProcess:     backup.ProcessCheckAndUpdataManualBackupTask,
		SuccessNextStep: StepManualBackupCb,
		ErrorNextStep:   StepManualBackupCheckAndUpdateBackupTask},
		// 备份任务最大执行时间 10h
		workflow.WithStepTimeout(10*60*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualBackupCb,
		Workflow:        WorkflowClusterManualBackup,
		StepProcess:     callback.ProcessBackupSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepManualBackupCb,
	})
}
