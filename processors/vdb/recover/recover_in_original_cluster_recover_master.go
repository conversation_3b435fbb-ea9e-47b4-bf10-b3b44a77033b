/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/02 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	apisdkiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

const (
	MasterNodeWorkDir = "/home/<USER>/mochow-master"
)

type restartXagentRequest struct {
	WorkDir string `json:"work_dir"`
}

func GetOriginalMasterIDs(ctx context.Context, parameters string, appID string) (string, []string, error) {
	param, err := iface.GetParameters(ctx, parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return "", nil, cerrs.ErrInvalidParams
	}

	resource.LoggerTask.Trace(ctx, "recover get master ids params",
		logit.String("params :", base_utils.Format(param)),
		logit.String("appID", base_utils.Format(appID)))

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return "", nil, cerrs.ErrDbQueryFail.Wrap(err)
	}

	var masterIDHasSnapshot string
	masterCount := 0
	for _, item := range param.RevoverParam.NodeRecoverItems {
		engine := util.GetEngineByID(ctx, app, item.RecoverNodeID)
		if engine == "" {
			resource.LoggerTask.Error(ctx, "engine get by id failed", logit.String("nodeID", item.RecoverNodeID))
			return "", nil, cerrs.ErrInvalidParams
		}

		if engine == vdbmodel.EngineVDBMaster {
			masterIDHasSnapshot = item.RecoverNodeID
			masterCount++
		}
	}

	if masterCount != 1 {
		resource.LoggerTask.Error(ctx, "only one master should have snapshot", logit.Int("count", masterCount))
		return "", nil, cerrs.ErrInvalidParams
	}

	var followerIDs []string
	for _, master := range app.Masters {
		if master.MasterID == masterIDHasSnapshot {
			continue
		}

		followerIDs = append(followerIDs, master.MasterID)
	}

	resource.LoggerTask.Trace(ctx, "recover get master ids", logit.String("masterIDHasSnapshot", base_utils.Format(masterIDHasSnapshot)),
		logit.String("followerIDs", base_utils.Format(followerIDs)))

	return masterIDHasSnapshot, followerIDs, nil
}

func doRestartMasterNode(ctx context.Context, master *vdbmodel.Master) error {
	mochowDir := MasterNodeWorkDir

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: master.FloatingIP,
			Port: int32(master.XagentPort),
		},
		Action: "restart",
		Params: &restartXagentRequest{
			WorkDir: mochowDir,
		},
		Product: vdbmodel.ProductVDB,
	}

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "restart mochow-master fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
			logit.Error("err", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "restart mochow-master success", logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)))

	return nil
}

func restartMasterNode(ctx context.Context, app *vdbmodel.Application, masterID string) error {
	for _, master := range app.Masters {
		if master.MasterID == masterID {
			err := doRestartMasterNode(ctx, master)
			if err != nil {
				resource.LoggerTask.Error(ctx, "restart mochow-master fail", logit.String("nodeID", masterID),
					logit.Error("err", err))
				return err
			}

			resource.LoggerTask.Notice(ctx, "restart mochow-master success", logit.String("nodeID", masterID))
			return nil
		}
	}

	return cerrs.ErrInvalidParams
}

func setMasterNodeSingleMode(ctx context.Context, app *vdbmodel.Application, masterID string) error {
	for _, master := range app.Masters {
		if master.MasterID == masterID {
			err := mochow.SetMasterPeers(ctx, master)
			if err != nil {
				resource.LoggerTask.Error(ctx, "set master node single mode fail", logit.String("nodeID", masterID),
					logit.Error("err", err))
				return err
			}

			resource.LoggerTask.Notice(ctx, "set master node single mode success", logit.String("nodeID", masterID))
			return nil
		}
	}

	return cerrs.ErrInvalidParams
}

func addFollowerMasterPeer(ctx context.Context, app *vdbmodel.Application, masterID string, followerID string) error {
	var masterServer *vdbmodel.Master
	var followerServer *vdbmodel.Master
	for _, master := range app.Masters {
		if master.MasterID == masterID {
			masterServer = master
		} else if master.MasterID == followerID {
			followerServer = master
		}
	}

	if masterServer == nil || followerServer == nil {
		return cerrs.ErrInvalidParams
	}

	err := mochow.AddMasterPeers(ctx, masterServer, followerServer.IP, followerServer.Port)
	if err != nil {
		resource.LoggerTask.Error(ctx, "set master node single mode fail", logit.String("nodeID", masterID),
			logit.Error("err", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "set master node single mode success", logit.String("nodeID", masterID))
	return nil
}

func relocateDatanodeAddr(ctx context.Context, app *vdbmodel.Application, masterID string, RevoverParam *apisdkiface.RecoverParam) error {
	for _, master := range app.Masters {
		if master.MasterID == masterID {
			err := mochow.RelocateDatanodeAddr(ctx, master, RevoverParam)
			if err != nil {
				resource.LoggerTask.Error(ctx, "relocate datanode addr fail", logit.String("nodeID", masterID),
					logit.Error("err", err))
				return err
			}

			resource.LoggerTask.Notice(ctx, "relocate datanode addr success", logit.String("nodeID", masterID))
			return nil
		}
	}

	return cerrs.ErrInvalidParams
}
