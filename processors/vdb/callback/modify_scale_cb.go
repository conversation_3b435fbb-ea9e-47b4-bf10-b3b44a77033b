/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
扩缩容成功后callback
*/

package callback

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessModifyScaleSuccCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	time.Sleep(30 * time.Second)
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToCreate {
				node.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate {
				proxy.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToCreate {
			master.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.DataNodeCount != app.DataNodeNum {
		app.DataNodeNum = param.DataNodeCount
	}

	if param.ProxyNodeCount != app.ProxyNum {
		app.ProxyNum = param.ProxyNodeCount
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessModifyScaleErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToCreate ||
				node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
				node.Status == vdbmodel.NodeOrProxyStatusToDelete {
				node.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate ||
				proxy.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
				proxy.Status == vdbmodel.NodeOrProxyStatusToDelete {
				proxy.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToCreate ||
			master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToDelete {
			master.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}
