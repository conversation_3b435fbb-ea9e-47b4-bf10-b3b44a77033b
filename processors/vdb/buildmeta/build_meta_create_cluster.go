/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/04/26, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
)

func createClusters(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Clusters) > 0 {
		resource.LoggerTask.Warning(ctx, "clusters has been created")
		return nil
	}

	for i := 0; i < param.DataNodeCount; i++ {
		app.Clusters = append(app.Clusters, &vdbmodel.Cluster{
			ClusterID:  app.AppID + "-c-" + strconv.Itoa(i),
			AppID:      app.AppID,
			Engine:     vdbmodel.EngineVDBDataNode,
			Port:       param.DataNodePort,
			Status:     vdbmodel.NodeOrProxyStatusInUse,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
			DestSpec:   param.DataNodeSpec,
			StoreType:  vdbmodel.StoreTypeDRAM,
		})
	}
	maxNodeIdx := 0
	zoneCount := len(param.Replicas)
	zoneIdx := 0

	for _, cluster := range app.Clusters {
		replica := param.Replicas[zoneIdx%zoneCount]

		azone, found := zoneMapper(replica.Zone, true)
		if !found {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
			return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
		}

		cluster.MaxNodeIndex = maxNodeIdx

		cluster.Nodes = append(cluster.Nodes, &vdbmodel.Node{
			NodeID:     app.AppID + "-n-" + strconv.Itoa(maxNodeIdx),
			ClusterID:  cluster.ClusterID,
			AppID:      app.AppID,
			Engine:     cluster.Engine,
			Port:       cluster.Port,
			Region:     param.Region,
			LogicZone:  replica.Zone,
			Azone:      azone,
			VpcID:      param.VPCID,
			SubnetID:   replica.SubnetID,
			XagentPort: vdbmodel.DefaultXagentPort,
			Status:     vdbmodel.NodeOrProxyStatusToCreate,
			Basedir:    util.DefaultBaseDir,
			NodeFixID:  app.AppID + "-n-" + strconv.Itoa(maxNodeIdx),
		})
		maxNodeIdx++
		zoneIdx++

	}
	return nil
}

func createBlbModels(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	if len(app.BLBs) > 0 {
		resource.LoggerTask.Warning(ctx, "clusters has been created")
		return nil
	}
	app.BLBs = append(app.BLBs, &vdbmodel.BLB{
		AppID:    app.AppID,
		Name:     app.AppID,
		VpcID:    app.VpcID,
		SubnetID: param.BlbSubnetID,
		Type:     vdbmodel.BLBTypeApp,
		IPType:   vdbmodel.Ipv4,
	})

	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Trace(ctx, "standard app", logit.String("params :", base_utils.Format(param)))
		return nil
	}

	if privatecloud.IsPrivateENV() || privatecloud.IsDBStackAdaptorBCCENV() {
		return nil
	}

	if conf.VdbMainConf.AzEntrance == "no" {
		resource.LoggerTask.Trace(ctx, "az entrance is no", logit.String("params :", base_utils.Format(param)))
		return nil
	}

	resource.LoggerTask.Trace(ctx, "create az blb models", logit.String("params :", base_utils.Format(param)))

	for _, replica := range param.Replicas {
		subnetID := replica.SubnetID
		app.BLBs = append(app.BLBs, &vdbmodel.BLB{
			AppID:    app.AppID,
			Name:     app.AppID + "-az-" + replica.Zone,
			VpcID:    app.VpcID,
			SubnetID: subnetID,
			Type:     vdbmodel.BLBTypeApp,
			IPType:   vdbmodel.Ipv4,
			RsRange:  vdbmodel.BLBRsRangeAZone,
			MasterAZ: replica.Zone,
		})
	}

	return nil
}

func fillAppCluster(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	app.ImageID = bccresource.GetBccImageId()

	app.Type = vdbmodel.AppTypeCluster
	app.DeploySetIDs = strings.Join(param.DeployIDSet, ",")
	app.InnerPort = param.ProxyPort
	app.Region = param.Region
	version, err := GetDeployConfVersion(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get deploy conf failed", logit.Error("error", err))
		return err
	}

	app.ImageID += ":" + version
	// 包管理用自己的镜像，基于centos8
	// https://console.cloud.baidu-int.com/devops/icode/repos/baidu/scs/scs-image/tree/x1_pkg_manager_centos8
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		app.ImageID = conf.PkgManConf.Image.DockerImage
	} else {
		app.ImageID = conf.PkgManConf.Image.Bcc
	}

	return nil
}

func fillProxies(ctx context.Context, app *vdbmodel.Application, parameters *iface.TaskParameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Interfaces) > 0 {
		resource.LoggerTask.Warning(ctx, "interfaces has been created")
		return nil
	}

	totalProxyCount := parameters.ProxyNodeCount

	proxyCountPerAz := 1
	zoneCount := len(parameters.Replicas)
	if zoneCount < 2 {
		proxyCountPerAz = 2
		zoneCount = 2
	}
	itfCount := totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		itfCount++
	}

	for i := 0; i < itfCount; i++ {
		app.Interfaces = append(app.Interfaces, &vdbmodel.Interface{
			InterfaceID: app.AppID + "-itf-" + strconv.Itoa(i),
			AppID:       app.AppID,
			Engine:      vdbmodel.EngineVDBProxy,
			Port:        parameters.ProxyPort,
			Status:      vdbmodel.NodeOrProxyStatusInUse,
			CreateTime:  time.Now(),
			UpdateTime:  time.Now(),
			DestSpec:    parameters.ProxyNodeSpec,
			StoreType:   vdbmodel.StoreTypeDRAM,
		})
	}

	maxProxyID := 0
Outloop:
	for _, itf := range app.Interfaces {
		itf.MaxNodeIndex = maxProxyID
		for _, replica := range parameters.Replicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for i := 0; i < proxyCountPerAz; i++ {
				maxProxyID++
				itf.Proxies = append(itf.Proxies, &vdbmodel.Proxy{
					ProxyID:     app.AppID + "-p-" + strconv.Itoa(maxProxyID),
					InterfaceID: itf.InterfaceID,
					AppID:       app.AppID,
					Engine:      itf.Engine,
					Port:        itf.Port,
					Region:      parameters.Region,
					LogicZone:   replica.Zone,
					Azone:       azone,
					VpcID:       parameters.VPCID,
					SubnetID:    replica.SubnetID,
					XagentPort:  vdbmodel.DefaultXagentPort,
					Status:      vdbmodel.NodeOrProxyStatusToCreate,
					Basedir:     util.DefaultBaseDir,
					StatPort:    22222,
					NodeFixID:   app.AppID + "-p-" + strconv.Itoa(maxProxyID),
				})

				if maxProxyID >= totalProxyCount {
					break Outloop
				}
			}
		}
	}

	return nil
}

func fillMasters(ctx context.Context, app *vdbmodel.Application, parameters *iface.TaskParameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Masters) > 0 {
		resource.LoggerTask.Warning(ctx, "masters has been created")
		return nil
	}

	zoneCount := len(parameters.Replicas)
	totalMasterCount := 3
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix && zoneCount == 2 {
		totalMasterCount = 4
	}

	masterCountPerAz := 1
	if zoneCount < 2 {
		masterCountPerAz = totalMasterCount
		zoneCount = 1
	} else {
		masterCountPerAz = totalMasterCount / zoneCount
		if totalMasterCount%zoneCount != 0 {
			masterCountPerAz++
		}
	}

	resource.LoggerTask.Trace(ctx, "fillMasters", logit.String("totalMasterCount :", base_utils.Format(totalMasterCount)),
		logit.String("zoneCount :", base_utils.Format(zoneCount)), logit.String("masterCountPerAz :", base_utils.Format(masterCountPerAz)))

	maxMasterID := 0
Outloop:
	for i := 0; i < zoneCount; i++ {
		replica := &parameters.Replicas[i]
		azone, found := zoneMapper(parameters.Replicas[i].Zone, true)
		if !found {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
			return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
		}

		for i := 0; i < masterCountPerAz; i++ {
			maxMasterID++
			app.Masters = append(app.Masters, &vdbmodel.Master{
				MasterID:   app.AppID + "-m-" + strconv.Itoa(maxMasterID),
				AppID:      app.AppID,
				Engine:     vdbmodel.EngineVDBMaster,
				Port:       parameters.MasterPort,
				Region:     parameters.Region,
				LogicZone:  replica.Zone,
				Azone:      azone,
				VpcID:      parameters.VPCID,
				SubnetID:   replica.SubnetID,
				XagentPort: vdbmodel.DefaultXagentPort,
				Status:     vdbmodel.NodeOrProxyStatusToCreate,
				Basedir:    util.DefaultBaseDir,
				DestSpec:   parameters.MasterNodeSpec,
				StoreType:  vdbmodel.StoreTypeDRAM,
				NodeFixID:  app.AppID + "-m-" + strconv.Itoa(maxMasterID),
			})

			if maxMasterID >= totalMasterCount {
				break Outloop
			}
		}
	}

	return nil
}

func ProcessBuildMetaForCreatingCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "build create meta", logit.String("params :", base_utils.Format(param)))

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := fillAppCluster(ctx, app, param); err != nil {
		resource.LoggerTask.Error(ctx, "fillAppCluster failed", logit.Error("error", err))
		return err
	}

	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	if err := fillMasters(ctx, app, param, zoneMapper); err != nil {
		resource.LoggerTask.Error(ctx, "fillMasters failed", logit.Error("error", err))
		return err
	}

	if err := fillProxies(ctx, app, param, zoneMapper); err != nil {
		resource.LoggerTask.Error(ctx, "fillProxies failed", logit.Error("error", err))
		return err
	}

	if err := createClusters(ctx, app, param, zoneMapper); err != nil {
		resource.LoggerTask.Error(ctx, "createClusters failed", logit.Error("error", err))
		return err
	}

	if err := createBlbModels(ctx, app, param); err != nil {
		resource.LoggerTask.Error(ctx, "createBlbModels failed", logit.Error("error", err))
		return err
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}
