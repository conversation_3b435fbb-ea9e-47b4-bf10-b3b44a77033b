package cds

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessDeleteDataCds 删除数据盘cds
// 1. 遍历tocreate node，获取bcc资源id和volume id
// 2. 调用bcc接口先卸载cds再删除cds
func ProcessDeleteDataCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to detach cds in non-standalone mode")
		return nil
	}

	toCreateNode, err := getToCreateNode(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get tocreate node error", logit.String("appId", app.AppID), logit.Error("error", err))
		return err
	}

	bccResourceID := toCreateNode.ResourceID
	volumeID := toCreateNode.VolumeID
	if volumeID == "" {
		resource.LoggerTask.Notice(ctx, "volume id not found, no need to delete", logit.String("volumeID", volumeID),
			logit.String("bccResourceId", bccResourceID))
		return nil
	}

	err = doDetachCds(ctx, bccResourceID, volumeID, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "detach cds volume id failed", logit.Error("error", err))
		return err
	}

	for {
		select {
		case <-ctx.Done():
			return cerrs.ErrorTaskManual.Errorf("delete cds[%s] in node[%s] timeout", volumeID, toCreateNode.NodeID)
		case <-time.After(time.Second * 1):
			// pass
		}
		op, err := doDeleteCds(ctx, volumeID, app.UserID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "delete cds volume id failed", logit.String("volumeID", volumeID),
				logit.Error("error", err))
			return err
		}
		if op == "retry" {
			continue
		}
		break
	}

	toCreateNode.VolumeID = ""
	if err := vdbmodel.NodesSave(ctx, []*vdbmodel.Node{toCreateNode}); err != nil {
		resource.LoggerTask.Error(ctx, "save node error", logit.Error("error", err))
		return err
	}

	return nil
}

// doDeleteCds 调用bcc接口删除cds
func doDeleteCds(ctx context.Context, volumeID string, userID string) (string, error) {
	// describe cds
	volumeDetail, err := bccresource.BccResourceOp().GetCdsDetail(ctx, &bccresource.GetCdsDetailParam{
		VolumeID: volumeID,
		UserID:   userID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cds info failed", logit.Error("error", err))
		return "", err
	}

	// no need to delete
	if volumeDetail == nil {
		resource.LoggerTask.Warning(ctx, "cds not found", logit.String("volumeID", volumeID))
		return "", nil
	}

	if volumeDetail.Status != "Available" {
		resource.LoggerTask.Warning(ctx, "cds status is not available", logit.String("volumeID", volumeID),
			logit.String("status", volumeDetail.Status))
		return "retry", nil
	}

	// delete cds
	err = bccresource.BccResourceOp().DeleteCds(ctx, &bccresource.DeleteCdsParam{
		VolumeID:       volumeID,
		UserID:         userID,
		AutoSnapshot:   "on",
		ManualSnapshot: "on",
		Recycle:        "off",
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "delete cds failed", logit.String("volumeID", volumeID))
		return "", err
	}

	return "", nil
}
