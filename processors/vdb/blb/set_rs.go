/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2021/12/29, by shangshuai02(<EMAIL>), first version
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/errors"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessSetStandaloneRs 初始化BLB
// 1. 检查App所有分片的主，是否绑定到blb上；如果未绑定，则进行绑定
// 2. 检查App所有分片的从，是否绑定到blb上；如果已绑定，则进行解绑
// 相关代码：BlbExecutorProcessor::process中 cluster_table->version() == VERSION_V7的部分
// 代码中shard_info->op_type() == ShardOpType::EXCHANGE_MASTER是用于节点规格变更时，将绑定转换到新主上
// 这里暂不涉及节点规格变更的相关逻辑
func ProcessSetStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有节点的blb绑定状态
	if err := updateRsBinding(ctx, app, false); err != nil {
		return err
	}

	return nil
}

func updateRsBinding(ctx context.Context, app *vdbmodel.Application, inSelfHeal bool) error {
	// 获得normal和application类型的blb列表
	normalBlbList := make([]*vdbmodel.BLB, 0)
	appBlbList := make([]*vdbmodel.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.BlbID) == 0 {
			continue
		}
		if b.Type == vdbmodel.BLBTypeApp {
			appBlbList = append(appBlbList, b)
		}
		if b.Type == vdbmodel.BLBTypeNormal {
			normalBlbList = append(normalBlbList, b)
		}
	}

	// 获取主从节点
	bindNodes := make([]*vdbmodel.Node, 0)
	unbindNodes := make([]*vdbmodel.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if isNodeAvailable(node) {
				bindNodes = append(bindNodes, node)
			} else if inSelfHeal && node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				unbindNodes = append(unbindNodes, node)
			}
		}
	}

	// 更新normal类型的blb绑定状态
	if err := updateRsBindingByBLBList(ctx, app, normalBlbList, bindNodes, unbindNodes); err != nil {
		return err
	}

	// 更新application类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, bindNodes, unbindNodes); err != nil {
		return err
	}

	return nil
}

func updateRsBindingByBLBList(ctx context.Context, app *vdbmodel.Application, blbList []*vdbmodel.BLB,
	bindNodes []*vdbmodel.Node, unbindNodes []*vdbmodel.Node) error {
	var userID string

	if len(blbList) == 0 {
		return nil
	}

	blbIds := make([]string, 0)
	for _, b := range blbList {
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		blbIds = append(blbIds, b.BlbID)
	}

	// 绑定rs
	if len(bindNodes) > 0 {
		rss := make([]*blb.Rs, 0)
		for _, n := range bindNodes {
			uuid := n.ResourceID

			rss = append(rss, &blb.Rs{
				UUID:   uuid,
				Weight: 1,
				Port:   int32(n.Port),
			})
		}

		bindReq := &blb.BindRsParam{
			UserID: userID,
			BLBIDs: blbIds,
			Rss:    rss,
		}
		// 里面会做是否已经绑定的判断
		if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppID),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("rss", base_utils.Format(rss)))
			return errors.BindRsFail.Wrap(err)
		}
	}

	// 解绑rs
	if len(unbindNodes) > 0 {
		uuidList := make([]string, 0)
		for _, n := range unbindNodes {
			uuid := n.ResourceID

			uuidList = append(uuidList, uuid)
		}
		unbindReq := &blb.UnbindRsParam{
			UserID: userID,
			BLBIDs: blbIds,
			UUIDs:  uuidList,
		}
		// 里面会做是否已经解绑的判断
		if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind rs fail",
				logit.String("appId", app.AppID),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("UUIDs", base_utils.Format(uuidList)))
			return errors.UnBindRsFail.Wrap(err)
		}
	}

	return nil
}

// 处理ip组成员
func updateRsBindingByAppBLBList(ctx context.Context, app *vdbmodel.Application, blbList []*vdbmodel.BLB,
	bindNodes []*vdbmodel.Node, unbindNodes []*vdbmodel.Node) error {
	var userID string

	if len(blbList) == 0 {
		return nil
	}

	blbIds := make([]string, 0)
	for _, b := range blbList {
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		blbIds = append(blbIds, b.BlbID)
	}

	// 绑定rs
	if len(bindNodes) > 0 {
		rss := make([]*blbv2.Rs, 0)
		for _, n := range bindNodes {
			uuid := n.ResourceID

			rss = append(rss, &blbv2.Rs{
				UUID:   uuid,
				Weight: 1,
				Port:   n.Port,
				IP:     n.IP,
			})
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbList {
			blb := blb
			bindReq := &blbv2.BindRsParams{
				UserID:  userID,
				BLBID:   blb.BlbID,
				IPGroup: blb.IPGroupID,
				Rss:     rss,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().BindRs(ctx, bindReq)
				})
			})
			totalCount++
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
				logit.String("appId", app.AppID),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2BindRs", err))
			return err
		}
	}

	// 解绑rss
	if len(unbindNodes) > 0 {
		IPList := make([]string, 0)
		for _, n := range unbindNodes {
			rsIPPort := fmt.Sprintf("%s:%d", n.IP, n.Port)
			IPList = append(IPList, rsIPPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbList {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userID,
				BLBID:      blb.BlbID,
				IPGroup:    blb.IPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
			totalCount++
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppID),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}

	return nil
}

// ProcessSetProxyRs process proxy rs include app blb
func ProcessSetProxyRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return updateProxyBinding(ctx, app)
}

func ProcessSetProxyRsForModify(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if app.Type == vdbmodel.AppTypeStandalone {
		return updateRsBinding(ctx, app, true)
	}

	return updateProxyBinding(ctx, app)
}

func updateProxyBinding(ctx context.Context, app *vdbmodel.Application) error {
	blbIds := make([]string, 0)
	appBlbIds := make([]*vdbmodel.BLB, 0)
	var userID string

	for _, blb := range app.BLBs {
		if blb.RsRange == vdbmodel.BLBRsRangeAZone {
			continue
		}
		if len(blb.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = blb.ResourceUserID
		}
		if blb.Type == vdbmodel.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbID)
		}
		if blb.Type == vdbmodel.BLBTypeApp {
			if blb.BlbID == "" || blb.Status != vdbmodel.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbID))
				return fmt.Errorf("blb(%s) not available", blb.BlbID)
			}
			appBlbIds = append(appBlbIds, blb)
		}
	}

	if len(blbIds) <= 0 && len(appBlbIds) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	toBindRsList := []*blb.Rs{}
	toBindNodeList := []*blbv2.Rs{}
	toUnbindRsIDList := []string{}
	toUnbindNodeList := []*vdbmodel.Proxy{}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			var uuid string
			if app.ResourceType == "container" {
				uuid = proxy.ContainerID
			} else {
				uuid = proxy.ResourceID
			}

			switch proxy.Status {
			case vdbmodel.NodeOrProxyStatusToCreate:
				toBindRsList = append(toBindRsList, &blb.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   int32(proxy.Port),
				})
				toBindNodeList = append(toBindNodeList, &blbv2.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   proxy.Port,
					IP:     proxy.IP,
				})
			case vdbmodel.NodeOrProxyStatusToDelete, vdbmodel.NodeOrProxyStatusToFakeDelete:
				toUnbindRsIDList = append(toUnbindRsIDList, uuid)
				toUnbindNodeList = append(toUnbindNodeList, proxy)
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(toBindRsList)))
	resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(toUnbindRsIDList)))

	if len(blbIds) > 0 {
		if len(toBindRsList) > 0 {
			bindReq := &blb.BindRsParam{
				UserID: userID,
				BLBIDs: blbIds,
				Rss:    toBindRsList,
			}
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("appId", app.AppID),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("rss", base_utils.Format(toBindRsList)))
				return errors.BindRsFail.Wrap(err)
			}
		}
		if len(toUnbindRsIDList) > 0 {
			unbindReq := &blb.UnbindRsParam{
				UserID: userID,
				BLBIDs: blbIds,
				UUIDs:  toUnbindRsIDList,
			}
			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind rs fail",
					logit.String("appId", app.AppID),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
				return errors.UnBindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIds) > 0 {
		if len(toBindNodeList) > 0 {
			// bind
			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIds {
				blb := blb
				bindReq := &blbv2.BindRsParams{
					UserID:  userID,
					BLBID:   blb.BlbID,
					IPGroup: blb.IPGroupID,
					Rss:     toBindNodeList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().BindRs(ctx, bindReq)
					})
				})
				totalCount++
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
					logit.String("appId", app.AppID),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2BindRs", err))
				return err
			}
		}
		if len(toUnbindNodeList) > 0 {
			// unbind
			IPList := make([]string, 0)
			for _, n := range toUnbindNodeList {
				rsIPPort := fmt.Sprintf("%s:%d", n.IP, n.Port)
				IPList = append(IPList, rsIPPort)
			}

			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIds {
				blb := blb
				unbindReq := &blbv2.UnbindRsParams{
					UserID:     userID,
					BLBID:      blb.BlbID,
					IPGroup:    blb.IPGroupID,
					MemberList: IPList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().UnbindRs(ctx, unbindReq)
					})
				})
				totalCount++
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
					logit.String("appId", app.AppID),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2UnBindRs", err))
				return err
			}
		}
	}

	err := updateProxyBindingForRangeBLB(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update proxy binding for range blb fail",
			logit.String("appId", app.AppID),
			logit.Error("err", err))
		return err
	}
	return nil
}

func updateProxyBindingForRangeBLB(ctx context.Context, app *vdbmodel.Application) error {
	var userID string

	for _, b := range app.BLBs {
		if b.RsRange != vdbmodel.BLBRsRangeAZone {
			continue
		}

		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}

		if b.Type == vdbmodel.BLBTypeNormal {
			toBindRsList := []*blb.Rs{}
			toUnbindRsIDList := []string{}
			for _, itf := range app.Interfaces {
				for _, proxy := range itf.Proxies {
					if proxy.LogicZone != b.MasterAZ {
						continue
					}
					var uuid string
					if app.ResourceType == "container" {
						uuid = proxy.ContainerID
					} else {
						uuid = proxy.ResourceID
					}
					switch proxy.Status {
					case vdbmodel.NodeOrProxyStatusToCreate, vdbmodel.NodeOrProxyStatusInUse:
						toBindRsList = append(toBindRsList, &blb.Rs{
							UUID:   uuid,
							Weight: 1,
							Port:   int32(proxy.Port),
						})
					case vdbmodel.NodeOrProxyStatusToDelete, vdbmodel.NodeOrProxyStatusToFakeDelete:
						toUnbindRsIDList = append(toUnbindRsIDList, uuid)
					}
				}
			}
			resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(toBindRsList)))
			resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(toUnbindRsIDList)))
			blbIds := []string{b.BlbID}
			if len(toBindRsList) > 0 {
				bindReq := &blb.BindRsParam{
					UserID: userID,
					BLBIDs: blbIds,
					Rss:    toBindRsList,
				}
				if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
					resource.LoggerTask.Warning(ctx, "bind rs fail",
						logit.String("appId", app.AppID),
						logit.String("blbIds", base_utils.Format(blbIds)),
						logit.String("rss", base_utils.Format(toBindRsList)))
					return errors.BindRsFail.Wrap(err)
				}
			}

			if len(toUnbindRsIDList) > 0 {
				unbindReq := &blb.UnbindRsParam{
					UserID: userID,
					BLBIDs: blbIds,
					UUIDs:  toUnbindRsIDList,
				}
				if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
					resource.LoggerTask.Warning(ctx, "unbind rs fail",
						logit.String("appId", app.AppID),
						logit.String("blbIds", base_utils.Format(blbIds)),
						logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
					return errors.UnBindRsFail.Wrap(err)
				}
			}
		} else if b.Type == vdbmodel.BLBTypeApp {
			toBindRsList := []*blbv2.Rs{}
			toUnbindIPList := make([]string, 0)
			for _, itf := range app.Interfaces {
				for _, proxy := range itf.Proxies {
					if proxy.LogicZone != b.MasterAZ {
						continue
					}
					var uuid string
					if app.ResourceType == "container" {
						uuid = proxy.ContainerID
					} else {
						uuid = proxy.ResourceID
					}
					switch proxy.Status {
					case vdbmodel.NodeOrProxyStatusToCreate, vdbmodel.NodeOrProxyStatusInUse:
						toBindRsList = append(toBindRsList, &blbv2.Rs{
							UUID:   uuid,
							Weight: 1,
							Port:   proxy.Port,
							IP:     proxy.IP,
						})
					case vdbmodel.NodeOrProxyStatusToDelete, vdbmodel.NodeOrProxyStatusToFakeDelete:
						rsIPPort := fmt.Sprintf("%s:%d", proxy.IP, proxy.Port)
						toUnbindIPList = append(toUnbindIPList, rsIPPort)
					}
				}
			}

			resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(toBindRsList)))
			resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(toUnbindIPList)))
			if len(toBindRsList) > 0 {
				bindReq := &blbv2.BindRsParams{
					UserID:  userID,
					BLBID:   b.BlbID,
					IPGroup: b.IPGroupID,
					Rss:     toBindRsList,
				}

				err := blbv2.Instance().BindRs(ctx, bindReq)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "bind rs fail", logit.Error("err", err))
					return err
				}
			}

			if len(toUnbindIPList) > 0 {
				unbindReq := &blbv2.UnbindRsParams{
					UserID:     userID,
					BLBID:      b.BlbID,
					IPGroup:    b.IPGroupID,
					MemberList: toUnbindIPList,
				}

				err := blbv2.Instance().UnbindRs(ctx, unbindReq)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "unbind rs fail", logit.String("appId", app.AppID),
						logit.Error("doblbv2UnBindRs", err))
					return err
				}
			}
		}
	}

	return nil
}
