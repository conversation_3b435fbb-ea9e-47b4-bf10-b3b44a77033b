/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* package_manager.go */
/*
modification history
--------------------
2023/04/19 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package util

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	PkgNameAgent = "agent"

	GeryTagBloom = "bloomfilter"
	GeryNewAgent = "newagent"
)

type PkgManConf struct {
}

type ParmasGetGreyBoxFilter struct {
	ServerID    string
	AppID       string
	UserID      string
	VpcID       string
	UseNewAgent bool
}

// MergeGreyBoxPkgs 自适应替换为灰度包
// toDeployPkgInfos 是原始 包列表
// 返回值是已经替换为所需灰度包的 包列表
func MergeGreyBoxPkgs(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter, toDeployPkgInfos []*vdbmodel.Package) []*vdbmodel.Package {
	greyBoxPkgsMap := getLatestGreyBoxPkgsWithFilter(ctx, getGreyBoxFilter(ctx, getGreyBoxParam))
	if len(greyBoxPkgsMap) > 0 {
		resource.LoggerTask.Trace(ctx, "get grey box pkgs success", logit.String("serverID", getGreyBoxParam.ServerID),
			logit.String("grey box pkgs", base_utils.Format(greyBoxPkgsMap)))
	} else {
		return toDeployPkgInfos
	}
	var toDeployPkgInfosWithGreyBoxPackage []*vdbmodel.Package
	for _, toDeployPkg := range toDeployPkgInfos {
		if greyPkg, ok := greyBoxPkgsMap[toDeployPkg.Name]; ok {
			// 灰度map里有就用灰度的
			if greyPkg.MajorVersion == toDeployPkg.MajorVersion {
				resource.LoggerTask.Warning(ctx, "switch to grey box pkg",
					logit.String("grey box pkg", base_utils.Format(greyPkg)),
					logit.String("online pkg", base_utils.Format(toDeployPkg)))
				toDeployPkgInfosWithGreyBoxPackage = append(toDeployPkgInfosWithGreyBoxPackage, greyPkg)
			} else {
				// 大版本不一样的话有问题，用老得并报错
				resource.LoggerTask.Warning(ctx, "grey box pkgs major version not match",
					logit.String("grey box pkg", base_utils.Format(greyPkg)),
					logit.String("online pkg", base_utils.Format(toDeployPkg)))
				toDeployPkgInfosWithGreyBoxPackage = append(toDeployPkgInfosWithGreyBoxPackage, toDeployPkg)
			}
		} else {
			// 灰度map里没有就用原来的
			toDeployPkgInfosWithGreyBoxPackage = append(toDeployPkgInfosWithGreyBoxPackage, toDeployPkg)
		}
	}
	resource.LoggerTask.Trace(ctx, "merge grey box pkgs success",
		logit.String("online pkgs", base_utils.Format(toDeployPkgInfos)),
		logit.String("after merge grey pkgs", base_utils.Format(toDeployPkgInfosWithGreyBoxPackage)))
	return toDeployPkgInfosWithGreyBoxPackage
}

func getLatestGreyBoxPkgsWithFilter(ctx context.Context, greyBoxPkgFilter map[string]string) map[string]*vdbmodel.Package {
	mapGrepBoxPkgs := make(map[string]*vdbmodel.Package, 0)
	for needGreyBoxPackageName, GreyBoxTag := range greyBoxPkgFilter {
		greyPkgs, err := vdbmodel.GetGreyBoxPackages(ctx, needGreyBoxPackageName, GreyBoxTag)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get grey box pkgs info fail",
				logit.String("pkg name", needGreyBoxPackageName),
				logit.String("grey box tag", GreyBoxTag),
				logit.Error("err", err))
			continue
		}
		if len(greyPkgs) == 0 {
			resource.LoggerTask.Warning(ctx, "get grey box pkgs info empty",
				logit.String("pkg name", needGreyBoxPackageName),
				logit.String("grey box tag", GreyBoxTag))
			continue
		}
		mapGrepBoxPkgs[needGreyBoxPackageName] = nil
		for _, grepPkg := range greyPkgs {
			if mapGrepBoxPkgs[needGreyBoxPackageName] == nil {
				mapGrepBoxPkgs[needGreyBoxPackageName] = grepPkg
			} else {
				if grepPkg.ID > mapGrepBoxPkgs[needGreyBoxPackageName].ID {
					mapGrepBoxPkgs[needGreyBoxPackageName] = grepPkg
				}
			}
		}
	}
	return mapGrepBoxPkgs
}

// getGreyBoxFilter 获取灰度tag map
// 返回的map key是package name value是需要的灰度tag
// 这个内部实现可以再改，目前先简单实现支持设置布隆过滤器
func getGreyBoxFilter(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter) map[string]string {
	greyBoxFilter := make(map[string]string, 0)

	return greyBoxFilter
}

func fillNewAgentGreyFilter(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter, greyBoxFilter map[string]string) {
	if getGreyBoxParam.UseNewAgent {
		greyBoxFilter[PkgNameAgent] = GeryNewAgent
	}
}
