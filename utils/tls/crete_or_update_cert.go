package tls

import (
	"context"
	"crypto/x509"
	"crypto/x509/pkix"
	"errors"
	"math/big"
	"net"
	"time"

	"gorm.io/gorm"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
)

func ProcessCreateOrUpdateCertificate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get parameters error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	blb, err := vdbmodel.BLBGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get blb error", logit.Error("error", err))
		return err
	}

	if params.TLSAction == iface.TLSOpen {
		if err := ProcessOpenAction(ctx, app, blb); err != nil {
			resource.LoggerTask.Error(ctx, "process open action error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	} else if params.TLSAction == iface.TLSUpdate {
		if err := ProcessUpdateAction(ctx, app, blb); err != nil {
			resource.LoggerTask.Error(ctx, "process udpate action error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	} else if params.TLSAction == iface.TLSClose {
	} else {
		resource.LoggerTask.Error(ctx, "invalid tls action", logit.String("app_id", app.AppID), logit.String("action", params.TLSAction))
		return errors.New("invalid tls action")
	}

	return nil
}

func ProcessOpenAction(ctx context.Context, app *vdbmodel.Application, blb *vdbmodel.BLB) error {
	instanceCAExist := true
	endpointCAExist := true

	instanceCA, err := vdbmodel.CertificateCAGetByAppID(ctx, app.AppID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			instanceCAExist = false
		} else {
			resource.LoggerTask.Error(ctx, "get instance ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	}

	endpointCA, err := vdbmodel.CertificateServerCAGetByAppID(ctx, app.AppID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			endpointCAExist = false
		} else {
			resource.LoggerTask.Error(ctx, "get endpoint ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	}
	if instanceCAExist != endpointCAExist {
		resource.LoggerTask.Error(ctx, "unknown error occur, instance ca and endpoint ca should be both set or unset", logit.String("app_id", app.AppID),
			logit.Bool("instance_ca", instanceCAExist), logit.Bool("endpoint_ca", endpointCAExist))
		return errors.New("instance ca and endpoint ca should be both set or unset")
	}

	if !instanceCAExist && !endpointCAExist {
		instanceCA, endpointCA, err = GetCertificate(ctx, app, blb)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get certificate error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	}

	if err := vdbmodel.CertificatesSave(ctx, []*vdbmodel.Certificate{instanceCA, endpointCA}); err != nil {
		resource.LoggerTask.Error(ctx, "save ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessUpdateAction(ctx context.Context, app *vdbmodel.Application, blb *vdbmodel.BLB) error {
	newInstanceCA, newEndpointCA, err := GetCertificate(ctx, app, blb)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get certificate error", logit.String("app_id", app.AppID), logit.Error("error", err))
		return err
	}

	instanceCA, err := vdbmodel.CertificateCAGetByAppID(ctx, app.AppID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			instanceCA = newInstanceCA
		} else {
			resource.LoggerTask.Error(ctx, "get instance ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	} else {
		if instanceCA == nil {
			resource.LoggerTask.Error(ctx, "get instance ca error", logit.String("app_id", app.AppID))
			return errors.New("get instance ca error")
		}
		if err := updateCertificate(instanceCA, newInstanceCA); err != nil {
			resource.LoggerTask.Error(ctx, "update instance ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	}

	endpointCA, err := vdbmodel.CertificateServerCAGetByAppID(ctx, app.AppID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			endpointCA = newEndpointCA
		} else {
			resource.LoggerTask.Error(ctx, "get endpoint ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	} else {
		if endpointCA == nil {
			resource.LoggerTask.Error(ctx, "get endpoint ca error", logit.String("app_id", app.AppID))
			return errors.New("get endpoint ca error")
		}
		if err := updateCertificate(endpointCA, newEndpointCA); err != nil {
			resource.LoggerTask.Error(ctx, "update endpoint ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
			return err
		}
	}

	if err := vdbmodel.CertificatesSave(ctx, []*vdbmodel.Certificate{instanceCA, endpointCA}); err != nil {
		resource.LoggerTask.Error(ctx, "save ca error", logit.String("app_id", app.AppID), logit.Error("error", err))
		return err
	}
	return nil
}

func GetCertificate(ctx context.Context, app *vdbmodel.Application, blb *vdbmodel.BLB) (*vdbmodel.Certificate, *vdbmodel.Certificate, error) {
	interCert, interKey, err := CreateChildCertificate(GetInterCSR(), conf.RootCert, conf.RootPrivateKey)
	if err != nil {
		resource.LoggerTask.Error(ctx, "create inter certificate error", logit.Error("error", err))
		return nil, nil, err
	}
	interCertPem, interKeyPem, err := ConvertCertAndPkToPem(interCert, interKey)
	if err != nil {
		resource.LoggerTask.Error(ctx, "convert cert and pk to pem error", logit.Error("error", err))
		return nil, nil, err
	}

	endpointIP := blb.EndpointIP
	if len(blb.EndpointIP) == 0 {
		endpointIP = blb.Ovip
	}
	endpointCert, endpointKey, err := CreateChildCertificate(GetEndpointCSR(app.Domain, blb.Vip, endpointIP), interCert, interKey)
	if err != nil {
		resource.LoggerTask.Error(ctx, "create inter certificate error", logit.Error("error", err))
		return nil, nil, err
	}
	endpointCertPem, endpointKeyPem, err := ConvertCertAndPkToPem(endpointCert, endpointKey)
	if err != nil {
		resource.LoggerTask.Error(ctx, "convert cert and pk to pem error", logit.Error("error", err))
		return nil, nil, err
	}

	encryptedInterPk, err := EncryptAES(string(interKeyPem), conf.TLSConfIns.EncryptPass)
	if err != nil {
		resource.LoggerTask.Error(ctx, "encrypt pk error", logit.String("app_id", app.AppID), logit.Error("error", err))
		return nil, nil, err
	}

	encryptedEndpointPk, err := EncryptAES(string(endpointKeyPem), conf.TLSConfIns.EncryptPass)
	if err != nil {
		resource.LoggerTask.Error(ctx, "encrypt pk error", logit.String("app_id", app.AppID), logit.Error("error", err))
		return nil, nil, err
	}

	var instanceCA = &vdbmodel.Certificate{
		AppID:          app.AppID,
		UserID:         app.UserID,
		CertData:       string(interCertPem),
		CertPrivateKey: encryptedInterPk,
		CertType:       0,
		IssuedDate:     interCert.NotBefore.Unix(),
		ExpiredDate:    interCert.NotAfter.Unix(),
	}

	var endpointCA = &vdbmodel.Certificate{
		AppID:          app.AppID,
		UserID:         app.UserID,
		CertData:       string(endpointCertPem),
		CertPrivateKey: encryptedEndpointPk,
		CertType:       1,
		IssuedDate:     endpointCert.NotBefore.Unix(),
		ExpiredDate:    endpointCert.NotAfter.Unix(),
	}
	return instanceCA, endpointCA, nil
}

func updateCertificate(oldCertificate *vdbmodel.Certificate, newCertificate *vdbmodel.Certificate) error {
	if oldCertificate == nil || newCertificate == nil {
		return errors.New("old or new certificate is empty")
	}
	oldCertificate.CertData = newCertificate.CertData
	oldCertificate.CertPrivateKey = newCertificate.CertPrivateKey
	oldCertificate.IssuedDate = newCertificate.IssuedDate
	oldCertificate.ExpiredDate = newCertificate.ExpiredDate
	return nil
}

func GetInterCSR() *x509.Certificate {
	var interCsr = &x509.Certificate{
		Version:      1,
		SerialNumber: big.NewInt(time.Now().Unix()),
		Subject: pkix.Name{
			Country:            []string{conf.TLSConfIns.Country},
			Province:           []string{conf.TLSConfIns.Province},
			Locality:           []string{conf.TLSConfIns.Locality},
			Organization:       []string{conf.TLSConfIns.Organization},
			OrganizationalUnit: []string{conf.TLSConfIns.OrganizationalUnit},
			CommonName:         conf.TLSConfIns.InterCAName,
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(10, 0, 0),
		BasicConstraintsValid: true,
		IsCA:                  true,
		MaxPathLen:            0,
		MaxPathLenZero:        true,
		KeyUsage:              x509.KeyUsageCertSign | x509.KeyUsageCRLSign,
	}
	return interCsr
}

func GetEndpointCSR(domain string, vip string, endpointIP string) *x509.Certificate {
	var interCsr = &x509.Certificate{
		Version:      1,
		SerialNumber: big.NewInt(time.Now().Unix()),
		Subject: pkix.Name{
			Country:            []string{conf.TLSConfIns.Country},
			Province:           []string{conf.TLSConfIns.Province},
			Locality:           []string{conf.TLSConfIns.Locality},
			Organization:       []string{conf.TLSConfIns.Organization},
			OrganizationalUnit: []string{conf.TLSConfIns.OrganizationalUnit},
			CommonName:         domain,
		},
		DNSNames:              []string{domain},
		IPAddresses:           []net.IP{net.ParseIP(vip), net.ParseIP(endpointIP)},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(10, 0, 0),
		BasicConstraintsValid: true,
		IsCA:                  false,
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
	}
	return interCsr
}
