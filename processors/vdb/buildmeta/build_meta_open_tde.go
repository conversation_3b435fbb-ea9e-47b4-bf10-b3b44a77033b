/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* build_meta_upgrade.go   */
/*
modification history
--------------------
2024/08/28, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
	build meta for upgrade
*/

package buildmeta

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessBuildMetaForOpenClusterTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusModifying

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node.Status = vdbmodel.NodeOrProxyStatusToModify
		}
	}

	for _, master := range app.Masters {
		master.Status = vdbmodel.NodeOrProxyStatusToModify
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppID),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForOpenOneMasterTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	find := false

OutLoop:
	for _, master := range app.Masters {
		if master.MasterID == param.TargetNodeID && master.Status == vdbmodel.NodeOrProxyStatusToModify {
			master.Status = vdbmodel.NodeOrProxyStatusModifying
			resource.LoggerTask.Trace(ctx, "master status tomodify change to modifying", logit.String("appId", app.AppID),
				logit.String("masterId", param.TargetNodeID))
			find = true
			break OutLoop
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "master not found", logit.String("appId", app.AppID),
			logit.String("nodeId", param.TargetNodeID))
		return errors.New("master not found")
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForOpenOneDatanodeTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	find := false

OutLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusToModify {
				node.Status = vdbmodel.NodeOrProxyStatusModifying
				resource.LoggerTask.Trace(ctx, "datanode status tomodify change to modifying", logit.String("appId", app.AppID),
					logit.String("nodeId", param.TargetNodeID))
				find = true
				break OutLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "datanode not found", logit.String("appId", app.AppID),
			logit.String("nodeId", param.TargetNodeID))
		return errors.New("datanode not found")
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForOpenOneNodeTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.TargetNodeEngine == vdbmodel.EngineVDBMaster {
		return ProcessBuildMetaForOpenOneMasterTDE(ctx, teu)
	} else if param.TargetNodeEngine == vdbmodel.EngineVDBDataNode {
		return ProcessBuildMetaForOpenOneDatanodeTDE(ctx, teu)
	}

	resource.LoggerTask.Error(ctx, "error engine type", logit.String("appId", teu.Entity))
	return errors.New("not support engine")

}
