/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessUpgradeRollbackMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			proxy.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	for _, master := range app.Masters {
		master.Status = vdbmodel.NodeOrProxyStatusInUse
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func ProcessOpenClusterTDERollbackMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	TDEs, err := vdbmodel.TDEGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get tde error", logit.Error("error", err))
		return err
	}

	if len(TDEs) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("tdes", base_utils.Format(TDEs)))
		if err := vdbmodel.NodeDeleteMulti(ctx, TDEs); err != nil {
			resource.LoggerTask.Error(ctx, "delete tde meta failed", logit.Error("error", err))
			return err
		}
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	for _, master := range app.Masters {
		master.Status = vdbmodel.NodeOrProxyStatusInUse
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}
