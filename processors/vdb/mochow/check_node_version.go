/**
* @Copyright 2025 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/04/22 16:44
**/

package mochow

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func checkUpgradingNodeVersion(ctx context.Context, teu *workflow.TaskExecUnit, app *vdbmodel.Application) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}
	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Notice(ctx, "only check version in cluster mode")
		return nil
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if param.UpgradeParam == nil {
		resource.LoggerTask.Notice(ctx, "upgrade param is nil, skip check node version")
		return nil
	}

	targetNodeID := param.TargetNodeID
	targetVersion := param.UpgradeParam.Version

	// 根据目标节点类型选择对应的检查逻辑
	switch param.TargetNodeEngine {
	case vdbmodel.EngineVDBMaster:
		return checkTargetNodeVersion(ctx, app, targetNodeID, targetVersion, findUpgradingMasterNode, checkMasterVersion)
	case vdbmodel.EngineVDBDataNode:
		return checkTargetNodeVersion(ctx, app, targetNodeID, targetVersion, findUpgradingDataNode, checkDatanodeVersion)
	case vdbmodel.EngineVDBProxy:
		return checkTargetNodeVersion(ctx, app, targetNodeID, targetVersion, findUpgradingProxyNode, checkProxyVersion)
	default:
		return errors.New("unsupported engine type")
	}
}

func checkTargetNodeVersion(
	ctx context.Context,
	app *vdbmodel.Application,
	targetNodeID string,
	targetVersion string,
	findNodeFunc func(*vdbmodel.Application, string) (interface{}, error),
	checkVersionFunc func(context.Context, *vdbmodel.Application, interface{}, string) error) error {
	node, err := findNodeFunc(app, targetNodeID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "target node not found", logit.String("appId", app.AppID),
			logit.String("nodeId", targetNodeID))
		return err
	}
	return checkVersionFunc(ctx, app, node, targetVersion)
}

// 查找 Master 节点
func findUpgradingMasterNode(app *vdbmodel.Application, targetNodeID string) (interface{}, error) {
	for _, master := range app.Masters {
		if master.MasterID == targetNodeID && master.Status == vdbmodel.NodeOrProxyStatusUpgrading {
			return master, nil
		}
	}
	return nil, errors.New("target master node not found")
}

// 查找 DataNode 节点
func findUpgradingDataNode(app *vdbmodel.Application, targetNodeID string) (interface{}, error) {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == targetNodeID && node.Status == vdbmodel.NodeOrProxyStatusUpgrading {
				return node, nil
			}
		}
	}
	return nil, errors.New("target datanode not found")
}

// 查找 Proxy 节点
func findUpgradingProxyNode(app *vdbmodel.Application, targetNodeID string) (interface{}, error) {
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.ProxyID == targetNodeID && proxy.Status == vdbmodel.NodeOrProxyStatusUpgrading {
				return proxy, nil
			}
		}
	}
	return nil, errors.New("target proxy not found")
}

// 检查 Master 版本
func checkMasterVersion(ctx context.Context, app *vdbmodel.Application, targetMaster interface{}, targetVersion string) error {
	master := targetMaster.(*vdbmodel.Master)
	showMasterReq := mochow.ShowMasterRequest{
		Token: "default_token",
	}
	showMasterRsp, err := mochow.ShowMaster(ctx, master, &showMasterReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "show master failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "show master", logit.String("rsp :", base_utils.Format(showMasterRsp)))

	if showMasterRsp.MasterInfo.Version != targetVersion {
		resource.LoggerTask.Notice(ctx, "check master version failed")
		return fmt.Errorf("master version mismatched")
	}
	resource.LoggerTask.Notice(ctx, "checkMasterVersion complete")

	return nil
}

// 检查 DataNode 版本
func checkDatanodeVersion(ctx context.Context, app *vdbmodel.Application, targetDataNode interface{}, targetVersion string) error {
	node := targetDataNode.(*vdbmodel.Node)
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master leader error", logit.Error("error", err))
		return err
	}

	listDataNodeReq := mochow.ListDataNodeRequest{
		Token: "default_token",
	}
	listDataNodeRsp, err := mochow.ListDataNode(ctx, masterServer, &listDataNodeReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list datanode failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "list datanode", logit.String("rsp :", base_utils.Format(listDataNodeRsp)))

	if listDataNodeRsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list datanode failed", logit.Int32("status_code", listDataNodeRsp.Status.Code))
		return fmt.Errorf(listDataNodeRsp.Status.Msg)
	}
	if len(listDataNodeRsp.NodeList) == 0 {
		resource.LoggerTask.Error(ctx, "datanode list is empty", logit.String("rsp :", base_utils.Format(listDataNodeRsp)))
		return fmt.Errorf("datanode list is empty")
	}
	for _, dn := range listDataNodeRsp.NodeList {
		dnAddr := util.LittleEndianInetNtoA(int64(dn.NodeAddr.IP))
		if node.IP == dnAddr && dn.Version == targetVersion && dn.IsAlive {
			return nil
		}
	}
	resource.LoggerTask.Warning(ctx, "check datanode version failed")
	return fmt.Errorf("datanode version mismatched")
}

// 检查 Proxy 版本
func checkProxyVersion(ctx context.Context, app *vdbmodel.Application, targetProxy interface{}, targetVersion string) error {
	proxy := targetProxy.(*vdbmodel.Proxy)
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master leader error", logit.Error("error", err))
		return err
	}

	listRequest := mochow.ListProxyRequest{
		Token: "default_token",
	}
	listResponse, err := mochow.ListProxy(ctx, masterServer, &listRequest)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list proxy failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "list proxy", logit.String("rsp :", base_utils.Format(listResponse)))

	if listResponse.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list proxy failed", logit.Int32("status_code", listResponse.Status.Code))
		return fmt.Errorf(listResponse.Status.Msg)
	}
	if len(listResponse.ProxyList) == 0 {
		resource.LoggerTask.Error(ctx, "proxy list is empty", logit.String("rsp :", base_utils.Format(listResponse)))
		return fmt.Errorf("proxy list is empty")
	}

	for _, p := range listResponse.ProxyList {
		proxyAddr := util.LittleEndianInetNtoA(int64(p.ProxyAddr.IP))
		if proxy.IP == proxyAddr && p.Version == targetVersion && p.IsAlive {
			return nil
		}
	}
	resource.LoggerTask.Warning(ctx, "check proxy version failed")
	return fmt.Errorf("proxy version mismatched")
}
