/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessRollbackBccResourcesMeta process rollback bcc resouce and meta
func ProcessRollbackMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var toDeleteNodes []*vdbmodel.Node
	var toDeleteProxies []*vdbmodel.Proxy
	var toDeleteMasterNodes []*vdbmodel.Master
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToCreate {
				toDeleteNodes = append(toDeleteNodes, node)
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate {
				toDeleteProxies = append(toDeleteProxies, proxy)
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToCreate {
			toDeleteMasterNodes = append(toDeleteMasterNodes, master)
		}
	}

	if len(toDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := vdbmodel.NodeDeleteMulti(ctx, toDeleteNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteProxies) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("proxies", base_utils.Format(toDeleteProxies)))
		if err := vdbmodel.ProxyDeleteMulti(ctx, toDeleteProxies); err != nil {
			resource.LoggerTask.Error(ctx, "delete proxy meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteMasterNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete master from vdbmodel", logit.String("nodes", base_utils.Format(toDeleteMasterNodes)))
		if err := vdbmodel.MasterDeleteMulti(ctx, toDeleteMasterNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete master meta failed", logit.Error("error", err))
			return err
		}
	}

	if err := rollbackClusterAndInterface(ctx, app); err != nil {
		return err
	}

	return nil
}

func rollbackClusterAndInterface(ctx context.Context, app *vdbmodel.Application) error {
	var toDeleteClusters []*vdbmodel.Cluster
	var toDeleteInterfaces []*vdbmodel.Interface
	for _, cluster := range app.Clusters {
		isEmpty := true
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			toDeleteClusters = append(toDeleteClusters, cluster)
		}
	}
	if len(toDeleteClusters) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("clusters", base_utils.Format(toDeleteClusters)))
		if err := vdbmodel.ClusterDeleteMulti(ctx, toDeleteClusters); err != nil {
			resource.LoggerTask.Error(ctx, "delete cluster meta failed", logit.Error("error", err))
			return err
		}
	}

	for _, itf := range app.Interfaces {
		isEmpty := true
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			toDeleteInterfaces = append(toDeleteInterfaces, itf)
		}
	}
	if len(toDeleteInterfaces) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("interfaces", base_utils.Format(toDeleteInterfaces)))
		if err := vdbmodel.InterfaceDeleteMulti(ctx, toDeleteInterfaces); err != nil {
			resource.LoggerTask.Error(ctx, "delete interface meta failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}
