// Copyright(C) 2021 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2021-11-30, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package bootstrap

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/scs/x1-base/task/bootstrap"
)

// Start 启动应用,同步的
func Start(ctx context.Context, cfg *Config) error {
	defer BeforeShutdown()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	var g gtask.Group
	ctx = g.WithContext(ctx)

	g.Go(func() error {
		return bootstrap.Start(ctx)
	})

	if err := RecordProcessID(filepath.Join(env.RootDir(), "x1-task.pid")); err != nil {
		log.Println("record pid failed, err: ", err)
	}

	_, err := g.Wait()
	return err
}

// RecordProcessID print pid into pid file
func RecordProcessID(name string) error {
	if name == "" {
		name = "../x1-task.pid"
	}

	file, err := os.OpenFile(name, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = fmt.Fprintf(file, "%d", os.Getpid())
	return err
}
