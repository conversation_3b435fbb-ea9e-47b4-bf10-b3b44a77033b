/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package upgrade

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/deploy"
)

func ExecuteUpgradeMaster(ctx context.Context, app *vdbmodel.Application, master *vdbmodel.Master,
	upgradeParam *iface.UpgradeParam, taskID string) error {
	resource.LoggerTask.Trace(ctx, "start upgrade master", logit.String("masterID", master.MasterID),
		logit.String("taskID", taskID))
	TDE, err := deploy.GetTdeInfo(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get tde error", logit.Error("error", err))
		return err
	}
	deployParams := deploy.GetMasterDeployParams(ctx, app, master, TDE)
	deployParams.IsUpgrade = true
	deployParams.TaskID = taskID
	deployParams.NoUpgradeConf = upgradeParam.NoUpgradeConf
	return deploy.UpgradeNodeOfAllTypeNew(ctx, deployParams, upgradeParam)
}

func ProcessUpgradeOneMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetMaster *vdbmodel.Master
	find := false

OuterLoop:
	for _, master := range app.Masters {
		if master.MasterID == param.TargetNodeID && master.Status == vdbmodel.NodeOrProxyStatusUpgrading {
			targetMaster = master
			find = true
			break OuterLoop
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target master", logit.String("masterID", param.TargetNodeID))
		return errors.New("can not find target master")
	}

	err = ExecuteUpgradeMaster(ctx, app, targetMaster, param.UpgradeParam, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "updtae master error", logit.String("master", base_utils.Format(targetMaster)), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessUpgradeOneDatanode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster
	find := false

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusUpgrading {
				targetNode = node
				targetCluster = cluster
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target node", logit.String("nodeID", param.TargetNodeID))
		return errors.New("can not find target node")
	}

	err = ExecuteUpgradeDatanode(ctx, app, targetCluster, targetNode, param.UpgradeParam, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "update datanode error", logit.String("node", base_utils.Format(targetNode)), logit.Error("error", err))
		return err
	}

	return nil
}

func ExecuteUpgradeProxy(ctx context.Context, app *vdbmodel.Application, itf *vdbmodel.Interface,
	proxy *vdbmodel.Proxy, upgradeParam *iface.UpgradeParam, taskID string) error {
	resource.LoggerTask.Trace(ctx, "start upgrade proxy", logit.String("proxyID", proxy.ProxyID))
	deployParams := deploy.GetProxyDeployParams(ctx, app, itf, proxy)
	deployParams.IsUpgrade = true
	deployParams.TaskID = taskID
	deployParams.NoUpgradeConf = upgradeParam.NoUpgradeConf
	return deploy.UpgradeNodeOfAllTypeNew(ctx, deployParams, upgradeParam)
}

func ProcessUpgradeOneProxy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetProxy *vdbmodel.Proxy
	var targetItf *vdbmodel.Interface
	find := false

OuterLoop:
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.ProxyID == param.TargetNodeID && proxy.Status == vdbmodel.NodeOrProxyStatusUpgrading {
				targetProxy = proxy
				targetItf = itf
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target proxy", logit.String("proxyID", param.TargetNodeID))
		return errors.New("can not find target node")
	}

	err = ExecuteUpgradeProxy(ctx, app, targetItf, targetProxy, param.UpgradeParam, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "upgrade proxy error", logit.String("proxy", base_utils.Format(targetProxy)), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessUpgradeOneNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.TargetNodeEngine == vdbmodel.EngineVDBMaster {
		return ProcessUpgradeOneMaster(ctx, teu)
	} else if param.TargetNodeEngine == vdbmodel.EngineVDBDataNode {
		return ProcessUpgradeOneDatanode(ctx, teu)
	} else if param.TargetNodeEngine == vdbmodel.EngineVDBProxy {
		return ProcessUpgradeOneProxy(ctx, teu)
	}

	return errors.New("not support engine")
}
