package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/recover"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowVdbRecoverInOriginalClusterDbrs      = "vdb-recover-in-original-cluster-dbrs-app"
	StepRecoverInOriginalClusterDbrsCloseHa      = "vdb-recover-in-original-cluster-dbrs-close-ha"
	StepRecoverInOriginalClusterDbrsDispatchTask = "vdb-recover-in-original-cluster-dbrs-dispatch-task"
	StepRecoverInOriginalClusterDbrsCheckStatus  = "vdb-recover-in-original-cluster-dbrs-check-status"
	StepRecoverInOriginalClusterDbrsRecoverHa    = "vdb-recover-in-original-cluster-dbrs-recover-ha"
	StepRecoverInOriginalClusterDbrsCallBack     = "vdb-recover-in-original-cluster-dbrs-cb"
)

func init() {
	// Step-1 关闭HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDbrsCloseHa,
		Workflow:        WorkflowVdbRecoverInOriginalClusterDbrs,
		StepProcess:     xmaster.ProcessCloseAppHaStatusInXmaster,
		SuccessNextStep: StepRecoverInOriginalClusterDbrsDispatchTask,
		ErrorNextStep:   StepRecoverInOriginalClusterDbrsCloseHa,
	})

	// Step-2 dispatch task
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDbrsDispatchTask,
		Workflow:        WorkflowVdbRecoverInOriginalClusterDbrs,
		StepProcess:     recover.ProcessDbrsRecoverInOriginalCluster,
		SuccessNextStep: StepRecoverInOriginalClusterDbrsCheckStatus,
		ErrorNextStep:   StepRecoverInOriginalClusterDbrsDispatchTask},

		workflow.WithStepTimeout(60*time.Minute))

	// Step-3 检查状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDbrsCheckStatus,
		Workflow:        WorkflowVdbRecoverInOriginalClusterDbrs,
		StepProcess:     mochow.ProcessCheckAllStatus,
		SuccessNextStep: StepRecoverInOriginalClusterDbrsRecoverHa,
		ErrorNextStep:   StepRecoverInOriginalClusterDbrsCheckStatus},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-4 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDbrsRecoverHa,
		Workflow:        WorkflowVdbRecoverInOriginalClusterDbrs,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: StepRecoverInOriginalClusterDbrsCallBack,
		ErrorNextStep:   StepRecoverInOriginalClusterDbrsRecoverHa,
	})

	// Step-5 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDbrsCallBack,
		Workflow:        WorkflowVdbRecoverInOriginalClusterDbrs,
		StepProcess:     callback.ProcessRecoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverInOriginalClusterDbrsCallBack,
	})
}
