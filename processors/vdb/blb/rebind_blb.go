/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2022/03/31
 * File: rebind_blb.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package blb TODO package function desc
package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/errors"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessRebindProxyRs process proxy blb rs include app blb
func ProcessRebindProxyRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	err = clusterRebindProxyRs(ctx, app, app)
	if err != nil {
		return err
	}

	return nil
}

func clusterRebindProxyRs(ctx context.Context, BlbApp *vdbmodel.Application, RsApp *vdbmodel.Application) error {
	var userID string

	blbIds := make([]string, 0)
	appBlbIDs := make([]*vdbmodel.BLB, 0)
	for _, blb := range BlbApp.BLBs {
		if blb.RsRange == vdbmodel.BLBRsRangeAZone {
			continue
		}

		if len(blb.ResourceUserID) == 0 {
			userID = BlbApp.UserID
		} else {
			userID = blb.ResourceUserID
		}
		if blb.Type == vdbmodel.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbID)
		}
		if blb.Type == vdbmodel.BLBTypeApp {
			appBlbIDs = append(appBlbIDs, blb)
		}
	}

	if len(blbIds) <= 0 && len(appBlbIDs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	if len(blbIds) > 0 {
		toBindRsList := []*blb.Rs{}
		for _, itf := range RsApp.Interfaces {
			for _, proxy := range itf.Proxies {
				switch proxy.Status {
				case vdbmodel.NodeOrProxyStatusToCreate, vdbmodel.NodeOrProxyStatusInUse:
					var uuid string
					if RsApp.ResourceType == "container" {
						uuid = proxy.ContainerID
					} else {
						uuid = proxy.ResourceID
					}
					toBindRsList = append(toBindRsList, &blb.Rs{
						UUID:   uuid,
						Weight: 1,
						Port:   int32(proxy.Port),
					})
				}
			}
		}
		if len(toBindRsList) > 0 {
			bindReq := &blb.BindRsParam{
				UserID: userID,
				BLBIDs: blbIds,
				Rss:    toBindRsList,
			}
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("BlbAppID", BlbApp.AppID),
					logit.String("RsAppID", RsApp.AppID),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("rss", base_utils.Format(toBindRsList)))
				return errors.BindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIDs) > 0 {
		// 处理proxy的应用型BLB的逻辑
		toBindRsList := []*blbv2.Rs{}
		for _, itf := range RsApp.Interfaces {
			for _, proxy := range itf.Proxies {
				switch proxy.Status {
				case vdbmodel.NodeOrProxyStatusToCreate, vdbmodel.NodeOrProxyStatusInUse:
					var uuid string
					if RsApp.ResourceType == "container" {
						uuid = proxy.ContainerID
					} else {
						uuid = proxy.ResourceID
					}
					toBindRsList = append(toBindRsList, &blbv2.Rs{
						UUID:   uuid,
						Weight: 1,
						Port:   proxy.Port,
						IP:     proxy.IP,
					})
				}
			}
		}
		if len(toBindRsList) > 0 {
			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIDs {
				blb := blb
				bindReq := &blbv2.BindRsParams{
					UserID:  userID,
					BLBID:   blb.BlbID,
					IPGroup: blb.IPGroupID,
					Rss:     toBindRsList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().BindRs(ctx, bindReq)
					})
				})
				totalCount++
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
					logit.String("BlbAppID", BlbApp.AppID),
					logit.String("RsAppID", RsApp.AppID),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2BindRs", err))
				return err
			}
		}
	}

	err := RebindProxyRsForRangeBLB(ctx, BlbApp, RsApp)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "rebind proxy rs for range blb fail",
			logit.String("BlbAppID", BlbApp.AppID),
			logit.String("RsAppID", RsApp.AppID),
			logit.Error("err", err))
		return err
	}

	return nil
}

func RebindProxyRsForRangeBLB(ctx context.Context, BlbApp *vdbmodel.Application, RsApp *vdbmodel.Application) error {
	var userID string

	for _, b := range BlbApp.BLBs {
		if b.RsRange != vdbmodel.BLBRsRangeAZone {
			continue
		}

		if len(b.ResourceUserID) == 0 {
			userID = BlbApp.UserID
		} else {
			userID = b.ResourceUserID
		}

		if b.Type == vdbmodel.BLBTypeNormal {
			toBindRsList := []*blb.Rs{}
			for _, itf := range RsApp.Interfaces {
				for _, proxy := range itf.Proxies {
					if proxy.LogicZone != b.MasterAZ {
						continue
					}
					switch proxy.Status {
					case vdbmodel.NodeOrProxyStatusToCreate, vdbmodel.NodeOrProxyStatusInUse:
						var uuid string
						if RsApp.ResourceType == "container" {
							uuid = proxy.ContainerID
						} else {
							uuid = proxy.ResourceID
						}
						toBindRsList = append(toBindRsList, &blb.Rs{
							UUID:   uuid,
							Weight: 1,
							Port:   int32(proxy.Port),
						})
					}
				}
			}
			blbIds := []string{b.BlbID}
			if len(toBindRsList) > 0 {
				bindReq := &blb.BindRsParam{
					UserID: userID,
					BLBIDs: blbIds,
					Rss:    toBindRsList,
				}
				if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
					resource.LoggerTask.Warning(ctx, "bind rs fail",
						logit.String("BlbAppID", BlbApp.AppID),
						logit.String("RsAppID", RsApp.AppID),
						logit.String("blbIds", base_utils.Format(blbIds)),
						logit.String("rss", base_utils.Format(toBindRsList)))
					return errors.BindRsFail.Wrap(err)
				}
			}
		} else if b.Type == vdbmodel.BLBTypeApp {
			toBindRsList := []*blbv2.Rs{}
			for _, itf := range RsApp.Interfaces {
				for _, proxy := range itf.Proxies {
					if proxy.LogicZone != b.MasterAZ {
						continue
					}
					switch proxy.Status {
					case vdbmodel.NodeOrProxyStatusToCreate, vdbmodel.NodeOrProxyStatusInUse:
						var uuid string
						if RsApp.ResourceType == "container" {
							uuid = proxy.ContainerID
						} else {
							uuid = proxy.ResourceID
						}
						toBindRsList = append(toBindRsList, &blbv2.Rs{
							UUID:   uuid,
							Weight: 1,
							Port:   proxy.Port,
							IP:     proxy.IP,
						})
					}
				}
			}
			if len(toBindRsList) > 0 {
				bindReq := &blbv2.BindRsParams{
					UserID:  userID,
					BLBID:   b.BlbID,
					IPGroup: b.IPGroupID,
					Rss:     toBindRsList,
				}

				err := blbv2.Instance().BindRs(ctx, bindReq)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "bind rs fail", logit.Error("err", err))
					return err
				}
			}
		}
	}

	return nil
}

// ProcessRebindStandaloneRs 重启，重新挂载Blb
func ProcessRebindStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有节点的blb绑定状态
	if err := updateRsBinding(ctx, app, false); err != nil {
		return err
	}

	return nil
}

func ProcessSwitchEntranceRebindFirstRsToSecondBlb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	firstAppID := param.SwitchEntranceParam.FirstAppID
	SecondAppID := param.SwitchEntranceParam.SecondAppID

	firstApp, err := vdbmodel.ApplicationGetByAppID(ctx, firstAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get first app fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	SecondApp, err := vdbmodel.ApplicationGetByAppID(ctx, SecondAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get second app fail", logit.String("appId", SecondAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	err = clusterRebindProxyRs(ctx, SecondApp, firstApp)
	if err != nil {
		return err
	}

	return nil
}

func ProcessSwitchEntranceRebindSecondRsToFirstBlb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	firstAppID := param.SwitchEntranceParam.FirstAppID
	SecondAppID := param.SwitchEntranceParam.SecondAppID

	firstApp, err := vdbmodel.ApplicationGetByAppID(ctx, firstAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get first app fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	SecondApp, err := vdbmodel.ApplicationGetByAppID(ctx, SecondAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get second app fail", logit.String("appId", SecondAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	err = clusterRebindProxyRs(ctx, firstApp, SecondApp)
	if err != nil {
		return err
	}

	return nil
}
