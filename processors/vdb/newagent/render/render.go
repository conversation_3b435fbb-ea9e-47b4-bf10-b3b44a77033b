package render

import (
	"bytes"
	"context"
	"fmt"
	"text/template"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/getvars"
)

type RenderedConfig struct {
	ConfPath    string `json:"conf_path"`
	ConfContent string `json:"conf_content"`
}

func GetRenderdConf(ctx context.Context, params *getvars.Parameter, tplID, pkgVersion string) ([]*RenderedConfig, error) {
	confTpls, err := vdbmodel.ConfTplGetAllByCond(ctx, "tpl_id = ?", tplID)
	if err != nil {
		return nil, err
	}
	if len(confTpls) == 0 {
		return nil, fmt.Errorf("tpl_id(%s) not found", tplID)
	}
	params.PackageVersion = pkgVersion
	var renderedConfs []*RenderedConfig
	for _, confTplItem := range confTpls[0].Items {
		t, err := template.New(tplID + "-" + confTplItem.DeployPath).Parse(confTplItem.Content)
		if err != nil {
			return nil, err
		}
		buffer := bytes.Buffer{}
		var vars map[string]string
		switch confTpls[0].PkgType {
		case "mochow-datanode":
			vars, err = getvars.GetDataNodeVar(ctx, params)
			if err != nil {
				return nil, err
			}
		case "mochow-proxy":
			vars, err = getvars.GetProxyVars(ctx, params)
			if err != nil {
				return nil, err
			}
		case "mochow-master":
			vars, err = getvars.GetMasterVars(ctx, params)
			if err != nil {
				return nil, err
			}
		case "mochow-standalone":
			vars, err = getvars.GetStandaloneVar(ctx, params)
			if err != nil {
				return nil, err
			}
		default:
			return nil, fmt.Errorf("unknown package name: %s", confTpls[0].PkgType)
		}
		if err := t.Execute(&buffer, vars); err != nil {
			return nil, err
		}
		rawConf := buffer.String()
		resource.LoggerTask.Trace(ctx, "trace rendered conf",
			logit.String("conf_path", confTplItem.DeployPath), logit.String("conf_content", rawConf))

		renderedConfs = append(renderedConfs, &RenderedConfig{
			ConfPath:    confTplItem.DeployPath,
			ConfContent: rawConf,
		})
	}
	return renderedConfs, nil
}
