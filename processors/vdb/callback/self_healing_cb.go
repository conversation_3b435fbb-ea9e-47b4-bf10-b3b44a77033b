/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessSelfHealingCb callback
func ProcessSelfHealingCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToDelete ||
				node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
				node.Status == vdbmodel.NodeOrProxyStatusToCreate {
				node.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToDelete ||
				proxy.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
				proxy.Status == vdbmodel.NodeOrProxyStatusToCreate {
				proxy.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToCreate {
			master.Status = vdbmodel.NodeOrProxyStatusInUse
		}

		if master.DestSpec != master.Spec {
			master.Spec = master.DestSpec
		}
		master.ActualCPU = master.CPU
		master.ActualMemSize = master.MemSize
		master.ActualDiskSize = int(master.DiskSize)
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}
