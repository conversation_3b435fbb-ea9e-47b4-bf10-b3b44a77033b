package bcm

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bcm"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

const (
	TypeInstanceForStandalone   = "RD_ST_INSTANCE"
	TypeRoForStandalone         = "RD_ST_NODE_RO"
	TypeClusterForRedisCluster  = "RD_CL_CLUSTER"
	TypeProxyForRedisCluster    = "RD_CL_PROXY"
	TypeShardForRedisCluster    = "RD_CL_SHARD"
	TypeClusterForPegaDBCluster = "PG_CL_CLUSTER"
	TypeProxyForPegaDBCluster   = "PG_CL_PROXY"
	TypeShardForPegaDBCluster   = "PG_CL_SHARD"
)

func isIgnore(action string) bool {

	return action == bcm.GroupInstanceActionRemove
}

func toMap(ctx context.Context, bcmInstanceGroup string) (map[string]string, error) {
	var m map[string]string
	err := json.Unmarshal([]byte(bcmInstanceGroup), &m)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Unmarshal GroupIDs fail",
			logit.Error("error", err))
		return m, fmt.Errorf("unmarshal GroupIDs fail")
	}
	return m, nil
}

func toString(ctx context.Context, typeName string, groups string) (string, error) {
	m := make(map[string]string, 0)
	m[typeName] = groups
	b, err := json.Marshal(m)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Marshal GroupIDs fail", logit.String("appId", base_utils.Format(m)),
			logit.Error("error", err))
		return "", err
	}
	return string(b), nil
}

func toIntArray(ctx context.Context, ids string) ([]int64, error) {
	idsStrSlice := strings.Split(ids, ",")
	result := make([]int64, len(idsStrSlice))
	for i, idStr := range idsStrSlice {
		idInt, err := strconv.Atoi(idStr)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Invalid ID format", logit.String("ids", ids))
			return result, err
		}
		result[i] = int64(idInt)
	}
	return result, nil
}

func getClusterAddParamsAndFillMeta(ctx context.Context, app *vdbmodel.Application, groupIDs string, typeName, action string) (
	*bcm.OperateClusterWithGroupParams, error) {

	ids, err := toIntArray(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	groupStr, err := toString(ctx, typeName, groupIDs)
	if err != nil {
		return nil, err
	}

	var hashNames []string
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}

			hashNames = append(hashNames, util.GetHashName(cluster.ClusterID, app.AppName))
			cluster.BcmInstanceGroup = groupStr
		}
	}

	return &bcm.OperateClusterWithGroupParams{
		UserID:    app.UserID,
		AppID:     app.AppID,
		TypeName:  typeName,
		GroupIDs:  ids,
		HashNames: hashNames,
		AppType:   app.Type,
		Action:    action,
		IgnoreErr: isIgnore(action),
	}, nil
}

func getClusterRemoveParams(ctx context.Context, app *vdbmodel.Application, status string) (
	[]*bcm.OperateClusterWithGroupParams, error) {

	var params []*bcm.OperateClusterWithGroupParams

	for _, cluster := range app.Clusters {
		if len(cluster.BcmInstanceGroup) == 0 {
			continue
		}

		m, err := toMap(ctx, cluster.BcmInstanceGroup)
		if err != nil {
			return params, err
		}

		for _, node := range cluster.Nodes {
			if node.Status != status {
				continue
			}

			for typeName, groupIDs := range m {
				if len(groupIDs) == 0 {
					continue
				}
				ids, err := toIntArray(ctx, groupIDs)
				if err != nil {
					return params, err
				}
				params = append(params, &bcm.OperateClusterWithGroupParams{
					UserID:    app.UserID,
					AppID:     app.AppID,
					TypeName:  typeName,
					GroupIDs:  ids,
					HashNames: []string{util.GetHashName(cluster.ClusterID, app.AppName)},
					Action:    bcm.GroupInstanceActionRemove,
					IgnoreErr: isIgnore(bcm.GroupInstanceActionRemove),
				})
			}
		}
	}

	return params, nil
}

func getProxyAddParamsAndFillMeta(ctx context.Context, app *vdbmodel.Application, groupIDs string, typeName, action string) (
	*bcm.OperateNodeWithGroupParams, error) {

	ids, err := toIntArray(ctx, groupIDs)
	if err != nil {
		return nil, err
	}
	groupStr, err := toString(ctx, typeName, groupIDs)
	if err != nil {
		return nil, err
	}

	var fixIDs []string
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			fixIDs = append(fixIDs, proxy.NodeFixID)
			itf.BcmInstanceGroup = groupStr
		}
	}

	return &bcm.OperateNodeWithGroupParams{
		UserID:     app.UserID,
		AppID:      app.AppID,
		TypeName:   typeName,
		GroupIDs:   ids,
		NodeFixIDs: fixIDs,
		Action:     action,
		IgnoreErr:  isIgnore(action),
	}, nil
}

func getProxyRemoveParams(ctx context.Context, app *vdbmodel.Application, status string) (
	[]*bcm.OperateNodeWithGroupParams, error) {

	var params []*bcm.OperateNodeWithGroupParams

	for _, itf := range app.Interfaces {
		if len(itf.BcmInstanceGroup) == 0 {
			continue
		}
		m, err := toMap(ctx, itf.BcmInstanceGroup)
		if err != nil {
			return params, err
		}
		for _, proxy := range itf.Proxies {
			if proxy.Status != status {
				continue
			}
			for typeName, groupIDs := range m {
				if len(groupIDs) == 0 {
					continue
				}
				ids, err := toIntArray(ctx, groupIDs)
				if err != nil {
					return params, err
				}
				params = append(params, &bcm.OperateNodeWithGroupParams{
					UserID:     app.UserID,
					AppID:      app.AppID,
					TypeName:   typeName,
					GroupIDs:   ids,
					NodeFixIDs: []string{proxy.NodeFixID},
					Action:     bcm.GroupInstanceActionRemove,
					IgnoreErr:  isIgnore(bcm.GroupInstanceActionRemove),
				})
			}
		}
	}

	return params, nil
}

func getRollbackProxyRemoveParams(ctx context.Context, app *vdbmodel.Application, status string) (
	[]*bcm.OperateNodeWithGroupParams, error) {

	var params []*bcm.OperateNodeWithGroupParams

	for _, itf := range app.Interfaces {
		if len(itf.BcmInstanceGroup) == 0 {
			continue
		}
		m, err := toMap(ctx, itf.BcmInstanceGroup)
		if err != nil {
			return params, err
		}
		for _, proxy := range itf.Proxies {
			if proxy.Status != status {
				continue
			}
			for typeName, groupIDs := range m {
				if len(groupIDs) == 0 {
					continue
				}
				ids, err := toIntArray(ctx, groupIDs)
				if err != nil {
					return params, err
				}
				params = append(params, &bcm.OperateNodeWithGroupParams{
					UserID:     app.UserID,
					AppID:      app.AppID,
					TypeName:   typeName,
					GroupIDs:   ids,
					NodeFixIDs: []string{proxy.NodeFixID},
					Action:     bcm.GroupInstanceActionAdd,
					IgnoreErr:  isIgnore(bcm.GroupInstanceActionAdd),
				})
			}
		}
	}

	return params, nil
}

func getAppParams(ctx context.Context, app *vdbmodel.Application, groupIDs string, typeName,
	action string) (*bcm.OperateAppWithGroupParams, error) {

	ids, err := toIntArray(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	return &bcm.OperateAppWithGroupParams{
		UserID:    app.UserID,
		AppID:     app.AppID,
		TypeName:  typeName,
		GroupIDs:  ids,
		Action:    action,
		IgnoreErr: isIgnore(action),
	}, nil
}

func needAdd(param *iface.TaskParameters) bool {
	if param.BcmInstanceGroups == nil || len(param.BcmInstanceGroups) == 0 {
		return false
	}
	for _, group := range param.BcmInstanceGroups {
		if len(group.IDS) > 0 {
			return true
		}
	}
	return false
}

func standaloneAddToGroup(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters, action string) error {
	parameter, err := getClusterAddParamsAndFillMeta(ctx, app, param.BcmInstanceGroups[0].IDS,
		param.BcmInstanceGroups[0].Type, action)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shard_add_params error", logit.Error("error", err))
		return err
	}
	bcmOp := bcm.Instance()
	if err := bcmOp.OperateClusterWithGroup(ctx, parameter); err != nil {
		resource.LoggerTask.Warning(ctx, "add to bcm group fail", logit.Error("error", err))
		return err
	}
	return nil
}

func shardRemoveFromGroup(ctx context.Context, app *vdbmodel.Application, status string) error {

	parameters, err := getClusterRemoveParams(ctx, app, status)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shard_remove_params error", logit.Error("error", err))
		return err
	}
	bcmOp := bcm.Instance()

	for _, parameter := range parameters {
		if err := bcmOp.OperateClusterWithGroup(ctx, parameter); err != nil {
			resource.LoggerTask.Warning(ctx, "remove from bcm group fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func clusterAddToGroup(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters,
	action string, withinApp bool) error {

	bcmOp := bcm.Instance()

	for _, group := range param.BcmInstanceGroups {
		// 兼容前端可能传空数组的情况
		if len(group.IDS) == 0 {
			continue
		}
		if withinApp && (group.Type == TypeClusterForPegaDBCluster || group.Type == TypeClusterForRedisCluster) {
			appParam, err := getAppParams(ctx, app, group.IDS, group.Type, action)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get app params error", logit.Error("error", err))
				return err
			}
			if err := bcmOp.OperateAppWithGroup(ctx, appParam); err != nil {
				resource.LoggerTask.Warning(ctx, "add app to group fail", logit.Error("error", err))
				return err
			}
		}

		if group.Type == TypeShardForRedisCluster || group.Type == TypeShardForPegaDBCluster {
			shardParam, err := getClusterAddParamsAndFillMeta(ctx, app, group.IDS, group.Type, action)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get shard params fail", logit.Error("error", err))
				return err
			}
			// 热活场景从地域不需要添加,待热活场景从地域添加分片监控后再改造
			if len(shardParam.HashNames) == 0 {
				continue
			}
			if err = bcmOp.OperateClusterWithGroup(ctx, shardParam); err != nil {
				resource.LoggerTask.Warning(ctx, "add shard to group fail", logit.Error("error", err))
				return err
			}
		}

		if group.Type == TypeProxyForPegaDBCluster || group.Type == TypeProxyForRedisCluster {
			proxyParam, err := getProxyAddParamsAndFillMeta(ctx, app, group.IDS, group.Type, action)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get proxy params fail", logit.Error("error", err))
				return err
			}
			// 扩缩容场景proxy可能为0
			if len(proxyParam.NodeFixIDs) == 0 {
				continue
			}
			if err = bcmOp.OperateNodeWithGroup(ctx, proxyParam); err != nil {
				resource.LoggerTask.Warning(ctx, "add proxy to group fail", logit.Error("error", err))
				return err
			}
		}
	}
	return nil
}

func proxyAddToGroup(ctx context.Context, app *vdbmodel.Application, action string) error {

	bcmOp := bcm.Instance()
	m := make(map[string]string, 0)
	err := json.Unmarshal([]byte(app.BcmInstanceGroup), &m)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Unmarshal GroupIDs fail", logit.String("appId", app.AppID),
			logit.Error("error", err))
		return fmt.Errorf("unmarshal GroupIDs fail")
	}
	for gType, gIDs := range m {
		if gType == TypeProxyForRedisCluster || gType == TypeProxyForPegaDBCluster {
			proxyParam, err := getProxyAddParamsAndFillMeta(ctx, app, gIDs, gType, action)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get proxy params fail", logit.Error("error", err))
				return err
			}
			if err = bcmOp.OperateNodeWithGroup(ctx, proxyParam); err != nil {
				resource.LoggerTask.Warning(ctx, "add proxy to group fail", logit.Error("error", err))
				return err
			}
		}
	}
	return nil
}

func proxyRollbackRemoveFromGroup(ctx context.Context, app *vdbmodel.Application, status string) error {

	bcmOp := bcm.Instance()

	proxyParams, err := getRollbackProxyRemoveParams(ctx, app, status)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy remove params fail", logit.Error("error", err))
		return err
	}
	for _, proxyParam := range proxyParams {
		if err = bcmOp.OperateNodeWithGroup(ctx, proxyParam); err != nil {
			resource.LoggerTask.Warning(ctx, "remove proxy from group fail", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func proxyRemoveFromGroup(ctx context.Context, app *vdbmodel.Application, status string) error {

	bcmOp := bcm.Instance()

	proxyParams, err := getProxyRemoveParams(ctx, app, status)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app params fail", logit.Error("error", err))
		return err
	}
	for _, proxyParam := range proxyParams {
		if err = bcmOp.OperateNodeWithGroup(ctx, proxyParam); err != nil {
			resource.LoggerTask.Warning(ctx, "remove from group fail", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func appRemoveFromGroup(ctx context.Context, app *vdbmodel.Application) error {

	if len(app.BcmInstanceGroup) == 0 {
		return nil
	}
	groupMap, err := toMap(ctx, app.BcmInstanceGroup)
	if err != nil {
		return err
	}
	bcmOp := bcm.Instance()
	for typeName, groupIDs := range groupMap {
		if typeName != TypeClusterForPegaDBCluster && typeName != TypeClusterForRedisCluster {
			continue
		}
		appParam, err := getAppParams(ctx, app, groupIDs, typeName, bcm.GroupInstanceActionRemove)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get app params fail", logit.Error("error", err))
			return err
		}
		if err := bcmOp.OperateAppWithGroup(ctx, appParam); err != nil {
			resource.LoggerTask.Warning(ctx, "add app to group fail", logit.Error("error", err))
			return err
		}
	}
	return nil
}

// ProcessClusterAddToGroup 企业版实例添加Group
func ProcessClusterAddToGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if !needAdd(param) {
		return nil
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	for _, group := range param.BcmInstanceGroups {
		if group.Type != TypeClusterForRedisCluster && group.Type != TypeProxyForRedisCluster &&
			group.Type != TypeShardForRedisCluster {
			resource.LoggerTask.Warning(ctx, "bcm group type is wrong", logit.Error("error", err),
				logit.String("type", group.Type), logit.String("app", app.AppID))
			return fmt.Errorf("BcmInstanceGroups.Type is wrong:%s", group.Type)
		}
	}

	err = clusterAddToGroup(ctx, app, param, bcm.GroupInstanceActionAdd, true)
	if err != nil {
		return err
	}

	m := make(map[string]string, 0)
	for _, group := range param.BcmInstanceGroups {
		m[group.Type] = group.IDS
	}

	groupBytes, err := json.Marshal(m)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Marshal GroupIDs fail", logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return fmt.Errorf("marshal GroupIDs fail")
	}

	app.BcmInstanceGroup = string(groupBytes)
	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

// ProcessRollbackClusterAddToGroup 回滚企业版实例添加Group
func ProcessRollbackClusterAddToGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	err = appRemoveFromGroup(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "appRemoveFromGroup fail", logit.Error("error", err))
		return err
	}

	err = shardRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "shardRemoveFromGroup fail", logit.Error("error", err))
		return err
	}

	err = proxyRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "proxyRemoveFromGroup fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessDeleteAllRemoveFromGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if err := shardRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusInUse); err != nil {
		resource.LoggerTask.Warning(ctx, "remove shard from group fail", logit.Error("error", err))
		return err
	}

	if err := proxyRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusInUse); err != nil {
		resource.LoggerTask.Warning(ctx, "remove proxy from group fail", logit.Error("error", err))
		return err
	}
	if err := appRemoveFromGroup(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "remove app from group fail", logit.Error("error", err))
		return err
	}

	return nil
}

// ProcessShardsAddToGroup 扩容分片添加实例组
func ProcessShardsAddToGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if !needAdd(param) {
		return nil
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	for _, group := range param.BcmInstanceGroups {
		if group.Type != TypeProxyForRedisCluster && group.Type != TypeShardForRedisCluster {
			resource.LoggerTask.Warning(ctx, "bcm group type is wrong", logit.Error("error", err),
				logit.String("type", group.Type), logit.String("app", app.AppID))
			return fmt.Errorf("BcmInstanceGroups.Type is wrong:%s", group.Type)
		}
	}

	err = clusterAddToGroup(ctx, app, param, bcm.GroupInstanceActionAdd, false)
	if err != nil {
		return err
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

// ProcessRollbackShardsAddToGroup 回滚扩容分片添加实例组
func ProcessRollbackShardsAddToGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	err = shardRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "shardRemoveFromGroup fail", logit.Error("error", err))
		return err
	}

	err = proxyRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "proxyRemoveFromGroup fail", logit.Error("error", err))
		return err
	}

	return nil
}

// ProcessShardsRemoveFromGroup 删除分片
func ProcessShardsRemoveFromGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	err = shardRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToDelete)
	if err != nil {
		return err
	}

	err = proxyRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToDelete)
	if err != nil {
		return err
	}

	return nil
}

func toDeleteProxyHasGroup(app *vdbmodel.Application) bool {

	hasGroup := false
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToDelete {
				continue
			}
			if len(itf.BcmInstanceGroup) != 0 {
				hasGroup = true
			}
		}
	}
	return hasGroup
}

func toCreateProxyMaybeNeedAdd(app *vdbmodel.Application) bool {

	needAdd := false
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			if len(app.BcmInstanceGroup) != 0 {
				needAdd = true
			}
		}
	}
	return needAdd
}

// ProcessProxyAddOrRemoveFromBcmGroup 企业版Proxy添加Group,或删除proxy
func ProcessProxyAddOrRemoveFromBcmGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if toCreateProxyMaybeNeedAdd(app) {
		err := proxyAddToGroup(ctx, app, bcm.GroupInstanceActionAdd)
		if err != nil {
			return err
		}
		err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
				logit.Error("dbError", err))
			return err
		}
	}

	if toDeleteProxyHasGroup(app) {
		err := proxyRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToDelete)
		if err != nil {
			return err
		}
	}

	return nil
}

// ProcessRollbackProxyAddOrRemoveFromBcmGroup 回滚企业版Proxy添加Group,或删除proxy
func ProcessRollbackProxyAddOrRemoveFromBcmGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if toCreateProxyMaybeNeedAdd(app) {
		err := proxyRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "proxyRemoveFromGroup fail", logit.Error("error", err))
			return err
		}
	}

	if toDeleteProxyHasGroup(app) {
		err := proxyRollbackRemoveFromGroup(ctx, app, vdbmodel.NodeOrProxyStatusToDelete)
		if err != nil {
			return err
		}
	}

	return nil
}
