/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"errors"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

/*
2024-03-26 datanode和master一次只允许自愈一个，避免同时自愈导致集群不可用
*/
func ProcessBuildMetaForClusterSelfHeal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "build selfhealth", logit.String("params :", base_utils.Format(param)))

	datanodeNum := 0
	masterNum := 0

	//vdb的master、proxy、data节点，都是注册为xmaster中的proxy节点类型
	clusterNodeIdsMap := make(map[string][]string)
	g := gtask.Group{}
	for _, ID := range param.SelfHealFromCsmaster.ProxyIDs {
		engine := util.GetEngineByID(ctx, app, ID)
		if engine == "" {
			resource.LoggerTask.Error(ctx, "engine get by id failed", logit.String("unhealthNode", ID))
			return errors.New("engine get by id failed")
		}

		switch engine {
		case vdbmodel.EngineVDBDataNode:
			node, cluster, err := FindNodeByIDInApp(ctx, app, ID)
			if err != nil {
				resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
				return err
			}

			if _, ok := clusterNodeIdsMap[cluster.ClusterID]; !ok {
				clusterNodeIdsMap[cluster.ClusterID] = make([]string, 0)
			}

			if datanodeNum >= MaxSelfHealDataNodeNum {
				resource.LoggerTask.Error(ctx, "too many unhealth data nodes", logit.String("unhealthNode", ID))
				continue
			}

			clusterNodeIdsMap[cluster.ClusterID] = append(clusterNodeIdsMap[cluster.ClusterID], node.NodeID)
			datanodeNum++

		case vdbmodel.EngineVDBProxy:
			proxy, err := FindProxyByIDInApp(ctx, app, ID)
			if err != nil {
				resource.LoggerTask.Error(ctx, "proxy not found", logit.Error("error", err))
				return err
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					resource.LoggerTask.Notice(ctx, "mark proxy to delete", logit.String("proxy", proxy.ProxyID))
					proxy.Status = vdbmodel.NodeOrProxyStatusToFakeDelete
					if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
						proxy.Status = vdbmodel.NodeOrProxyStatusToDelete
					}
					if err := vdbmodel.ProxysSave(ctx, []*vdbmodel.Proxy{proxy}); err != nil {
						resource.LoggerTask.Error(ctx, "save proxy error", logit.Error("error", err))
						return err
					}
					return nil
				})
			})

		case vdbmodel.EngineVDBMaster:
			master, err := FindMasterByIDInApp(ctx, app, ID)
			if err != nil {
				resource.LoggerTask.Error(ctx, "master not found", logit.Error("error", err))
				return err
			}
			if masterNum >= MaxSelfHealMasterNum {
				resource.LoggerTask.Error(ctx, "too many unhealth masters", logit.String("unhealthNode", ID))
				continue
			}

			g.Go(func() error {
				return gtask.NoPanic(func() error {
					resource.LoggerTask.Notice(ctx, "mark master to delete", logit.String("master", master.MasterID))
					master.Status = vdbmodel.NodeOrProxyStatusToFakeDelete
					if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
						master.Status = vdbmodel.NodeOrProxyStatusToDelete
					}
					if err := vdbmodel.MastersSave(ctx, []*vdbmodel.Master{master}); err != nil {
						resource.LoggerTask.Error(ctx, "save master error", logit.Error("error", err))
						return err
					}
					return nil
				})
			})
			masterNum++
		}
	}

	resource.LoggerTask.Trace(ctx, "Unhealth cluster NodeIds Map", logit.String("clusterNodeIdsMap", base_utils.Format(clusterNodeIdsMap)))

	for clusterID, nodeIds := range clusterNodeIdsMap {
		clusterID := clusterID
		nodeIds := nodeIds
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return markToDeleteNodeInCluster(ctx, clusterID, nodeIds)
			})
		})
	}

	if _, err = g.Wait(); err != nil {
		resource.LoggerTask.Error(ctx, "mark to delete node error", logit.Error("error", err))
		return err
	}

	app, err = vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := util.AddNewNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replacing failed", logit.Error("error", err))
		return err
	}
	if err := util.AddNewProxyForReplacing(ctx, app, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new proxy for replacing failed", logit.Error("error", err))
		return err
	}
	if err := util.AddNewMasterForReplacing(ctx, app, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new master for replacing failed", logit.Error("error", err))
		return err
	}
	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func markToDeleteNodeInCluster(ctx context.Context, clusterID string, nodeIds []string) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterID, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()

	resource.LoggerTask.Notice(ctx, "mark to delete node in cluster",
		logit.String("cluster", clusterID), logit.String("nodeIds", base_utils.Format(nodeIds)))
	cluster, err := vdbmodel.ClusterGetByClusterID(ctx, clusterID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster error", logit.Error("error", err))
		return err
	}

	for _, node := range cluster.Nodes {
		if in, _ := base_utils.InArray(node.NodeID, nodeIds); !in {
			resource.LoggerTask.Trace(ctx, "node not in list, skip", logit.String("node_id", node.NodeID))
			continue
		}
		resource.LoggerTask.Notice(ctx, "mark node to delete", logit.String("node", node.NodeID))
		node.Status = vdbmodel.NodeOrProxyStatusToFakeDelete
		if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
			node.Status = vdbmodel.NodeOrProxyStatusToDelete
		}
	}
	if err := vdbmodel.ClusterSave(ctx, []*vdbmodel.Cluster{cluster}); err != nil {
		resource.LoggerTask.Error(ctx, "save cluster error", logit.Error("error", err))
		return err
	}
	return nil
}
