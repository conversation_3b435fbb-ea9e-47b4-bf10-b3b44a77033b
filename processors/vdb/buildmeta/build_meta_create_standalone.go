/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/04/26, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
)

func createClustersForStandalone(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Clusters) > 0 {
		resource.LoggerTask.Warning(ctx, "clusters has been created")
		return nil
	}

	// only one cluster in standalone app, and one node in cluster
	for i := 0; i < param.DataNodeCount; i++ {
		app.Clusters = append(app.Clusters, &vdbmodel.Cluster{
			ClusterID:  app.AppID + "-c-" + strconv.Itoa(i),
			AppID:      app.AppID,
			Engine:     vdbmodel.EngineVDBDataNode,
			Port:       param.DataNodePort,
			Status:     vdbmodel.NodeOrProxyStatusInUse,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
			DestSpec:   param.DataNodeSpec,
			StoreType:  vdbmodel.StoreTypeDRAM,
		})
	}
	maxNodeIdx := 0
	zoneCount := len(param.Replicas)
	zoneIdx := 0

	for _, cluster := range app.Clusters {
		replica := param.Replicas[zoneIdx%zoneCount]

		azone, found := zoneMapper(replica.Zone, true)
		if !found {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
			return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
		}

		cluster.MaxNodeIndex = maxNodeIdx

		cluster.Nodes = append(cluster.Nodes, &vdbmodel.Node{
			NodeID:     app.AppID + "-n-" + strconv.Itoa(maxNodeIdx),
			ClusterID:  cluster.ClusterID,
			AppID:      app.AppID,
			Engine:     cluster.Engine,
			Port:       cluster.Port,
			Region:     param.Region,
			LogicZone:  replica.Zone,
			Azone:      azone,
			VpcID:      param.VPCID,
			SubnetID:   replica.SubnetID,
			XagentPort: vdbmodel.DefaultXagentPort,
			Status:     vdbmodel.NodeOrProxyStatusToCreate,
			Basedir:    util.DefaultBaseDir,
			NodeFixID:  app.AppID + "-n-" + strconv.Itoa(maxNodeIdx),
		})
		maxNodeIdx++
		zoneIdx++
	}
	return nil
}

func fillAppStandalone(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	app.ImageID = bccresource.GetBccImageId()

	app.Type = vdbmodel.AppTypeStandalone
	app.Product = vdbmodel.ProductVDB
	app.DeploySetIDs = strings.Join(param.DeployIDSet, ",")
	app.InnerPort = param.DataNodePort
	app.Region = param.Region
	version, err := GetDeployConfVersion(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get deploy conf failed", logit.Error("error", err))
		return err
	}

	app.ImageID += ":" + version
	// 包管理用自己的镜像，基于centos8
	// https://console.cloud.baidu-int.com/devops/icode/repos/baidu/scs/scs-image/tree/x1_pkg_manager_centos8
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		app.ImageID = conf.PkgManConf.Image.DockerImage
	} else {
		app.ImageID = conf.PkgManConf.Image.Bcc
	}

	return nil
}

func ProcessBuildMetaForCreatingStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "build create meta", logit.String("params :", base_utils.Format(param)))

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := fillAppStandalone(ctx, app, param); err != nil {
		resource.LoggerTask.Error(ctx, "fillAppStandalone failed", logit.Error("error", err))
		return err
	}

	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	if err := createClustersForStandalone(ctx, app, param, zoneMapper); err != nil {
		resource.LoggerTask.Error(ctx, "createClusters failed", logit.Error("error", err))
		return err
	}

	if err := createBlbModels(ctx, app, param); err != nil {
		resource.LoggerTask.Error(ctx, "createBlbModels failed", logit.Error("error", err))
		return err
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}
