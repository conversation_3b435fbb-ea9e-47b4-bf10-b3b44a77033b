/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessAddNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type == vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to add node in standalone mode")
		return nil
	}

	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}
	//对所有的新建datanode节点进行添加
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}

			req := mochow.AddDataNodeRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
				AZName: node.Azone,
			}
			rsp, err := mochow.AddDataNode(ctx, masterServer, &req)
			if err != nil {
				resource.LoggerTask.Error(ctx, "add data node failed", logit.Error("error", err))
				return err
			}

			if rsp.Status.Code != 0 {
				if rsp.Status.Code == 8004 {
					resource.LoggerTask.Trace(ctx, "data node already exist", logit.Int32("status_code", rsp.Status.Code),
						logit.String("status_msg", rsp.Status.Msg))
				} else {
					resource.LoggerTask.Error(ctx, "add data node failed", logit.Int32("status_code", rsp.Status.Code))
					return fmt.Errorf(rsp.Status.Msg)
				}
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}

		req := mochow.AddMasterPeerRequest{
			Token: "default_token",
			MasterAddr: mochow.NodeID{
				IP:   int32(util.LittleEndianInetAtoN(master.IP)),
				Port: int32(master.Port),
			},
		}
		rsp, err := mochow.AddMasterPeer(ctx, masterServer, &req)
		if err != nil {
			resource.LoggerTask.Error(ctx, "add master peer failed", logit.Error("error", err))
			return err
		}

		if rsp.Status.Code != 0 {
			resource.LoggerTask.Error(ctx, "add master peer failed", logit.Int32("status_code", rsp.Status.Code))
			return fmt.Errorf(rsp.Status.Msg)
		}
	}

	return nil
}
