
InstanceTypesResizeAllowed=["N0"]
ForbidLiveResize=false
OrderOverTime=120
MasterEndpoint="vdb.agilecloud.com"
BccImageId="72e519cd-c9d4-43f1-9851-a5317640eb4b"
BccImageIdForIpv6="b5b8bf65-2155-4ee9-b73c-f74674f3d61e"
BccImageIdForPegaDB="76704f12-3520-4e7a-81dc-f0c1f3c2365c"
SubOrderSize=8

[[DefaultConfigList]]
AZone="AZONE-bjrs"
StoreType="DRAM"
DefaultInstanceType="N5"
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-bjlsh"
StoreType="DRAM"
DefaultInstanceType="N5"
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-bdbl"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-fsh"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-gzhxy"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-gzns"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-gznj"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-szth"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-szwg"
StoreType="DRAM"
DefaultInstanceType="N5"
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-wxtky"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-njjs"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="*"
StoreType="DRAM"
DefaultInstanceType="N6"
AlternativeInstanceTypes=["N5"]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="*"
StoreType="AEP"
DefaultInstanceType="AEP"
AlternativeInstanceTypes=[]
RootDiskStorageType="hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=1
CpuList=[1]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=2
CpuList=[1, 2]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=4
CpuList=[1, 2, 4]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=8
CpuList=[1, 2, 4, 8]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=12
CpuList=[2, 4, 8, 12]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=16
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=32
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=48
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=64
CpuList=[2, 4, 8, 12, 16, 32]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=96
CpuList=[2, 4, 24, 48]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=128
CpuList=[2, 4, 16, 24, 32, 48, 64]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=256
CpuList=[32, 64, 128]

[IDC_SPEC.bjtest]
BccImageId = "c571a8fa-8426-413e-882d-9ffb526ca262"
