package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessDelBccResources 删除bcc资源
// 1. 遍历所有node、proxy，获取所有bcc
// 2. 调用bcc接口删除资源，如果node volumeId不为空，则删除volume
func ProcessDelBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	delBccReq := &bccresource.DeleteBccResourceParams{
		UserID:    app.UserID,
		IsShortID: false,
	}

	volumeIds := make([]string, 0)

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			delBccReq.InstanceIds = append(delBccReq.InstanceIds, node.ResourceID)
			if node.VolumeID != "" {
				volumeIds = append(volumeIds, node.VolumeID)
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			delBccReq.InstanceIds = append(delBccReq.InstanceIds, proxy.ResourceID)
		}
	}

	for _, master := range app.Masters {
		delBccReq.InstanceIds = append(delBccReq.InstanceIds, master.ResourceID)
	}

	resource.LoggerTask.Notice(ctx, "send delbcc", logit.String("raw params", base_utils.Format(delBccReq)))
	err = bccresource.BccResourceOp().DeleteBccResource(ctx, delBccReq)

	if err != nil {
		resource.LoggerTask.Error(ctx, "delete resource failed", logit.Error("error", err))
	}

	if len(volumeIds) > 0 {
		resource.LoggerTask.Notice(ctx, "send del cds", logit.String("volume ids", base_utils.Format(volumeIds)))
		for _, volumeID := range volumeIds {
			err = bccresource.BccResourceOp().DeleteCds(ctx, &bccresource.DeleteCdsParam{
				VolumeID:       volumeID,
				UserID:         app.UserID,
				AutoSnapshot:   "on",
				ManualSnapshot: "on",
				Recycle:        "off",
			})
			if err != nil {
				resource.LoggerTask.Error(ctx, "delete cds failed", logit.String("volumeID", volumeID),
					logit.Error("error", err))
			}
		}
	}

	return nil
}
