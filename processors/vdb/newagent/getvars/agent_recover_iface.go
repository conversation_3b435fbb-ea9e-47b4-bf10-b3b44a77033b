package getvars

type ID struct {
	UUID   string `json:"uuid"`
	UserID string `json:"user_id"`
	AppID  string `json:"appid"`
	NodeID string `json:"node_id"`
}

// DataNodeConf
// recover/datanode_conf.txt
type DataNodeConf struct {
	InstanceType string `json:"instance_type"`
	AppID        string `json:"appid"`
	ClusterID    string `json:"cluster_id"`
	NodeShowID   string `json:"node_show_id"`
	Port         int    `json:"port"`
}

// ProxyConf
// recover/proxy_conf.txt
type ProxyConf struct {
	InstanceType string `json:"instance_type"`
	AppID        string `json:"appid"`
	NodeShowID   string `json:"node_show_id"`
	ProxyID      string `json:"proxy_id"`
	Port         int    `json:"port"`
}

// ProxyConf
// recover/master_conf.txt
type MasterConf struct {
	InstanceType string `json:"instance_type"`
	AppID        string `json:"appid"`
	NodeShowID   string `json:"node_show_id"`
	MasterID     string `json:"master_id"`
	Port         int    `json:"port"`
}

type InstanceMetadata struct {
	Port         int    `json:"port"`
	Type         string `json:"type"`
	ProcName     string `json:"proc_name"`
	ResourceType string `json:"resource_type"`
	IsPrivate    bool   `json:"is_private"`
	PrivateType  string `json:"private_type"`
}

type BcmConf struct {
	Cycle         int `json:"cycle"`
	RefactoringV1 int `json:"refactoring_v1"`
}

type Blb struct {
	ElbIP   string `json:"elb_ip"`
	ElbPort int    `json:"elb_port"`
}

type Monitor struct {
	HashName        string `json:"hash_name"`
	HashID          string `json:"hash_id"`
	Version         int    `json:"version"`
	StoreType       int    `json:"store_type"`
	NoahBns         string `json:"noah_bns"`
	NoahEndpoint    string `json:"noah_endpoint"`
	AllowPush       bool   `json:"forbid_push"`
	ProxyInstanceID int    `json:"proxy_instance_id"`
	FixIP           string `json:"fix_ip"`
}

type AgentRecovers struct {
	ID               *ID               `json:"id"`
	DataNodeConf     *DataNodeConf     `json:"datanode_conf,omitempty"`
	ProxyConf        *ProxyConf        `json:"proxy_conf,omitempty"`
	MasterConf       *MasterConf       `json:"master_conf,omitempty"`
	InstanceMetadata *InstanceMetadata `json:"instance_metadata"`
	BcmConf          *BcmConf          `json:"bcm_conf"`
	Blb              *Blb              `json:"blb"`
	Monitor          *Monitor          `json:"monitor"`
}
