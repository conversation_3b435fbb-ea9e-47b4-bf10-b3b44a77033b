/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessDropNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type == vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to drop node in standalone mode")
		return nil
	}

	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	//对所有的新建master节点、datanode节点进行删除操作
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToDelete && node.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}

			req := mochow.DropDataNodeRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
			}
			rsp, err := mochow.DropDataNode(ctx, masterServer, &req)
			if err != nil {
				resource.LoggerTask.Error(ctx, "drop data node failed", logit.Error("error", err))
				return err
			}

			//8051为节点不存在
			if rsp.Status.Code == 8051 {
				resource.LoggerTask.Error(ctx, "data node is not found", logit.String("node_info", base_utils.Format(rsp)))
				continue
			}

			if rsp.Status.Code != 0 {
				resource.LoggerTask.Error(ctx, "drop data node failed", logit.Int32("status_code", rsp.Status.Code))
				return fmt.Errorf(rsp.Status.Msg)
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToDelete && master.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
			continue
		}

		req := mochow.DropMasterPeerRequest{
			Token: "default_token",
			MasterAddr: mochow.NodeID{
				IP:   int32(util.LittleEndianInetAtoN(master.IP)),
				Port: int32(master.Port),
			},
		}
		rsp, err := mochow.DropMasterPeer(ctx, masterServer, &req)
		if err != nil {
			resource.LoggerTask.Error(ctx, "drop master peer failed", logit.Error("error", err))
			return err
		}

		if rsp.Status.Code != 0 {
			resource.LoggerTask.Error(ctx, "drop master peer failed", logit.Int32("status_code", rsp.Status.Code))
			return fmt.Errorf(rsp.Status.Msg)
		}
	}

	return nil
}
