/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
解析parameter中的NodeType，将具体规格填入Cluster表/Interface表
*/

package specification

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessFillSpec 解析parameter中的NodeType，将具体规格填入Cluster表/Interface表
// 1. 从x1-base的spec-conf中获取NodeType的具体规格，包括cpu、mem、root/data disk、instanceType
// 2. 将规格填入到cluster表\interface表\master表中（表中有规格相关的字段，供后续创建资源使用)；
func ProcessFillSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get parameters error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			param := &specification.GetSpecificationParams{
				UserID:    app.UserID,
				Name:      cluster.DestSpec,
				Engine:    cluster.Engine,
				StoreType: cluster.StoreType,
				AppType:   app.Type,
			}
			spec, err := specification.GetSpecification(ctx, param)
			if err != nil {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
				return err
			}
			cluster.AvailableVolume = spec.AvailableVolume
			cluster.CPU = spec.CPUCount

			cluster.MemSize = spec.MemoryCapacityInGB

			cluster.SysDiskSize = spec.RootDiskCapacityInGB
			cluster.DiskSize = int64(spec.DataDiskCapacityInGB)
		}
		for _, fspec := range params.ForceSpecs {
			if fspec.Engine == cluster.Engine {
				if fspec.CPU > 0 {
					cluster.CPU = fspec.CPU
				}
				if fspec.Memory > 0 {
					cluster.MemSize = fspec.Memory
				}
				if fspec.SysDisk > 0 {
					cluster.SysDiskSize = fspec.SysDisk
				}
				if fspec.DataDisk > 0 {
					cluster.DiskSize = int64(fspec.DataDisk)
				}
				if fspec.Volume > 0 {
					cluster.AvailableVolume = fspec.Volume
				}
				break
			}
		}
	}

	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			param := &specification.GetSpecificationParams{
				UserID:    app.UserID,
				Name:      itf.DestSpec,
				Engine:    itf.Engine,
				StoreType: itf.StoreType,
				AppType:   app.Type,
			}
			spec, err := specification.GetSpecification(ctx, param)
			if err != nil {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
				return err
			}
			itf.AvailableVolume = spec.AvailableVolume
			itf.CPU = spec.CPUCount
			itf.MemSize = spec.MemoryCapacityInGB
			itf.SysDiskSize = spec.RootDiskCapacityInGB
			itf.DiskSize = int64(spec.DataDiskCapacityInGB)
		}
		for _, fspec := range params.ForceSpecs {
			if fspec.Engine == itf.Engine {
				if fspec.CPU > 0 {
					itf.CPU = fspec.CPU
				}
				if fspec.Memory > 0 {
					itf.MemSize = fspec.Memory
				}
				if fspec.SysDisk > 0 {
					itf.SysDiskSize = fspec.SysDisk
				}
				if fspec.DataDisk > 0 {
					itf.DiskSize = int64(fspec.DataDisk)
				}
				if fspec.Volume > 0 {
					itf.AvailableVolume = fspec.Volume
				}
				break
			}
		}
	}

	for _, master := range app.Masters {
		if master.DestSpec != master.Spec {
			param := &specification.GetSpecificationParams{
				UserID:    app.UserID,
				Name:      master.DestSpec,
				Engine:    master.Engine,
				StoreType: master.StoreType,
				AppType:   app.Type,
			}
			spec, err := specification.GetSpecification(ctx, param)
			if err != nil {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
				return err
			}

			resource.LoggerTask.Trace(ctx, "Get master Specification", logit.String("param :", base_utils.Format(param)),
				logit.String("spec :", base_utils.Format(spec)))

			master.CPU = spec.CPUCount
			master.MemSize = spec.MemoryCapacityInGB
			master.SysDiskSize = spec.RootDiskCapacityInGB
			master.DiskSize = int64(spec.DataDiskCapacityInGB)
		}
		for _, fspec := range params.ForceSpecs {
			if fspec.Engine == master.Engine {
				if fspec.CPU > 0 {
					master.CPU = fspec.CPU
				}
				if fspec.Memory > 0 {
					master.MemSize = fspec.Memory
				}
				if fspec.SysDisk > 0 {
					master.SysDiskSize = fspec.SysDisk
				}
				if fspec.DataDisk > 0 {
					master.DiskSize = int64(fspec.DataDisk)
				}
				if fspec.Volume > 0 {
					master.AvailableVolume = fspec.Volume
				}

				break
			}
		}
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCommitSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	nodeSpec := app.NodeSpec
	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
			if nodeSpec != cluster.Spec {
				nodeSpec = cluster.Spec
			}
		}
		cluster.ActualCPU = cluster.CPU
		cluster.ActualMemSize = cluster.MemSize
		cluster.ActualDiskSize = int(cluster.DiskSize)
	}
	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			itf.Spec = itf.DestSpec
		}
		itf.ActualCPU = itf.CPU
		itf.ActualMemSize = itf.MemSize
		itf.ActualDiskSize = int(itf.DiskSize)
	}

	if app.NodeSpec != nodeSpec {
		app.NodeSpec = nodeSpec
	}

	for _, fspec := range params.ForceSpecs {
		if fspec.DataDisk > 0 {
			app.DiskFlavor = fspec.DataDisk
		}
		break
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessRollbackSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			cluster.DestSpec = cluster.Spec
		}
		cluster.CPU = cluster.ActualCPU
		cluster.MemSize = cluster.ActualMemSize
		cluster.DiskSize = int64(cluster.ActualDiskSize)
	}
	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			itf.DestSpec = itf.Spec
		}
		itf.CPU = itf.ActualCPU
		itf.MemSize = itf.ActualMemSize
		itf.DiskSize = int64(itf.ActualDiskSize)
	}
	for _, master := range app.Masters {
		if master.DestSpec != master.Spec {
			master.DestSpec = master.Spec
		}
		master.CPU = master.ActualCPU
		master.MemSize = master.ActualMemSize
		master.DiskSize = int64(master.ActualDiskSize)
	}
	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
