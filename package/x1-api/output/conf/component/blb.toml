[BLBConf]
    HealthCheck = "TCP"
    HealthCheckTimeoutInSecond = 3
    HealthCheckUpRetry = 3
    HealthCheckDownRetry = 3
    HealthCheckIntervalInSecond = 3
    Internal = "true"
    Product = "vdb"

[BLBListenerConf]
    Type                          = "TCP"
    ## 绑定后端server端口
    DefaultBackendPort            = 8000
    ## 健康检查端口
    HealthCheckPort               = 8000
    ## 健康检查超时时间
    HealthCheckTimeoutInSecond    = 2
    ## 健康检查上行重试次数
    HealthCheckUpRetry            = 3
    ## 健康检查下行重试次数
    HealthCheckDownRetry          = 3
    ## 健康检查周期
    HealthCheckIntervalInSecond   = 1
    ## 调度策略
    Scheduler                     = "RoundRobin"
    TcpSessionTimeoutInMiliSecond = 4000

[BLBTCPListenerConf]
    TCPSessionTimeout = 900

[BLBHTTPListenerConf]
    TcpSessionTimeoutInMiliSecond = 4000
    KeepSession = false
    KeepSessionTimeout = 3600
    XForwardedFor = false
    XForwardedProto = false
    ServerTimeout = 30