/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* upgrade_standalone_app.go - the controller of upgrade standalone app  */
/*
modification history
--------------------
2024/08/27, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
执行节点升级

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"UpgradeParam":  ..
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/upgrade"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowUpgradeStandalone      = "vdb-upgrade-standalone-app"
	StepUpgradeStandaloneBuildMeta = "vdb-upgrade-standalone-app-build-meta"
	StepUpgradeStandaloneCloseHa   = "vdb-upgrade-standalone-app-close-ha"
	StepUpgradeStandaloneUpgrade   = "vdb-upgrade-standalone-upgrade"
	StepUpgradeStandaloneCheck     = "vdb-upgrade-standalone-check"
	StepUpgradeStandaloneRecoverHa = "vdb-upgrade-standalone-app-recover-ha"
	StepUpgradeStandaloneCallback  = "vdb-upgrade-standalone-cb"

	StepUpgradeStandaloneRollback          = "vdb-upgrade-standalone-app-rollback"
	StepUpgradeStandaloneRollbackRecoverHa = "vdb-upgrade-standalone-app-rollback-recover-ha"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// Step-0 构建元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneBuildMeta,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForUpgrade,
		SuccessNextStep: StepUpgradeStandaloneCloseHa,
		ErrorNextStep:   StepUpgradeStandaloneBuildMeta,
	})

	// Step-1 关闭HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneCloseHa,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     xmaster.ProcessCloseAppHaStatusInXmaster,
		SuccessNextStep: StepUpgradeStandaloneUpgrade,
		ErrorNextStep:   StepUpgradeStandaloneCloseHa,
	})

	// Step-2 执行节点的升级
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneUpgrade,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     upgrade.ProcessUpgradeStandalone,
		SuccessNextStep: StepUpgradeStandaloneCheck,
		ErrorNextStep:   StepUpgradeStandaloneUpgrade},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 检查节点状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneCheck,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     mochow.ProcessCheckDataNodeTablet,
		SuccessNextStep: StepUpgradeStandaloneRecoverHa,
		ErrorNextStep:   StepUpgradeStandaloneCheck},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-4 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneRecoverHa,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: StepUpgradeStandaloneCallback,
		ErrorNextStep:   StepUpgradeStandaloneRecoverHa,
	})

	// Step-5 更新成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneCallback,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     callback.ProcessUpgradeSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpgradeStandaloneCallback,
	})
	/////////////////////////////////////////////////////////
	// errStep-1 恢复集群和节点状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneRollback,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     callback.ProcessUpgradeRollbackMeta,
		SuccessNextStep: StepUpgradeStandaloneRollbackRecoverHa,
		ErrorNextStep:   StepUpgradeStandaloneRollback},

		workflow.WithStepTimeout(15*time.Minute))

	// errStep-2 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneRollbackRecoverHa,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepUpgradeStandaloneRollbackRecoverHa,
	})
}
