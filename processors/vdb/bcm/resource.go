package bcm

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bcm"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func createBcmAppResource(ctx context.Context, app *vdbmodel.Application, csClusterID int64) error {

	op := bcm.Instance()
	if err := op.CreateAppResource(ctx, &bcm.CreateAppResourceParams{
		UserID:     app.UserID,
		AppID:      app.AppID,
		AppShortID: csClusterID,
		AppType:    app.Type,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm app resource failed",
			logit.String("appId", app.AppID))
		return err
	}
	return nil
}

func deleteBcmAppResource(ctx context.Context, app *vdbmodel.Application) error {

	op := bcm.Instance()
	if err := op.DeleteAppResourceIfExisted(ctx, &bcm.DeleteAppResourceIfExistedParams{
		UserID: app.UserID,
		AppID:  app.AppID,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm app resource failed",
			logit.String("appId", app.AppID))
		return err
	}
	return nil
}

func getToCreateNodeResourceParams(ctx context.Context, app *vdbmodel.Application, shortID int64,
	nodeStatus string) ([]*bcm.CreateNodeResourceParams, error) {
	var createNodeParams []*bcm.CreateNodeResourceParams
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != nodeStatus {
				continue
			}
			createNodeParams = append(createNodeParams, &bcm.CreateNodeResourceParams{
				UserID:     app.UserID,
				AppID:      app.AppID,
				AppShortID: shortID,
				HashID:     node.ResourceID,
				NodeFixID:  node.NodeFixID,
			})
		}
	}

	for _, master := range app.Masters {
		if master.Status != nodeStatus {
			continue
		}
		createNodeParams = append(createNodeParams, &bcm.CreateNodeResourceParams{
			UserID:     app.UserID,
			AppID:      app.AppID,
			AppShortID: shortID,
			HashID:     master.ResourceID,
			NodeFixID:  master.NodeFixID,
		})
	}

	if app.Type == vdbmodel.AppTypeCluster {
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxies {
				if proxy.Status != nodeStatus {
					continue
				}
				createNodeParams = append(createNodeParams, &bcm.CreateNodeResourceParams{
					UserID:     app.UserID,
					AppID:      app.AppID,
					AppShortID: shortID,
					HashID:     proxy.ResourceID,
					NodeFixID:  proxy.NodeFixID,
				})
			}
		}
	}
	return createNodeParams, nil
}

func createBcmClusterOrNodeResource(ctx context.Context, app *vdbmodel.Application, shortID int64, nodeStatus string) error {
	op := bcm.Instance()

	createNodeParams, err := getToCreateNodeResourceParams(ctx, app, shortID, nodeStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get create bcm node resource params failed",
			logit.Error("error", err))
		return err
	}

	g := gtask.Group{
		Concurrent: 5,
	}
	for _, item := range createNodeParams {
		createParam := item
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return op.CreateNodeResource(ctx, createParam)
			})
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster resource fail", logit.Error("err", err))
		return err
	}
	return nil
}

func getToDeleteNodeResourceParams(ctx context.Context, app *vdbmodel.Application, nodeStatus string) (
	[]*bcm.DeleteNodeResourceIfExistedParams, error) {

	var deleteNodeParams []*bcm.DeleteNodeResourceIfExistedParams

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != nodeStatus {
				continue
			}
			deleteNodeParams = append(deleteNodeParams, &bcm.DeleteNodeResourceIfExistedParams{
				UserID:    app.UserID,
				AppID:     app.AppID,
				NodeFixID: node.NodeFixID,
			})
		}
	}

	for _, master := range app.Masters {
		if master.Status != nodeStatus {
			continue
		}
		deleteNodeParams = append(deleteNodeParams, &bcm.DeleteNodeResourceIfExistedParams{
			UserID:    app.UserID,
			AppID:     app.AppID,
			NodeFixID: master.NodeFixID,
		})
	}

	if app.Type == vdbmodel.AppTypeCluster {
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxies {
				if proxy.Status != nodeStatus {
					continue
				}
				deleteNodeParams = append(deleteNodeParams, &bcm.DeleteNodeResourceIfExistedParams{
					UserID:    app.UserID,
					AppID:     app.AppID,
					NodeFixID: proxy.NodeFixID,
				})
			}
		}
	}
	return deleteNodeParams, nil
}

func deleteBcmClusterOrNodeResource(ctx context.Context, app *vdbmodel.Application, nodeStatus string) error {
	op := bcm.Instance()

	deleteNodeParams, err := getToDeleteNodeResourceParams(ctx, app, nodeStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get delete bcm node resource params failed",
			logit.Error("err", err))
		return err
	}

	g := gtask.Group{
		Concurrent: 5,
	}
	for _, item := range deleteNodeParams {
		deleteParam := item
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return op.DeleteNodeResourceIfExisted(ctx, deleteParam)
			})
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm node resource fail", logit.Error("err", err))
		return err
	}
	return nil
}

// 创建实例时初始化监控对象
func ProcessCreateBcmResourceWithinApp(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if err := createBcmAppResource(ctx, app, int64(app.ID)); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm app resource failed", logit.String("appId", app.AppID))
		return err
	}
	if err := createBcmClusterOrNodeResource(ctx, app, int64(app.ID), vdbmodel.NodeOrProxyStatusToCreate); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster or node resource failed", logit.String("appId", app.AppID))
		return err
	}
	return nil
}

// 实例创建失败回滚监控对象
func ProcessRollbackBcmResourceWithinApp(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if err := deleteBcmAppResource(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm app resource failed", logit.String("appId", app.AppID))
		return err
	}
	if err = deleteBcmClusterOrNodeResource(ctx, app, vdbmodel.NodeOrProxyStatusToCreate); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm cluster or node resource failed", logit.String("appId", app.AppID))
		return err
	}
	return nil
}

// 实例释放时删除监控对象
func ProcessDeleteBcmResourceWithinApp(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if err := deleteBcmAppResource(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm app resource failed", logit.String("appId", app.AppID))
		return err
	}
	if err := deleteBcmClusterOrNodeResource(ctx, app, vdbmodel.NodeOrProxyStatusInUse); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster or node resource failed", logit.String("appId", app.AppID))
		return err
	}
	return nil
}
