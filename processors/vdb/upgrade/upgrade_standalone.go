/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/24
 * File: upgrade.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package upgrade TODO package function desc
package upgrade

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/deploy"
)

func ProcessUpgradeStandalone(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "start upgrade", logit.String("appId", app.AppID),
		logit.String("params :", base_utils.Format(param.UpgradeParam)))

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToUpgrade {
				if err := processUpgradeOneDataNode(ctx, app, cluster, node, param.UpgradeParam, teu.TaskID); err != nil {
					resource.LoggerTask.Warning(ctx, "upgrade node fail", logit.String("nodeId", node.NodeFixID))
					return err
				}
			}
		}
	}

	return nil
}

func processUpgradeOneDataNode(ctx context.Context, app *vdbmodel.Application, cluster *vdbmodel.Cluster,
	node *vdbmodel.Node, param *iface.UpgradeParam, taskID string) error {
	err := ExecuteUpgradeDatanode(ctx, app, cluster, node, param, taskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "upgrade node fail", logit.String("nodeId", node.NodeFixID), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "node upgrade suc", logit.String("appId", app.AppID), logit.String("clusterId", app.AppID),
		logit.String("nodeId", node.NodeID))

	return nil
}

func ExecuteUpgradeDatanode(ctx context.Context, app *vdbmodel.Application, cluster *vdbmodel.Cluster,
	node *vdbmodel.Node, upgradeParam *iface.UpgradeParam, taskID string) error {
	resource.LoggerTask.Trace(ctx, "start upgrade node", logit.String("appId", app.AppID),
		logit.String("clusterId", cluster.ClusterID), logit.String("nodeId", node.NodeID))
	TDE, err := deploy.GetTdeInfo(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get tde error", logit.Error("error", err))
		return err
	}
	deployParams := deploy.GetNodeDeployParams(app, cluster, node, TDE)
	deployParams.IsUpgrade = true
	deployParams.TaskID = taskID
	deployParams.NoUpgradeConf = upgradeParam.NoUpgradeConf
	return deploy.UpgradeNodeOfAllTypeNew(ctx, deployParams, upgradeParam)
}
