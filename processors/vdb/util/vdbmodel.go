/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/02/18
 * File: vdbmodel.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package util TODO package function desc
package util

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	DefaultBaseDir = "/home/<USER>"
)

const (
	GetNewNodeForReplacingAction = "replacing"
	GetNewNodeForModifyingAction = "modifying"
)

func GetHashName(clusterID string, appName string) string {
	clusterIDChunks := strings.Split(clusterID, "-")
	idx := clusterIDChunks[len(clusterIDChunks)-1]
	return appName + "_" + idx
}

func GetHashID(cluster *vdbmodel.Cluster) string {

	return ""
}

func GetResFlavor(cluster *vdbmodel.Cluster) string {
	return strings.Join([]string{
		strconv.Itoa(cluster.CPU),
		strconv.Itoa(cluster.MemSize * 1024),
		strconv.Itoa(cluster.SysDiskSize),
		strconv.Itoa(int(cluster.DiskSize)),
	}, "_")
}

func GetProxyResFlavor(itf *vdbmodel.Interface) string {
	return strings.Join([]string{
		strconv.Itoa(itf.CPU),
		strconv.Itoa(itf.ActualMemSize * 1024),
		strconv.Itoa(itf.SysDiskSize),
		strconv.Itoa(int(itf.DiskSize)),
	}, "_")
}

func checkNodeFixID(app *vdbmodel.Application, nodeFixID string, clusterID string) (bool, error) {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeFixID == nodeFixID {
				// 双保险，防止把其他分片的node影响
				if node.ClusterID != clusterID {
					return false, cerrs.Errorf("nodefixId %s clusterID is node match node %s", nodeFixID, node.NodeID)
				}
				switch node.Status {
				case vdbmodel.NodeOrProxyStatusToDelete, vdbmodel.NodeOrProxyStatusToFakeDelete:
					continue
				case vdbmodel.NodeOrProxyStatusToCreate:
					return false, nil
				default:
					return false, cerrs.Errorf("nodefixid %s is used by inuse node %s", nodeFixID, node.NodeID)
				}
			}
		}
	}
	return true, nil
}

func checkProxyFixID(app *vdbmodel.Application, proxyFixID string) (bool, error) {
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.NodeFixID == proxyFixID {
				switch proxy.Status {
				case vdbmodel.NodeOrProxyStatusToDelete, vdbmodel.NodeOrProxyStatusToFakeDelete:
					continue
				case vdbmodel.NodeOrProxyStatusToCreate:
					return false, nil
				default:
					return false, cerrs.Errorf("proxyfixid %s is used by inuse proxy %s", proxyFixID, proxy.ProxyID)
				}
			}
		}
	}
	return true, nil
}

func checkMasterFixID(app *vdbmodel.Application, masrerFixID string) (bool, error) {
	for _, master := range app.Masters {
		if master.NodeFixID == masrerFixID {
			switch master.Status {
			case vdbmodel.NodeOrProxyStatusToDelete, vdbmodel.NodeOrProxyStatusToFakeDelete:
				continue
			case vdbmodel.NodeOrProxyStatusToCreate:
				return false, nil
			default:
				return false, cerrs.Errorf("masrerFixID %s is used by inuse proxy %s", masrerFixID, master.MasterID)
			}

		}
	}
	return true, nil
}

func AddNewNodesForReplacing(ctx context.Context, app *vdbmodel.Application, action string, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserID, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	nodeIdx, err := GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node max index failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToDelete && node.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}
			nodeIdx++

			resource.LoggerTask.Notice(ctx, "add new node for replacing", logit.String("node_id", node.NodeID))

			needNew, err := checkNodeFixID(app, node.NodeFixID, node.ClusterID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check node fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of node %s had been created", node.NodeID))
				continue
			}

			newNode := &vdbmodel.Node{
				NodeID:     app.AppID + "-n-" + strconv.Itoa(nodeIdx),
				ClusterID:  cluster.ClusterID,
				AppID:      app.AppID,
				Engine:     cluster.Engine,
				Port:       cluster.Port,
				Region:     node.Region,
				LogicZone:  node.LogicZone,
				Azone:      node.Azone,
				Role:       "",
				VpcID:      node.VpcID,
				SubnetID:   node.SubnetID,
				Pool:       node.Pool,
				XagentPort: vdbmodel.DefaultXagentPort,
				Status:     vdbmodel.NodeOrProxyStatusToCreate,
				Basedir:    DefaultBaseDir,
				NodeFixID:  node.NodeFixID,
			}

			// 指定了子网，将新 node 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newNode.SubnetID = subnetID
				newNode.Azone = azone
				newNode.LogicZone = logicZone
			}
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("create new node %s replace of old node %s", newNode.NodeID, node.NodeID))
			cluster.Nodes = append(cluster.Nodes, newNode)
		}
	}
	return nil
}

func AddNewProxyForReplacing(ctx context.Context, app *vdbmodel.Application, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserID, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	proxyIdx, err := GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy max index failed", logit.Error("error", err))
		return err
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToDelete && proxy.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}
			proxyIdx++
			needNew, err := checkProxyFixID(app, proxy.NodeFixID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check proxy fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of proxy %s had been created", proxy.ProxyID))
				continue
			}

			newProxy := &vdbmodel.Proxy{
				ProxyID:     app.AppID + "-p-" + strconv.Itoa(proxyIdx),
				InterfaceID: itf.InterfaceID,
				AppID:       app.AppID,
				Engine:      itf.Engine,
				Port:        itf.Port,
				Region:      proxy.Region,
				LogicZone:   proxy.LogicZone,
				Azone:       proxy.Azone,
				VpcID:       proxy.VpcID,
				SubnetID:    proxy.SubnetID,
				XagentPort:  vdbmodel.DefaultXagentPort,
				Status:      vdbmodel.NodeOrProxyStatusToCreate,
				Basedir:     proxy.Basedir,
				StatPort:    22222,
				NodeFixID:   proxy.NodeFixID,
			}

			// 指定了子网，将新 proxy 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newProxy.SubnetID = subnetID
				newProxy.Azone = azone
				newProxy.LogicZone = logicZone
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("create new proxy %s replace of old proxy %s", newProxy.ProxyID, proxy.ProxyID))
			itf.Proxies = append(itf.Proxies, newProxy)
		}
	}
	return nil
}

func AddNewMasterForReplacing(ctx context.Context, app *vdbmodel.Application, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserID, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	masterIDx, err := GetMaxMasterIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master max index failed", logit.Error("error", err))
		return err
	}
	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToDelete && master.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
			continue
		}
		masterIDx++
		needNew, err := checkMasterFixID(app, master.NodeFixID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check master fix id failed", logit.Error("error", err))
			return err
		}
		if !needNew {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of master %s had been created", master.MasterID))
			continue
		}

		newMaster := &vdbmodel.Master{
			MasterID:   app.AppID + "-m-" + strconv.Itoa(masterIDx),
			AppID:      app.AppID,
			Engine:     vdbmodel.EngineVDBMaster,
			Port:       master.Port,
			Region:     master.Region,
			LogicZone:  master.LogicZone,
			Azone:      master.Azone,
			VpcID:      master.VpcID,
			SubnetID:   master.SubnetID,
			XagentPort: vdbmodel.DefaultXagentPort,
			Status:     vdbmodel.NodeOrProxyStatusToCreate,
			Basedir:    master.Basedir,
			DestSpec:   master.DestSpec,
			StoreType:  vdbmodel.StoreTypeDRAM,
			NodeFixID:  master.NodeFixID,
		}

		// 指定了子网，将新 master 的 subnetid/azone/logiczone 进行更换
		if len(subnetID) != 0 {
			newMaster.SubnetID = subnetID
			newMaster.Azone = azone
			newMaster.LogicZone = logicZone
		}
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("create new master %s replace of old master %s", newMaster.MasterID, master.MasterID))
		app.Masters = append(app.Masters, newMaster)

	}
	return nil
}

// FetchAllNodesOfCluster return all nodes of cluster include roNode
func FetchAllNodesOfCluster(ctx context.Context, cluster *vdbmodel.Cluster) []*vdbmodel.Node {
	nodes := make([]*vdbmodel.Node, 0)
	for _, node := range cluster.Nodes {
		nodes = append(nodes, node)
	}

	return nodes
}

func GetEngineVersion(app *vdbmodel.Application) string {
	engineVersion := ""
	for _, cluster := range app.Clusters {
		engineVersion = cluster.EngineVersion
		if len(engineVersion) != 0 {
			break
		}
	}
	return engineVersion
}

func GetEngine(app *vdbmodel.Application) string {
	engine := ""
	for _, cluster := range app.Clusters {
		engine = cluster.Engine
		break
	}
	return engine
}

func LockForVdbModeModify(ctx context.Context, appID string) (func(), error) {
	resource.LoggerTask.Trace(ctx, "VdbMode try to lock", logit.String("appID", appID))
	unlock, err := lock.BlockLock(ctx, "save_app_"+appID, 30*time.Second, 30*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "VdbMode lock fail", logit.String("appID", appID),
			logit.Error("lockError", err))
		return nil, err
	}
	resource.LoggerTask.Trace(ctx, "VdbMode lock success", logit.String("appID", appID))
	return func() {
		resource.LoggerTask.Trace(ctx, "VdbMode unlock", logit.String("appID", appID))
		unlock()
	}, err
}

func GetRedisCryptedPassword(ctx context.Context, app *vdbmodel.Application) (string, error) {
	password := ""
	return password, nil

}
