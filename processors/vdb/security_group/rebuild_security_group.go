/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建虚机安全组、初始化安全组规则
*/

package securitygroup

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func processRebuildSecurityGroupClusterLocal(ctx context.Context, app *vdbmodel.Application) error {
	ipList := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			ipList = append(ipList, node.IP)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			ipList = append(ipList, proxy.IP)
		}
	}

	for _, master := range app.Masters {
		ipList = append(ipList, master.IP)
	}

	if len(app.InternalSecurityGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "no InternalSecurityGroupId skip")
		return nil
	}

	var innerPorts []int32
	innerPorts = GetSecurityGrouptInnerPorts(ctx, app)
	err := security_group_openapi.Instance().ReopenSecurityGroupRules(ctx, &security_group_openapi.ReopenSecurityGroupRulesParams{
		SecurityGroupID: app.InternalSecurityGroupID,
		ServicePorts:    innerPorts,
		WhitelistIPs:    ipList,
		UserID:          app.UserID,
		IsEnableIpv6:    app.IPType == vdbmodel.Ipv6,
	})

	if err != nil {
		resource.LoggerTask.Warning(ctx, "reopen sg fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessRebuildSecurityGroupCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() {
		return nil
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}

	return processRebuildSecurityGroupClusterLocal(ctx, app)

}
