/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* build_meta_upgrade.go   */
/*
modification history
--------------------
2024/08/28, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
	build meta for upgrade
*/

package buildmeta

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessBuildMetaForUpgrade(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusUpgrading

	if app.Type == vdbmodel.AppTypeStandalone {
		return processBuildMetaForClusterUpgrade(ctx, app)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.UpgradeParam.Type == vdbmodel.UpgradeTypeApp {
		return processBuildMetaForClusterUpgrade(ctx, app)
	} else if param.UpgradeParam.Type == vdbmodel.UpgradeTypeEngine {
		return processBuildMetaForEngineUpgrade(ctx, app, param)
	} else if param.UpgradeParam.Type == vdbmodel.UpgradeTypeNode {
		return processBuildMetaForNodeUpgrade(ctx, app, param)
	}

	return errors.New("not support upgrade type")
}

func processBuildMetaForClusterUpgrade(ctx context.Context, app *vdbmodel.Application) error {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node.Status = vdbmodel.NodeOrProxyStatusToUpgrade
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			proxy.Status = vdbmodel.NodeOrProxyStatusToUpgrade
		}
	}

	for _, master := range app.Masters {
		master.Status = vdbmodel.NodeOrProxyStatusToUpgrade
	}

	err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppID),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func processBuildMetaForEngineUpgrade(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	updateEngine := param.UpgradeParam.Engine

	if updateEngine == vdbmodel.EngineVDBDataNode {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				node.Status = vdbmodel.NodeOrProxyStatusToUpgrade
			}
		}
	} else if updateEngine == vdbmodel.EngineVDBProxy {
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxies {
				proxy.Status = vdbmodel.NodeOrProxyStatusToUpgrade
			}
		}
	} else if updateEngine == vdbmodel.EngineVDBMaster {
		for _, master := range app.Masters {
			master.Status = vdbmodel.NodeOrProxyStatusToUpgrade
		}
	} else {
		resource.LoggerTask.Warning(ctx, "error engine type", logit.String("appId", app.AppID))
		return nil
	}

	err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppID),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func processBuildMetaForNodeUpgrade(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	find := false

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.UpgradeParam.NodeID {
				node.Status = vdbmodel.NodeOrProxyStatusToUpgrade
				find = true
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.ProxyID == param.UpgradeParam.NodeID {
				proxy.Status = vdbmodel.NodeOrProxyStatusToUpgrade
				find = true
			}
		}
	}

	for _, master := range app.Masters {
		if master.MasterID == param.UpgradeParam.NodeID {
			master.Status = vdbmodel.NodeOrProxyStatusToUpgrade
			find = true
		}
	}

	if !find {
		resource.LoggerTask.Warning(ctx, "node not found", logit.String("appId", app.AppID),
			logit.String("nodeId", param.UpgradeParam.NodeID))
		return errors.New("node not found")
	}

	err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppID),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForUpgradeOneMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	find := false

OutLoop:
	for _, master := range app.Masters {
		if master.MasterID == param.TargetNodeID && master.Status == vdbmodel.NodeOrProxyStatusToUpgrade {
			master.Status = vdbmodel.NodeOrProxyStatusUpgrading
			resource.LoggerTask.Trace(ctx, "master status toupgrade change to upgrading", logit.String("appId", app.AppID),
				logit.String("masterId", param.TargetNodeID))
			find = true
			break OutLoop
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "master not found", logit.String("appId", app.AppID),
			logit.String("nodeId", param.TargetNodeID))
		return errors.New("master not found")
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForUpgradeOneDatanode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	find := false

OutLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusToUpgrade {
				node.Status = vdbmodel.NodeOrProxyStatusUpgrading
				resource.LoggerTask.Trace(ctx, "datanode status toupgrade change to upgrading", logit.String("appId", app.AppID),
					logit.String("nodeId", param.TargetNodeID))
				find = true
				break OutLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "datanode not found", logit.String("appId", app.AppID),
			logit.String("nodeId", param.TargetNodeID))
		return errors.New("datanode not found")
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForUpgradeOneProxy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	find := false

OutLoop:
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.ProxyID == param.TargetNodeID && proxy.Status == vdbmodel.NodeOrProxyStatusToUpgrade {
				proxy.Status = vdbmodel.NodeOrProxyStatusUpgrading
				resource.LoggerTask.Trace(ctx, "proxy status toupgrade change to upgrading", logit.String("appId", app.AppID),
					logit.String("proxyId", param.TargetNodeID))
				find = true
				break OutLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "proxy not found", logit.String("appId", app.AppID),
			logit.String("nodeId", param.TargetNodeID))
		return errors.New("proxy not found")
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForUpgradeOneNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.TargetNodeEngine == vdbmodel.EngineVDBMaster {
		return ProcessBuildMetaForUpgradeOneMaster(ctx, teu)
	} else if param.TargetNodeEngine == vdbmodel.EngineVDBDataNode {
		return ProcessBuildMetaForUpgradeOneDatanode(ctx, teu)
	} else if param.TargetNodeEngine == vdbmodel.EngineVDBProxy {
		return ProcessBuildMetaForUpgradeOneProxy(ctx, teu)
	}

	resource.LoggerTask.Error(ctx, "error engine type", logit.String("appId", teu.Entity))
	return errors.New("not support engine")

}
