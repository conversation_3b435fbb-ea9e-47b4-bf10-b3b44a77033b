# Service的名字，必选，需自定义修改
Name = "broker-redis"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，ms
ConnTimeOut = 1000
# 写数据超时，ms
WriteTimeOut = 5000
# 读数据超时，ms
ReadTimeOut = 5000

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
[[Resource.Manual.default]]
#Host = "*************"
Host = "127.0.0.1"
Port = 6998
[[Resource.Manual.preonline]]
#Host = "*************"
Host = "127.0.0.1"
Port = 6999
[[Resource.Manual.onlinebj]]
#Host = "************"
Host = "127.0.0.1"
Port = 6379
[[Resource.Manual.onlinebd]]
#Host = "*************"
Host = "127.0.0.1"
Port = 6379
[[Resource.Manual.onlinegz]]
#Host = "*************"
Host = "127.0.0.1"
Port = 6379
[[Resource.Manual.onlinesu]]
#Host = "************"
Host = "127.0.0.1"
Port = 6379
[[Resource.Manual.bdtest]]
#Host = "************"
Host = "127.0.0.1"
Port = 6379
[[Resource.Manual.dbstackadaptorbcc]]
Host = "127.0.0.1"
Port = 6379

# redis 特有的配置，可选
[Redis]
DB = 15
PoolSizePerIP = 100
# 空闲状态允许的连接数，建议值 和 PoolSizePerIP 一样
MinIdleConnsPerIP = 100
Password = "x1taskredispppwwwxnxb8"
CmdArgsLogLen = -1

[Redis.IDC_SPEC.dbstackadaptorbcc]
Password = "drds@2021"
