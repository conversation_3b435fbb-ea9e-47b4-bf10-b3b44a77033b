/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
检查子网, 根据子网属性，决定是否添加ipv6标记
*/

package checksubnets

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	subnet "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/subnet"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

// CheckIPV6 根据子网属性，决定是否添加ipv6标记
// (1）检查子网是否支持ipv6 调用接口VpcSDK::get_subnet_ip_type，属于neutron后端接口
// (2) a) 如果子网均支持ipv6，且配置可以创建ipv6实例，配置了ipv6镜像，则设置app标记，支持ipv6
//
//	b) 如果子网均不支持ipv6, 则设置app标记，不支持ipv6
//	c) 其他情况报错退出
//
// 参考代码 OriginalReqToGeneralReqProcessor::check_cluster_is_ipv6_and_set_ipv6_flag
func CheckIPV6(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("CheckSubnetsIPV6 fail : teu is nilptr") // todo 细化err
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err // todo 细化err
	}

	iamUserID := app.UserID

	var replicas []iface.Replica
	err = json.Unmarshal([]byte(app.AzDeployInfo), &replicas)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal replicas fail", logit.String("app.Replicas", app.AzDeployInfo),
			logit.Error("error", err))
		return err
	}

	var subnetList []string
	for _, v := range replicas {
		subnetList = append(subnetList, v.SubnetID)
	}

	// 检查subnet的类型是否相同,是否都为IPv4或IPv6
	subnetCompent := subnet.Instance()
	isEnableIpv6, err := subnetCompent.CheckSubnetsSupportIPV6(ctx, &subnet.CheckSubnetsSupportIPV6Params{
		UserID: iamUserID,
		Subnet: subnetList,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check ipv6 component error", logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return err // todo 细化err c) 其他情况报错退出
	}

	// (2) a) 如果子网均支持ipv6，且配置可以创建ipv6实例，配置了ipv6镜像，则设置app标记，支持ipv6
	//     b) 如果子网均不支持ipv6, 则设置app标记，不支持ipv6
	if isEnableIpv6 {
		app.IPType = vdbmodel.Ipv6
		paramForIpv6Blb, err := iface.GetParameters(ctx, teu.Parameters)
		if err != nil {
			resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
			return err
		}
		createIpv6BlbModels(app, paramForIpv6Blb)
		_, version := util.GetImageIDAndVersion(app.ImageID)
		app.ImageID = bccresource.GetBccImageIdForIpv6() + ":" + version

	} else {
		app.IPType = vdbmodel.Ipv4
	}
	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func createIpv6BlbModels(app *vdbmodel.Application, param *iface.TaskParameters) {
	app.BLBs = append(app.BLBs, &vdbmodel.BLB{
		AppID:             app.AppID,
		Name:              app.AppID,
		VpcID:             app.VpcID,
		SubnetID:          param.BlbSubnetID,
		Type:              vdbmodel.BLBTypeNormal,
		IPType:            vdbmodel.Ipv6,
		BgwGroupID:        param.BgwGroup.BgwGroupID,
		BgwGroupMode:      param.BgwGroup.BgwGroupMode,
		BgwGroupExclusive: param.BgwGroup.BgwGroupExclusive,
		MasterAZ:          param.BgwGroup.MasterAZ,
		SlaveAZ:           param.BgwGroup.SlaveAZ,
	})
}
