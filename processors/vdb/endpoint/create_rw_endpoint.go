/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2022/06/27, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
服务网卡
*/

package endpoint

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blb_v2_component "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// 1. 获取app信息
// 2. 检查是否需要创建服务网卡
// 3. 创建服务发布点
// 4. 创建服务网卡
func ProcessCreateRwEndpoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() {
		return nil
	}

	// 1. 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity), logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 2. 检查是否需要创建服务网卡(根据 user_id 判断)
	env := blb_v2_component.Instance().GetEnv(ctx)
	if app.UserID == env.ResourcePrivateUserId {
		return nil
	}

	// 3. 创建服务发布点
	if err := createServicePublishEndpoint(ctx, app, ""); err != nil {
		resource.LoggerTask.Error(ctx, "execute CreateEndpoint step err",
			logit.String("appId", app.AppID),
			logit.String("exe_step", "create_service_publish_endpoint"),
			logit.Error("error", err),
		)
		return err
	}

	// 4. 创建服务网卡
	if err := createEndpoint(ctx, app, ""); err != nil {
		resource.LoggerTask.Error(ctx, "execute CreateEndpoint step err",
			logit.String("appId", app.AppID),
			logit.String("exe_step", "create_endpoint"),
			logit.Error("error", err),
		)
		return err
	}
	return nil
}
