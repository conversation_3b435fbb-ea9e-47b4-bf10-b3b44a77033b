/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建虚机安全组、初始化安全组规则
*/

package securitygroup

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// 初始化安全组
// 1. 创建安全组
// 2. 初始化基本规则p
// 3. 初始化服务端口的规则
func initSecurityGroup(ctx context.Context, app *vdbmodel.Application, needInner bool) error {
	isEnableIpv6 := app.IPType == vdbmodel.Ipv6
	sgComponent := security_group_openapi.Instance()
	outerPorts := []int32{cast.ToInt32(app.InnerPort)}

	securityGroupID, err := sgComponent.InitSecurityGroupRules(ctx, &security_group_openapi.InitSecurityGroupRulesParams{
		UserID:       app.UserID,
		ServicePorts: nil,
		IsEnableIPV6: isEnableIpv6,
		VpcID:        app.VpcID,
		Ports:        outerPorts,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "init sg component error", logit.Error("error", err))
		return err
	}
	if len(securityGroupID) == 0 {
		resource.LoggerTask.Warning(ctx, "securityGroupId is empty")
		return errors.New("securityGroupId is empty")
	}
	app.SecurityGroupID = securityGroupID

	// 创建datanode和master的安全组
	if needInner {
		var innerPorts []int32
		innerPorts = GetSecurityGrouptInnerPorts(ctx, app)
		securityGroupID, err := sgComponent.InitSecurityGroupRules(ctx, &security_group_openapi.InitSecurityGroupRulesParams{
			UserID:       app.UserID,
			ServicePorts: nil,
			IsEnableIPV6: isEnableIpv6,
			VpcID:        app.VpcID,
			Ports:        innerPorts,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "init sg component error", logit.Error("error", err))
			return err
		}
		if len(securityGroupID) == 0 {
			resource.LoggerTask.Warning(ctx, "securityGroupId is empty")
			return errors.New("securityGroupId is empty")
		}
		app.InternalSecurityGroupID = securityGroupID
	}

	return nil
}

func ProcessInitSecurityGroupStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 容器化集群无需创建安全组
	if app.ResourceType == "container" {
		return nil
	}

	if err := initSecurityGroup(ctx, app, false); err != nil {
		return err
	}
	return saveSecurityGroup(ctx, app)
}

func ProcessInitSecurityGroupCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 容器化集群无需创建安全组
	if app.ResourceType == "container" {
		return nil
	}

	needInner := true

	if err := initSecurityGroup(ctx, app, needInner); err != nil {
		return err
	}
	return saveSecurityGroup(ctx, app)
}

func saveSecurityGroup(ctx context.Context, app *vdbmodel.Application) error {
	sgID, innerSgID := app.SecurityGroupID, app.InternalSecurityGroupID
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = vdbmodel.ApplicationGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	app.SecurityGroupID = sgID
	app.InternalSecurityGroupID = innerSgID
	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}
	return nil
}
