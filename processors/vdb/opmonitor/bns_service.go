package opmonitor

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessCreateOpmonitorBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	op := opmonitor.Instance()

	// 创建proxy bns service
	req := &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: int64(app.ID),
		Engine:    vdbmodel.EngineVDBProxy,
	}

	if err := op.CreateInterfaceBnsServices(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "create proxy bns service fail", logit.Error("error", err))
		return err
	}

	// 创建datanode bns service
	req = &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: int64(app.ID),
		Engine:    vdbmodel.EngineVDBDataNode,
	}

	if err := op.CreateNodeBnsServices(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "create datanode bns service fail", logit.Error("error", err))
		return err
	}

	// 创建master bns service
	req = &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: int64(app.ID),
		Engine:    vdbmodel.EngineVDBMaster,
	}

	if err := op.CreateNodeBnsServices(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "create master bns service fail", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessDeleteOpmonitorBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	err = doDeleteOpmonitorBnsService(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete opmonitor bns service fail", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessCleanDeletedOpmonitorBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.CleanBnsParam == nil || param.CleanBnsParam.DaysAgoEnd < 1 {
		resource.LoggerTask.Warning(ctx, "clean bns param is nil or days ago end lt 1")
		return errors.New("invalid param")
	}

	// 获取符合条件的删除的集群
	appModels, err := vdbmodel.ApplicationGetAllByCond(ctx, "status = ?", vdbmodel.AppStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app models failed", logit.Error("err", err))
		return err
	}

	for _, app := range appModels {
		err = doDeleteOpmonitorBnsService(ctx, app)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "delete opmonitor bns service fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func doDeleteOpmonitorBnsService(ctx context.Context, app *vdbmodel.Application) error {
	op := opmonitor.Instance()
	// 删除proxy bns service
	req := &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: int64(app.ID),
		Engine:    vdbmodel.EngineVDBProxy,
	}

	if err := op.DeleteInterfaceBnsServices(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "delete proxy bns service fail", logit.Error("error", err))
		return err
	}

	// 删除datanode bns service
	req = &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: int64(app.ID),
		Engine:    vdbmodel.EngineVDBDataNode,
	}

	if err := op.DeleteNodeBnsServices(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "delete datanode bns service fail", logit.Error("error", err))
		return err
	}

	// 删除master bns service
	req = &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: int64(app.ID),
		Engine:    vdbmodel.EngineVDBMaster,
	}

	if err := op.DeleteNodeBnsServices(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "delete master bns service fail", logit.Error("error", err))
		return err
	}

	return nil
}
