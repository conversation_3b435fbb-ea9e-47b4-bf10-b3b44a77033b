/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessCheckAllStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Notice(ctx, "only check status in cluster mode")
		return nil
	}

	for {
		complete := true
		if err := checkUpgradingNodeVersion(ctx, teu, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "upgrading node version check failed", logit.Error("err", err))
		}
		resource.LoggerTask.Notice(ctx, "checkUpgradingNodeVersion is complete")

		if err := checkDatanodeStatusIsAlive(ctx, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "datanode status check failed", logit.Error("err", err))
		}

		resource.LoggerTask.Notice(ctx, "checkDatanodeStatusIsAlive is complete")

		if err := checkDataNodeTablet(ctx, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "datanode tablet check failed", logit.Error("err", err))
		}

		resource.LoggerTask.Notice(ctx, "checkDataNodeTablet is complete")

		if err := checkProxyStatusIsAlive(ctx, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "proxy status check failed", logit.Error("err", err))
		}

		resource.LoggerTask.Notice(ctx, "checkProxyStatusIsAlive is complete")

		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			continue
		}
	}
}
