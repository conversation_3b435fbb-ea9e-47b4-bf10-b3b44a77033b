package opmonitor

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessCreateOpmonitorInstanceBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	op := opmonitor.Instance()

	if app.Type == vdbmodel.AppTypeCluster {
		proxyReq := &opmonitor.ProxyInstanceBnsParams{
			ClusterID:      int64(app.ID),
			ProxyInstances: make([]*opmonitor.ProxyInstanceInfo, 0),
		}
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxies {
				if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
					continue
				}
				proxyReq.ProxyInstances = append(proxyReq.ProxyInstances,
					&opmonitor.ProxyInstanceInfo{
						ShortID:    proxy.ID,
						FloatingIP: proxy.FloatingIP,
					})
			}
		}
		if len(proxyReq.ProxyInstances) > 0 {
			if err := op.CreateProxyInstanceBns(ctx, proxyReq); err != nil {
				resource.LoggerTask.Warning(ctx, "create proxy instance bns fail", logit.Error("error", err))
				return err
			}
		}
	}

	nodeReq := &opmonitor.NodeInstanceBnsParams{
		ClusterID:     int64(app.ID),
		NodeInstances: make([]*opmonitor.NodeInstanceInfo, 0),
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			nodeReq.NodeInstances = append(nodeReq.NodeInstances,
				&opmonitor.NodeInstanceInfo{
					ShortID:    node.ID,
					FloatingIP: node.FloatingIP,
					Engine:     cluster.Engine,
				})
		}
	}

	if len(nodeReq.NodeInstances) > 0 {
		if err := op.CreateNodeInstanceBns(ctx, nodeReq); err != nil {
			resource.LoggerTask.Warning(ctx, "create node instance bns fail", logit.Error("error", err))
			return err
		}
	}

	masterReq := &opmonitor.NodeInstanceBnsParams{
		ClusterID:     int64(app.ID),
		NodeInstances: make([]*opmonitor.NodeInstanceInfo, 0),
	}
	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}
		masterReq.NodeInstances = append(masterReq.NodeInstances,
			&opmonitor.NodeInstanceInfo{
				ShortID:    master.ID,
				FloatingIP: master.FloatingIP,
				Engine:     master.Engine,
			})
	}

	if len(masterReq.NodeInstances) > 0 {
		if err := op.CreateNodeInstanceBns(ctx, masterReq); err != nil {
			resource.LoggerTask.Warning(ctx, "create master instance bns fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessCleanToDeleteInstanceOpmonitorInstanceBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	instances, err := vdbmodel.ToDeleteNodeGetAllByCond(ctx, "bns_clear_status = ?", util.BNSUnclearStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get unclear inst fail", logit.Error("err", err))
		return err
	}

	op := opmonitor.Instance()

	for _, instance := range instances {
		if instance.Engine == vdbmodel.EngineVDBProxy {
			if err := op.DeleteProxyInstanceBns(ctx, &opmonitor.ProxyInstanceBnsParams{
				ClusterID: int64(app.ID),
				ProxyInstances: []*opmonitor.ProxyInstanceInfo{{
					ShortID:    int(instance.ShortID),
					FloatingIP: instance.FloatingIP,
				}}}); err != nil {
				resource.LoggerTask.Warning(ctx, "delete proxy instance bns fail", logit.Error("error", err))
				// 避免脏数据影响主流程
				continue
			}
		} else if instance.Engine == vdbmodel.EngineVDBDataNode || instance.Engine == vdbmodel.EngineVDBMaster {
			if err := op.DeleteNodeInstanceBns(ctx, &opmonitor.NodeInstanceBnsParams{
				ClusterID: int64(app.ID),
				NodeInstances: []*opmonitor.NodeInstanceInfo{{
					ShortID:    int(instance.ShortID),
					FloatingIP: instance.FloatingIP,
					Engine:     instance.Engine,
				}},
			}); err != nil {
				resource.LoggerTask.Warning(ctx, "delete node/master instance bns fail", logit.Error("error", err))
				// 避免脏数据影响主流程
				continue
			}
		} else {
			continue
		}

		instance.BnsClearStatus = util.BNSClearStatus
	}

	err = vdbmodel.ToDeleteNodesSave(ctx, instances)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update cache instances to delete failed", logit.Error("error", err))
		return err
	}

	return nil
}
