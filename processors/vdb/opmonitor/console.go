package opmonitor

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
)

var (
	ErrCallXagentFail = errors.New("call xagent fail")
)

func getUsage(ctx context.Context, host string, port int) (int64, int64, int64, error) {
	xagentAddr := xagent.Addr{Host: host, Port: int32(port)}
	Req := xagent.Request{
		Addr:    &xagentAddr,
		Action:  "get_usage",
		Product: vdbmodel.ProductVDB,
	}

	resp, err := xagent.Instance().Do(ctx, &Req)
	// 调用xagent失败
	if err != nil {
		resource.LoggerTask.Trace(ctx, "call xagent fail", logit.Error("err", err), logit.String("host", base_utils.Format(host)),
			logit.Int("port", port))
		return 0, 0, 0, ErrCallXagentFail
	}

	strResp := base_utils.Format(resp.Result)
	resource.LoggerTask.Trace(ctx, "get usage via xagent success", logit.String("host", base_utils.Format(host)),
		logit.Int("port", port), logit.String("resp", strResp))

	strResp = strings.TrimPrefix(strResp, "[")
	strResp = strings.TrimSuffix(strResp, "]")

	numbers := strings.Split(strResp, ",")
	var intNumbers []int64
	for _, numberStr := range numbers {
		num, err := strconv.Atoi(numberStr)
		if err == nil {
			intNumbers = append(intNumbers, int64(num))
		} else {
			resource.LoggerTask.Trace(ctx, "convert string to int fail", logit.String("numberStr", numberStr))
			return 0, 0, 0, errors.New("invalid response")
		}
	}

	if len(intNumbers) == 3 {
		resource.LoggerTask.Trace(ctx, "get usage via xagent success", logit.Int64("mem_used", intNumbers[0]),
			logit.Int64("disk_used", intNumbers[1]), logit.Int64("cpu_used", intNumbers[2]))
		return intNumbers[0], intNumbers[1], intNumbers[2], nil
	} else if len(intNumbers) == 2 {
		resource.LoggerTask.Trace(ctx, "get usage via xagent success", logit.Int64("mem_used", intNumbers[0]),
			logit.Int64("disk_used", intNumbers[1]))
		return intNumbers[0], intNumbers[1], 0, nil
	}

	resource.LoggerTask.Trace(ctx, "get usage via xagent fail", logit.String("resp", strResp))
	return 0, 0, 0, errors.New("invalid response")

}

// ProcessCollectDataUsageForConsole 收集数据使用情况
func ProcessCollectDataUsageForConsole(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.CollectUsageParam == nil || param.CollectUsageParam.SchedulePeriodMinutes == 0 {
		resource.LoggerTask.Warning(ctx, "collect data usage param is nil or schedule period = 0")
		return errors.New("invalid param")
	}

	appModels, err := vdbmodel.ApplicationGetAllByCond(ctx, "status not in (?,?,?,?)", vdbmodel.AppStatusDeleted,
		vdbmodel.AppStatusPaused, vdbmodel.AppStatusPreCreate, vdbmodel.AppStatusCreateFailed)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app models failed", logit.Error("err", err))
		return err
	}

	g := gtask.Group{
		Concurrent:    50,
		AllowSomeFail: true,
	}

	for _, app := range appModels {
		app := app
		period := param.CollectUsageParam.SchedulePeriodMinutes

		balanceTaskNum, err := mochow.ListBalanceTaskNum(ctx, app)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get balance task num fail", logit.String("appId", app.AppID), logit.Error("error", err))
		} else if balanceTaskNum > 0 {
			err = resource.RedisClient.Set(ctx, vdbmodel.BalanceTaskNumKeyPrefix+app.AppID, balanceTaskNum,
				time.Duration(period)*2*time.Minute).Err()
			if err != nil {
				resource.LoggerTask.Error(ctx, "set balance task num key fail", logit.Error("error", err), logit.String("appId", app.AppID))
				return err
			}
		}
		resource.LoggerTask.Trace(ctx, "ready to get data usage", logit.String("app", app.AppID))
		g.Go(func() error {
			nodes, err := vdbmodel.NodeGetAllByCond(ctx, "app_id=? and status=?", app.AppID,
				vdbmodel.NodeOrProxyStatusInUse)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get node fail", logit.Error("error", err),
					logit.String("appId", app.AppID))
				return err
			}

			if len(nodes) == 0 {
				resource.LoggerTask.Notice(ctx, "app has no master node",
					logit.String("appId", app.AppID))
				return nil
			}

			var totalMemUsageBytes int64
			var totalDiskUsageBytes int64
			nodeMaxMemUsageBytes := int64(0)
			nodeMaxDiskUsageBytes := int64(0)
			nodeMaxCPUUsage := int64(0)
			for _, node := range nodes {
				memUsage, diskUsage, cpuUsage, err := getUsage(ctx, node.FloatingIP, node.XagentPort)
				if err != nil {
					resource.LoggerTask.Error(ctx, "get data usage from node fail",
						logit.String("app", app.AppID), logit.String("node", base_utils.Format(node)))
					return err
				}
				totalMemUsageBytes += memUsage
				totalDiskUsageBytes += diskUsage

				if memUsage > nodeMaxMemUsageBytes {
					nodeMaxMemUsageBytes = memUsage
				}

				if diskUsage > nodeMaxDiskUsageBytes {
					nodeMaxDiskUsageBytes = diskUsage
				}

				if cpuUsage > nodeMaxCPUUsage {
					nodeMaxCPUUsage = cpuUsage
				}
			}

			resource.LoggerTask.Trace(ctx, "get data usage success", logit.String("app", app.AppID), logit.String("memkey", vdbmodel.MemKeyPrefix+app.AppID),
				logit.String("diskkey", vdbmodel.DiskKeyPrefix+app.AppID), logit.String("nodeMaxmemkey", vdbmodel.NodeMaxMemKeyPrefix+app.AppID),
				logit.String("nodeMaxdiskkey", vdbmodel.NodeMaxDiskKeyPrefix+app.AppID), logit.Int64("mem_usage", totalMemUsageBytes),
				logit.Int64("diskUsage", totalDiskUsageBytes), logit.Int64("node_max_mem_usage", nodeMaxMemUsageBytes),
				logit.Int64("node_max_diskUsage", nodeMaxDiskUsageBytes), logit.Int64("node_max_cpuUsage", nodeMaxCPUUsage))
			err = resource.RedisClient.Set(ctx, vdbmodel.MemKeyPrefix+app.AppID, totalMemUsageBytes, time.Duration(period)*2*time.Minute).Err()
			if err != nil {
				resource.LoggerTask.Error(ctx, "set data mem usage key fail", logit.Error("error", err), logit.String("appId", app.AppID))
				return err
			}

			err = resource.RedisClient.Set(ctx, vdbmodel.DiskKeyPrefix+app.AppID, totalDiskUsageBytes, time.Duration(period)*2*time.Minute).Err()
			if err != nil {
				resource.LoggerTask.Error(ctx, "set data disk usage key fail", logit.Error("error", err), logit.String("appId", app.AppID))
				return err
			}

			err = resource.RedisClient.Set(ctx, vdbmodel.NodeMaxMemKeyPrefix+app.AppID, nodeMaxMemUsageBytes, time.Duration(period)*2*time.Minute).Err()
			if err != nil {
				resource.LoggerTask.Error(ctx, "set node max mem usage key fail", logit.Error("error", err), logit.String("appId", app.AppID))
				return err
			}

			err = resource.RedisClient.Set(ctx, vdbmodel.NodeMaxDiskKeyPrefix+app.AppID, nodeMaxDiskUsageBytes, time.Duration(period)*2*time.Minute).Err()
			if err != nil {
				resource.LoggerTask.Error(ctx, "set node max disk usage key fail", logit.Error("error", err), logit.String("appId", app.AppID))
				return err
			}

			err = resource.RedisClient.Set(ctx, vdbmodel.NodeMaxCPUKeyPrefix+app.AppID, nodeMaxCPUUsage, time.Duration(period)*2*time.Minute).Err()
			if err != nil {
				resource.LoggerTask.Error(ctx, "set node max cpu usage key fail", logit.Error("error", err), logit.String("appId", app.AppID))
				return err
			}

			return nil
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "collect usage not all success", logit.Error("err", err))
		return err
	}
	return nil
}
