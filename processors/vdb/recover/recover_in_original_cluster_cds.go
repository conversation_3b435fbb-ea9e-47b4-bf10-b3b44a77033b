/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/13 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/common"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
)

func ProcessRecoverInOriginalClusterCreateNewCDS(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := param.AppID
	nodeID := teu.Entity

	resource.LoggerTask.Trace(ctx, "RecoverInOriginalClusterCreateNewCDS",
		logit.String("params :", base_utils.Format(param)), logit.String("appID", appID))

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := processRecoverClusterCreateNewCDS(ctx, teu.TaskID, app, nodeID, param.RevoverParam); err != nil {
		resource.LoggerTask.Warning(ctx, "process recover in original cluster create cds fail", logit.String("appID", appID),
			logit.String("nodeID", nodeID), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverInOriginalClusterDeleteOldCDS(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := param.AppID
	nodeID := teu.Entity

	resource.LoggerTask.Trace(ctx, "RecoverInOriginalClusterDeleteOldCDS",
		logit.String("params :", base_utils.Format(param)), logit.String("appID", appID))

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := processRecoverClusterDeleteOldCDS(ctx, teu.TaskID, app, nodeID); err != nil {
		resource.LoggerTask.Warning(ctx, "process recover in original cluster delete old cds fail", logit.String("clusterId", teu.Entity),
			logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverInOriginalClusterRecoverLeaderMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	leaderID, followerIDs, err := GetOriginalMasterIDs(ctx, teu.Parameters, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get recover master ids error", logit.String("appID", appID), logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "RecoverInOriginalClusterRecoverMaster",
		logit.String("params :", base_utils.Format(param)), logit.String("appID", appID),
		logit.String("leaderID", leaderID), logit.String("followerIDs", base_utils.Format(followerIDs)))

	//恢复leader节点
	if err := restartMasterNode(ctx, app, leaderID); err != nil {
		resource.LoggerTask.Warning(ctx, "recover master node fail", logit.String("nodeID", leaderID), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverInOriginalClusterSetLeaderMasterSingleMode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	leaderID, followerIDs, err := GetOriginalMasterIDs(ctx, teu.Parameters, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get recover master ids error", logit.String("appID", appID), logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "RecoverInOriginalClusterSetLeaderMasterSingleMode",
		logit.String("params :", base_utils.Format(param)), logit.String("appID", appID),
		logit.String("leaderID", leaderID), logit.String("followerIDs", base_utils.Format(followerIDs)))

	//设置单主
	if err := setMasterNodeSingleMode(ctx, app, leaderID); err != nil {
		resource.LoggerTask.Warning(ctx, "set master node single mode fail", logit.String("nodeID", leaderID), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverInOriginalClusterRelocateDatanodeAddr(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	leaderID, followerIDs, err := GetOriginalMasterIDs(ctx, teu.Parameters, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get recover master ids error", logit.String("appID", appID), logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "ProcessRecoverInOriginalClusterRelocateDatanodeAddr",
		logit.String("params :", base_utils.Format(param)), logit.String("appID", appID),
		logit.String("leaderID", leaderID), logit.String("followerIDs", base_utils.Format(followerIDs)))

	//在master中修改dn节点信息
	if err := relocateDatanodeAddr(ctx, app, leaderID, param.RevoverParam); err != nil {
		resource.LoggerTask.Warning(ctx, "relocate datanode addr fail", logit.String("nodeID", leaderID), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverInOriginalClusterRecoverFollowerMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := param.AppID
	masterID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	//恢复follower节点
	if err := restartMasterNode(ctx, app, masterID); err != nil {
		resource.LoggerTask.Warning(ctx, "recover follower master node fail", logit.String("nodeID", masterID), logit.Error("error", err))
		return err
	}

	return nil
}

func GetRecoverFollowerMasterIDs(ctx context.Context, task *workflow.Task) ([]string, error) {
	param, err := iface.GetParameters(ctx, task.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return nil, cerrs.ErrInvalidParams.Wrap(err)
	}

	resource.LoggerTask.Trace(ctx, "recover get all need operate cds node ids params",
		logit.String("params :", base_utils.Format(param)),
		logit.String("appID", base_utils.Format(task.Entity)))

	_, followerIDs, err := GetOriginalMasterIDs(ctx, task.Parameters, task.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get recover master ids error", logit.String("appID", task.Entity), logit.Error("error", err))
		return nil, err
	}

	return followerIDs, nil
}

func ProcessRecoverInOriginalClusterAddFollowerMasterPeer(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	leaderID, followerIDs, err := GetOriginalMasterIDs(ctx, teu.Parameters, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get recover master ids error", logit.String("appID", appID), logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "RecoverInOriginalClusterSetLeaderMasterSingleMode",
		logit.String("params :", base_utils.Format(param)), logit.String("appID", appID),
		logit.String("leaderID", leaderID), logit.String("followerIDs", base_utils.Format(followerIDs)))

	for _, followerID := range followerIDs {
		if err := addFollowerMasterPeer(ctx, app, leaderID, followerID); err != nil {
			resource.LoggerTask.Warning(ctx, "add follower master peer fail", logit.String("nodeID", followerID), logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessRecoverInOriginalClusterRecoverDataNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			err = doRestartDatanode(ctx, node)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "recover datanode node fail", logit.String("nodeID", node.NodeID), logit.Error("error", err))
				return err
			}
		}
	}

	return nil
}

func ProcessRecoverInOriginalClusterRecoverProxy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			err = doRestartProxyNode(ctx, proxy)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "recover proxy node fail", logit.String("nodeID", proxy.ProxyID), logit.Error("error", err))
				return err
			}
		}
	}

	return nil
}

func ProcessRecoverSetAppTabletPeers(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	err = mochow.SetAppTabletPeers(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "set app tablet peers error", logit.String("appID", appID), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverSetSingleMasterAddr(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	/*发送请求*/
	confReqList := make([]*xagent.AsyncRequest, 0)
	envReqList := make([]*xagent.AsyncRequest, 0)

	for _, master := range app.Masters {
		newMasterAddr := fmt.Sprintf("%s:%d", master.IP, master.Port)
		confReq := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "replace_master_addr",
			Params: &common.UpdateNewAddrXagentRequest{
				Meta: &xagent.Meta{
					Basedir: "/home/<USER>/mochow-master",
				},
				NewAddress: newMasterAddr,
			},
		}

		confReqList = append(confReqList, confReq)

		envReq := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "env_vars_op",
			Params: &common.UpdateEnvVarsXagentRequest{
				Meta: &xagent.Meta{
					Basedir: "/home/<USER>/",
				},
				Op: "set",
				ConfigList: []*common.ConfigItem{{
					Name:  "MASTER_ADDRS",
					Value: "list://" + newMasterAddr,
				}},
			},
		}

		envReqList = append(envReqList, envReq)
	}

	g := gtask.Group{Concurrent: 3}
	var failedConfTasks []string
	for _, req := range confReqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update mochow conf master addr fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
				failedConfTasks = append(failedConfTasks, fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port))
			}
			return nil
		})
	}

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Some update mochow conf tasks failed", logit.String("tasks %s", strings.Join(failedConfTasks, ",")))
	}

	if len(failedConfTasks) > 0 {
		resource.LoggerTask.Warning(ctx, "Some update mochow conf tasks failed", logit.String("tasks %s", strings.Join(failedConfTasks, ",")))
	}

	gg := gtask.Group{Concurrent: 3}
	var failedEnvTasks []string
	for _, req := range envReqList {
		req := req
		gg.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update env vars master addr fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
				failedEnvTasks = append(failedEnvTasks, fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port))
			}
			return nil
		})
	}

	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Some update env tasks failed", logit.String("tasks %s", strings.Join(failedEnvTasks, ",")))
	}

	if len(failedEnvTasks) > 0 {
		resource.LoggerTask.Warning(ctx, "Some update env tasks failed", logit.String("tasks %s", strings.Join(failedEnvTasks, ",")))
	}

	return nil
}

func ProcessRecoverUpdateMasterAddr(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	/*获取最新的master addr*/
	newMasterAddr := ""
	for _, master := range app.Masters {
		if newMasterAddr != "" {
			newMasterAddr += ","
		}
		newMasterAddr += fmt.Sprintf("%s:%d", master.IP, master.Port)
	}

	resource.LoggerTask.Notice(ctx, "to update", logit.String("newMasterAddr :", base_utils.Format(newMasterAddr)))

	/*发送请求*/
	confReqList := make([]*xagent.AsyncRequest, 0)
	envReqList := make([]*xagent.AsyncRequest, 0)

	for _, master := range app.Masters {
		confReq := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "replace_master_addr",
			Params: &common.UpdateNewAddrXagentRequest{
				Meta: &xagent.Meta{
					Basedir: "/home/<USER>/mochow-master",
				},
				NewAddress: newMasterAddr,
			},
		}

		confReqList = append(confReqList, confReq)

		envReq := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "env_vars_op",
			Params: &common.UpdateEnvVarsXagentRequest{
				Meta: &xagent.Meta{
					Basedir: "/home/<USER>/",
				},
				Op: "set",
				ConfigList: []*common.ConfigItem{{
					Name:  "MASTER_ADDRS",
					Value: "list://" + newMasterAddr,
				}},
			},
		}

		envReqList = append(envReqList, envReq)
	}

	g := gtask.Group{Concurrent: 3}
	var failedConfTasks []string
	for _, req := range confReqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update mochow conf master addr fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
				failedConfTasks = append(failedConfTasks, fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port))
			}
			return nil
		})
	}

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Some update mochow conf tasks failed", logit.String("tasks %s", strings.Join(failedConfTasks, ",")))
	}

	if len(failedConfTasks) > 0 {
		resource.LoggerTask.Warning(ctx, "Some update mochow conf tasks failed", logit.String("tasks %s", strings.Join(failedConfTasks, ",")))
	}

	gg := gtask.Group{Concurrent: 3}
	var failedEnvTasks []string
	for _, req := range envReqList {
		req := req
		gg.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update env vars master addr fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
				failedEnvTasks = append(failedEnvTasks, fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port))
			}
			return nil
		})
	}

	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Some update env tasks failed", logit.String("tasks %s", strings.Join(failedEnvTasks, ",")))
	}

	if len(failedEnvTasks) > 0 {
		resource.LoggerTask.Warning(ctx, "Some update env tasks failed", logit.String("tasks %s", strings.Join(failedEnvTasks, ",")))
	}

	return nil
}
