[Image]
Bcc = "ad3caba3-dd71-456b-9fac-3e708bad5f6b"
DockerImage = "registry.baidubce.com/scs_test_cce/vdb-binary-centos6u3-xagent-dbstack:2024071303"

[PkgConf]
AgentBins = ["slot-redis", "redis", "PegaDB2", "proxy-slot", "nutcraker"]
NeedExcutePkgs = ["agent", "monitor-agent", "cron", "mochow-master","mochow-proxy","mochow-datanode","mochow-standalone"]
CorePkgs = ["slot-redis", "redis", "PegaDB2", "proxy-slot", "nutcraker", "sync-agent"]
