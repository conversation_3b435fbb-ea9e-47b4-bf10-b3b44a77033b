/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* package_manager_test.go */
/*
modification history
--------------------
2023/05/12 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package conf

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestInitPkgManConf(t *testing.T) {
	err := InitPkgManConf()
	if err != nil {
		t.<PERSON>rf("[%s] load conf fail %s\n", t.Name(), err.<PERSON>rror())
	}
	fmt.Println(base_utils.Format(PkgManConf))
}

func TestIsAgentsBin(t *testing.T) {
	err := InitPkgManConf()
	if err != nil {
		return
	}
	if err != nil {
		t.<PERSON>rf("[%s] load conf fail %s\n", t.Name(), err.<PERSON>rror())
	}
	if IsAgentsBin("agent") {
		t.<PERSON><PERSON>("[%s] is agent bin wrong %s\n", t.Name(), err.<PERSON>rror())
	}
	if !IsAgentsBin("sync-agent") {
		t.Errorf("[%s] is agent bin wrong %s\n", t.Name(), err.Error())
	}
}

func TestIsNeedExuctePkg(t *testing.T) {
	err := InitPkgManConf()
	if err != nil {
		return
	}
	if err != nil {
		t.Errorf("[%s] load conf fail %s\n", t.Name(), err.Error())
	}
	if IsNeedExuctePkg("slot-redis") {
		t.Errorf("[%s] TestIsNeedExuctePkg wrong %s\n", t.Name(), err.Error())
	}
	if !IsNeedExuctePkg("monitor-agent") {
		t.Errorf("[%s] TestIsNeedExuctePkg wrong %s\n", t.Name(), err.Error())
	}
}
