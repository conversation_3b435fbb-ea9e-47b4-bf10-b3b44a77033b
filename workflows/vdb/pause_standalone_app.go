/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* pause_standalone_app.go - File Description */
/*
Modification History
--------------------
2024/02/27, by Fangjinxia, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
)

const (
	WorkflowPauseStandaloneApp     = "vdb-pause-standalone-app"
	StepPauseStandaloneUnbindAllRs = "vdb-pause-standalone-app-unbind-all-rs"
	StepPauseStandaloneCallbacks   = "vdb-pause-standalone-app-callbacks"
)

func init() {
	// Step-1 摘除所有Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseStandaloneUnbindAllRs,
		Workflow:        WorkflowPauseStandaloneApp,
		StepProcess:     blb.ProcessUnbindAllStandalones,
		SuccessNextStep: StepPauseStandaloneCallbacks,
		ErrorNextStep:   StepPauseStandaloneUnbindAllRs,
	})

	// Step-2 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseStandaloneCallbacks,
		Workflow:        WorkflowPauseStandaloneApp,
		StepProcess:     callback.ProcessPauseCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPauseStandaloneCallbacks,
	})
}
