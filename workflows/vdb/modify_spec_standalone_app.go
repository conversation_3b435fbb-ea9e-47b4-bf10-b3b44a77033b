/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/05/11
*/

/*
DESCRIPTION
纵向扩缩容workflow

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"DestSpec": "vdb.dc.small"
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/resize"
	restartdatanode "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/restart"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/specification"
)

const (
	WorkflowModifySpecStandalone            = "vdb-modify-spec-standalone-app"
	StepModifySpecStandaloneBuildMeta       = "vdb-modify-spec-standalone-app-build-meta"
	StepModifySpecStandaloneFillSpec        = "vdb-modify-spec-standalone-fill-spec"
	StepModifySpecStandaloneResizeCds       = "vdb-modify-spec-standalone-resize-cds"
	StepModifySpecStandaloneChangeFsMeta    = "vdb-modify-spec-standalone-change-fs-meta"
	StepModifySpecStandaloneResize          = "vdb-modify-spec-standalone-resize"
	StepModifySpecStandaloneDataNodeReStart = "vdb-modify-spec-standalone-data-node-restart"
	StepModifySpecStandaloneCommitSpec      = "vdb-modify-spec-standalone-commit-spec"
	StepModifySpecStandaloneSuccessCallback = "vdb-modify-spec-standalone-succ-cb"

	StepModifySpecStandaloneRollbackSpec     = "vdb-modify-spec-standalone-rollback-spec"
	StepModifySpecStandaloneRollbackCallback = "vdb-modify-spec-standalone-rollback-cb"
)

func init() {
	// Step-1 修改x1元数据库
	// 将所有分片的cluster.DestSpec 修改为 param.DataNodeSpec
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneBuildMeta,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForModifySpec,
		SuccessNextStep: StepModifySpecStandaloneFillSpec,
		ErrorNextStep:   StepModifySpecStandaloneBuildMeta,
	})

	// Step-2 填写详细配置
	// 将规格信息填入Cluster表或Interface表；
	// 若cluster.DestSpec != cluster.Spec则从x1-base的spec-conf中获取NodeType的具体规格
	// 包括cpu、mem、root/data disk、instanceType并填入x1元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneFillSpec,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepModifySpecStandaloneResizeCds,
		ErrorNextStep:   StepModifySpecStandaloneFillSpec,
	})

	// Step-3 修改cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneResizeCds,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     resize.ProcessResizeStandaloneCds,
		SuccessNextStep: StepModifySpecStandaloneChangeFsMeta,
		ErrorNextStep:   StepModifySpecStandaloneResizeCds,
	})
	// Step-4 修改fs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneChangeFsMeta,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     resize.ProcessStandaloneResizeFs,
		SuccessNextStep: StepModifySpecStandaloneResize,
		ErrorNextStep:   StepModifySpecStandaloneChangeFsMeta,
	})
	// Step-5 修改节点规格(cpu/mem)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneResize,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     resize.ProcessStandaloneResizeVM,
		SuccessNextStep: StepModifySpecStandaloneDataNodeReStart,
		ErrorNextStep:   StepModifySpecStandaloneResize,
	})
	// Step-6 拉起进程
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneDataNodeReStart,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     restartdatanode.ProcessModifySpecRestartStandalone,
		SuccessNextStep: StepModifySpecStandaloneCommitSpec,
		ErrorNextStep:   StepModifySpecStandaloneDataNodeReStart,
	})

	// Step-7 填入规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneCommitSpec,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     specification.ProcessCommitSpec,
		SuccessNextStep: StepModifySpecStandaloneSuccessCallback,
		ErrorNextStep:   StepModifySpecStandaloneCommitSpec},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-8 变更成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSuccessCallback,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     callback.ProcessStandaloneModifySpecSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifySpecStandaloneSuccessCallback,
	})

	// Step-Error-01 回滚规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneRollbackSpec,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     specification.ProcessRollbackSpec,
		SuccessNextStep: StepModifySpecStandaloneRollbackCallback,
		ErrorNextStep:   StepModifySpecStandaloneRollbackSpec,
	})

	// Step-Error-02 失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneRollbackCallback,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     callback.ProcessModifySpecErrorCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifySpecStandaloneRollbackCallback,
	})
}
