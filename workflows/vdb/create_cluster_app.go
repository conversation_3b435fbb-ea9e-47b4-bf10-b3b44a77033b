/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2023/11/13, by <PERSON><PERSON><PERSON><PERSON><PERSON>(z<PERSON><PERSON><PERSON><PERSON>@baidu.com), for vdb
*/

/*
DESCRIPTION
创建VDB实例WORKFLOW

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"Name": "vdb-kx42u7p4",
	"UserID": "680cdfe705434d53b13a156284ed973b",
	"Port": 6379,
	"VpcID": "5c03c3e6-c38e-44e8-a50c-0648cff93faf"
	"AzInfos": [{
		"Zone": "zoneA",
		"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
	}],
	"NodeSpec": "cache.n1.small",
	"DataNodeNum": 4,
	"proxyNum":2,
}
*/

package workflows

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/applyresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	checksubnets "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/check_subnets"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/delresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/deploy"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/dns"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/endpoint"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/pushflag"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/opmonitor"
	securitygroup "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/security_group"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/specification"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/tls"
)

const (
	WorkflowCreateCluster                                          = "vdb-create-cluster-app"
	StepCreateClusterBuildMeta                                     = "vdb-create-cluster-app-build-meta"
	StepCreateClusterFillSpec                                      = "vdb-create-cluster-fill-spec"
	StepCreateClusterCheckSubnetsIpv6                              = "vdb-create-cluster-check-subnets-ipv6"
	StepCreateClusterCheckSubnetsEnoughIps                         = "vdb-create-cluster-check-subnets-enough-ips"
	StepCreateClusterStartInitBlbAndEndpoint                       = "vdb-create-cluster-start-init-blb-and-endpoint"
	StepCreateClusterCreateSecurityGroups                          = "vdb-create-cluster-create-security-groups"
	StepCreateOrUpdateCertificate                                  = "vdb-create-or-update-certificate"
	StepCreateClusterApplyResources                                = "vdb-create-cluster-apply-resources"
	StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource = "vdb-create-cluster-apply-resources-start-sub-tasks-after-apply-resource"
	StepCreateClusterDeploy                                        = "vdb-create-cluster-apply-deploy"
	StepCreateClusterInitAppInXmaster                              = "vdb-create-cluster-init-app-in-xmaster"
	StepCreateClusterUpdatePushFlagAllTrue                         = "vdb-create-cluster-update-push-flag-all-true"
	StepCreateClusterInitMaster                                    = "vdb-create-cluster-init-master"
	StepCreateClusterCreateCallback                                = "vdb-create-cluster-create-cb"

	StepCreateClusterRollbackCallback             = "vdb-create-cluster-rollback-callback"
	StepCreateClusterRollbackDeleteAppFromXmaster = "vdb-create-cluster-rollback-delete-app-from-xmaster"
	StepCreateClusterRollbackDeleteDNS            = "vdb-create-cluster-rollback-delete-dns"
	StepCreateClusterRollbackDeleteEndpoint       = "vdb-create-cluster-rollback-delete-endpoint"
	StepCreateClusterRollbackDeleteBLB            = "vdb-create-cluster-rollback-delete-blb"
	StepCreateClusterRollbackReleaseResources     = "vdb-create-cluster-rollback-release-resource"

	WorkflowCreateClusterInitBlbAndEndpoint            = "vdb-create-cluster-init-blb-and-endpoint"
	StepCreateClusterInitBlbAndEndpointInitBLB         = "vdb-create-cluster-init-blb-and-endpoint-init-blb"
	StepCreateClusterInitBlbAndEndpointCreateEndpoint  = "vdb-create-cluster-init-blb-and-endpoint-create-endpoint"
	StepCreateClusterInitBlbAndEndpointCreateAppDomain = "vdb-create-cluster-init-blb-and-endpoint-create-app-domain"

	WorkflowCreateClusterUpdateConfig     = "vdb-create-cluster-update-config"
	StepCreateClusterUpdateConfigUpdateSg = "vdb-create-cluster-update-config-update-sg"

	WorkflowCreateClusterInitBlbRs  = "vdb-create-cluster-init-blb-rs"
	StepCreateClusterInitBlbRsSetRs = "vdb-create-cluster-init-blb-rs-set-rs"

	WorkflowCreateClusterInitOpMonitor              = "vdb-create-cluster-init-op-monitor"
	StepCreateClusterInitOpMonitorCreateBnsService  = "vdb-create-cluster-init-op-monitor-create-bns-service"
	StepCreateClusterInitOpMonitorCreateBnsInstance = "vdb-create-cluster-init-op-monitor-create-bns-instance"
)

func init() {
	// Step-01 初始化x1数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterBuildMeta,
		Workflow: WorkflowCreateCluster,

		StepProcess: buildmeta.ProcessBuildMetaForCreatingCluster,

		SuccessNextStep: StepCreateClusterFillSpec,
		ErrorNextStep:   StepCreateClusterBuildMeta,
	})

	// Step-02 计算规格并填入
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterFillSpec,
		Workflow: WorkflowCreateCluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepCreateClusterCheckSubnetsIpv6,
		ErrorNextStep:   StepCreateClusterFillSpec,
	})

	// Step-03 检查子网是否为ipv6
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCheckSubnetsIpv6,
		Workflow: WorkflowCreateCluster,

		StepProcess: checksubnets.CheckIPV6,

		SuccessNextStep: StepCreateClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepCreateClusterCheckSubnetsIpv6,
	})

	// Step-04 检查子网中ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCheckSubnetsEnoughIps,
		Workflow: WorkflowCreateCluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepCreateClusterStartInitBlbAndEndpoint,
		ErrorNextStep:   StepCreateClusterCheckSubnetsEnoughIps,
	})

	// Step-04-1 加快创建流程，并行进行blb和endpoint的创建
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterStartInitBlbAndEndpoint,
		Workflow: WorkflowCreateCluster,

		StepProcess: ProcessCreateClusterStartWorkflowInitBlbAndEndpoint,

		SuccessNextStep: StepCreateClusterCreateSecurityGroups,
		ErrorNextStep:   StepCreateClusterStartInitBlbAndEndpoint,
	})

	// Step-05 创建安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCreateSecurityGroups,
		Workflow: WorkflowCreateCluster,

		StepProcess: securitygroup.ProcessInitSecurityGroupCluster,

		SuccessNextStep: StepCreateOrUpdateCertificate,
		ErrorNextStep:   StepCreateClusterCreateSecurityGroups,
	})

	// 创建证书
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateOrUpdateCertificate,
		Workflow: WorkflowCreateCluster,

		StepProcess: tls.ProcessCreateOrUpdateCertificate,

		SuccessNextStep: StepCreateClusterApplyResources,
		ErrorNextStep:   StepCreateOrUpdateCertificate,
	})

	// Step-06 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterApplyResources,
		Workflow: WorkflowCreateCluster,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource,
		ErrorNextStep:   StepCreateClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource,
		Workflow: WorkflowCreateCluster,

		StepProcess: ProcessCreateClusterStartWorkflowsAfterApplyResources,

		SuccessNextStep: StepCreateClusterDeploy,
		ErrorNextStep:   StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource,
	})

	// Step-07 部署所有节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterDeploy,
		Workflow: WorkflowCreateCluster,

		StepProcess: deploy.ProcessDeployAllForNewCreate,

		SuccessNextStep: StepCreateClusterInitAppInXmaster,
		ErrorNextStep:   StepCreateClusterDeploy},

		workflow.WithStepTimeout(15*time.Minute),
	)

	// Step-8 集群注册到xmaster：拓扑信息、监控策略、自愈开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterInitAppInXmaster,
		Workflow: WorkflowCreateCluster,

		StepProcess: xmaster.ProcessInitAppInXmaster,

		SuccessNextStep: StepCreateClusterUpdatePushFlagAllTrue,
		ErrorNextStep:   StepCreateClusterInitAppInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterUpdatePushFlagAllTrue,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagAllTrue,
		SuccessNextStep: StepCreateClusterInitMaster,
		ErrorNextStep:   StepCreateClusterUpdatePushFlagAllTrue,
	})

	//注册datanode到master
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitMaster,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     mochow.ProcessInitCluster,
		SuccessNextStep: StepCreateClusterCreateCallback,
		ErrorNextStep:   StepCreateClusterInitMaster,
	})

	// Step-10 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCreateCallback,
		Workflow: WorkflowCreateCluster,

		StepProcess: callback.ProcessCreateSuccessCb,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterCreateCallback,
	})

	// Step-Error-01 创建失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackCallback,
		Workflow: WorkflowCreateCluster,

		StepProcess: callback.ProcessCreateErrorCb,

		SuccessNextStep: StepCreateClusterRollbackDeleteAppFromXmaster,
		ErrorNextStep:   StepCreateClusterRollbackCallback,
	})

	// Step-Error-02 创建失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteAppFromXmaster,
		Workflow: WorkflowCreateCluster,

		StepProcess: xmaster.ProcessDeleteAppFromXmaster,

		SuccessNextStep: StepCreateClusterRollbackDeleteDNS,
		ErrorNextStep:   StepCreateClusterRollbackDeleteAppFromXmaster,
	})

	// 删除 Dns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterRollbackDeleteDNS,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     dns.ProcessDeleteAppDomain,
		SuccessNextStep: StepCreateClusterRollbackDeleteEndpoint,
		ErrorNextStep:   StepCreateClusterRollbackDeleteDNS},

		workflow.WithMaxReentry(2, StepCreateClusterRollbackDeleteEndpoint))

	// Step-Error 回滚 Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteEndpoint,
		Workflow: WorkflowCreateCluster,

		StepProcess: endpoint.ProcessDeleteRwEndpoint,

		SuccessNextStep: StepCreateClusterRollbackDeleteBLB,
		ErrorNextStep:   StepCreateClusterRollbackDeleteEndpoint,
	})

	// Step-Error-03 回滚资源申请
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteBLB,
		Workflow: WorkflowCreateCluster,

		StepProcess: blb.ProcessDelBLB,

		SuccessNextStep: StepCreateClusterRollbackReleaseResources,
		ErrorNextStep:   StepCreateClusterRollbackDeleteBLB,
	})

	// Step-Error-06 回滚BLB资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackReleaseResources,
		Workflow: WorkflowCreateCluster,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepCreateClusterRollbackReleaseResources,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbAndEndpointInitBLB,
		Workflow:        WorkflowCreateClusterInitBlbAndEndpoint,
		StepProcess:     blb.ProcessInitAppBLB,
		SuccessNextStep: StepCreateClusterInitBlbAndEndpointCreateEndpoint,
		ErrorNextStep:   StepCreateClusterInitBlbAndEndpointInitBLB,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbAndEndpointCreateEndpoint,
		Workflow:        WorkflowCreateClusterInitBlbAndEndpoint,
		StepProcess:     endpoint.ProcessCreateRwEndpoint,
		SuccessNextStep: StepCreateClusterInitBlbAndEndpointCreateAppDomain,
		ErrorNextStep:   StepCreateClusterInitBlbAndEndpointCreateEndpoint,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbAndEndpointCreateAppDomain,
		Workflow:        WorkflowCreateClusterInitBlbAndEndpoint,
		StepProcess:     dns.ProcessCreateAppDomain,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitBlbAndEndpointCreateAppDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitOpMonitorCreateBnsService,
		Workflow:        WorkflowCreateClusterInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorBnsService,
		SuccessNextStep: StepCreateClusterInitOpMonitorCreateBnsInstance,
		ErrorNextStep:   StepCreateClusterInitOpMonitorCreateBnsService,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitOpMonitorCreateBnsInstance,
		Workflow:        WorkflowCreateClusterInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitOpMonitorCreateBnsInstance,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbRsSetRs,
		Workflow:        WorkflowCreateClusterInitBlbRs,
		StepProcess:     blb.ProcessSetProxyRs,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitBlbRsSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterUpdateConfigUpdateSg,
		Workflow:        WorkflowCreateClusterUpdateConfig,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterUpdateConfigUpdateSg,
	})

}

func ProcessCreateClusterStartWorkflowInitBlbAndEndpoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateClusterInitBlbAndEndpoint,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_and_endpoint",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}})
}

func ProcessCreateClusterStartWorkflowsAfterApplyResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateClusterUpdateConfig,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_update_config",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}, {
		WorkFlow:   WorkflowCreateClusterInitBlbRs,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_rs",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}}

	if !privatecloud.IsPrivateENV() {
		params = append(params, &iface.CreateTaskParams{
			WorkFlow:   WorkflowCreateClusterInitOpMonitor,
			Schedule:   time.Now(),
			Mutex:      teu.Entity + "_init_op_monitor",
			Entity:     teu.Entity,
			Parameters: teu.Parameters,
		})
	}

	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, params)
}
