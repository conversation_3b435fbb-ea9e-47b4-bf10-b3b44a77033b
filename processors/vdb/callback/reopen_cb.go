/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
暂停成功后回调操作
*/

package callback

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessReOpenCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// billing停止订单需要一定时间，这里等待1min，避免billing还没停止订单，就开始回调
	// 这是一个短期方案，后续需要优化
	resource.LoggerTask.Notice(ctx, "wait 1 min for billing stop order")
	time.Sleep(1 * time.Minute)
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	return nil
}
