/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessRollbackBccResources 申请bcc资源
// 1. 遍历所有node、proxy，获取需要删除的bcc
// 2. 调用bcc接口删除资源
func ProcessRollbackBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	inUseMap := make(map[string]bool)
	delBccReq := &bccresource.DeleteBccResourceParams{
		UserID:    app.UserID,
		IsShortID: false,
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				inUseMap[node.ResourceOrderID] = true
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				inUseMap[proxy.ResourceOrderID] = true
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToCreate {
			inUseMap[master.ResourceOrderID] = true
		}

	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToCreate && len(node.ResourceID) != 0 {
				if _, has := inUseMap[node.ResourceOrderID]; !has {
					delBccReq.InstanceIds = append(delBccReq.InstanceIds, node.ResourceID)
				}
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate && len(proxy.ResourceID) != 0 {
				if _, has := inUseMap[proxy.ResourceOrderID]; !has {
					delBccReq.InstanceIds = append(delBccReq.InstanceIds, proxy.ResourceID)
				}
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToCreate && len(master.ResourceID) != 0 {
			if _, has := inUseMap[master.ResourceOrderID]; !has {
				delBccReq.InstanceIds = append(delBccReq.InstanceIds, master.ResourceID)
			}
		}
	}

	err = bccresource.BccResourceOp().DeleteBccResource(ctx, delBccReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "delete bcc resource failed", logit.Error("error", err))
	}

	if err := bccresource.BccResourceOp().SendRollbackTask(ctx, &bccresource.RollbackBccResourceParams{
		TaskID: teu.TaskID,
		UserID: app.UserID,
		AppID:  app.AppID,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "send rollback bcc resource task failed", logit.Error("error", err))
		return err
	}
	return nil
}
