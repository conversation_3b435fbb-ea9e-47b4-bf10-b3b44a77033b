package util

import "testing"

func Test_getImageIdAndVersion(t *testing.T) {
	type args struct {
		dbImageID string
	}
	tests := []struct {
		name        string
		args        args
		wantImageID string
		wantVersion string
	}{
		{
			name: "normal",
			args: args{
				dbImageID: "e9e9af52-acad-4f4f-9f89-83f857e327ff:20220823102819",
			},
			wantImageID: "e9e9af52-acad-4f4f-9f89-83f857e327ff",
			wantVersion: "20220823102819",
		},
		{
			name: "only-image-id",
			args: args{
				dbImageID: "e9e9af52-acad-4f4f-9f89-83f857e327ff",
			},
			wantImageID: "e9e9af52-acad-4f4f-9f89-83f857e327ff",
			wantVersion: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotImageID, gotVersion := GetImageIDAndVersion(tt.args.dbImageID)
			if gotImageID != tt.wantImageID {
				t.Errorf("getImageIDAndVersion() gotImageId = %v, want %v", gotImageID, tt.wantImageID)
			}
			if gotVersion != tt.wantVersion {
				t.Errorf("getImageIDAndVersion() gotVersion = %v, want %v", gotVersion, tt.wantVersion)
			}
		})
	}
}
