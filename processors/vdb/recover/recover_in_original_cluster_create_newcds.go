/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/12 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	apisdkiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func getSnapshotID(ctx context.Context, app *vdbmodel.Application, nodeID string,
	RevoverParam *apisdkiface.RecoverParam) (snapshotID string) {
	for _, item := range RevoverParam.NodeRecoverItems {
		if item.RecoverNodeID == nodeID {
			snapshotID = item.ObjectKey
			break
		}
	}

	if !strings.HasPrefix(snapshotID, "s-") {
		errMsg := "snapshotID is not valid"
		resource.LoggerTask.Notice(ctx, errMsg, logit.String("appID:", base_utils.Format(app.AppID)),
			logit.String("nodeID:", base_utils.Format(nodeID)), logit.String("snapshotID:", base_utils.Format(snapshotID)),
			logit.String("recoverParam:", base_utils.Format(RevoverParam)))
		return ""
	}

	resource.LoggerTask.Notice(ctx, "get target backup info",
		logit.String("appID:", base_utils.Format(app.AppID)),
		logit.String("nodeID:", base_utils.Format(nodeID)), logit.String("snapshotID:", base_utils.Format(snapshotID)))
	return snapshotID
}

// 支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度1-65。
func getNewCDSName(taskID string, nodeID string) string {
	nodeIDSplitStrs := strings.Split(nodeID, "-")
	if len(nodeIDSplitStrs) != 5 {
		return "r_" + taskID
	}
	return "r_" + taskID + "_" + nodeIDSplitStrs[3] + "-" + nodeIDSplitStrs[4]
}

func LoadSnapshotRestoreNewCDSID(ctx context.Context, taskID string, nodeID string) (string, error) {
	key := "snapshot_restore_newcds:" + taskID + ":" + nodeID
	newCDSID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return newCDSID, nil
}

func saveSnapshotRestoreNewCDSID(ctx context.Context, taskID string, nodeID string, newCDSID string) error {
	key := "snapshot_restore_newcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.SetNX(ctx, key, newCDSID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteSnapshotRestoreNewCDSID(ctx context.Context, taskID string, nodeID string) error {
	key := "snapshot_restore_newcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processRecoverClusterCreateNewCDS(ctx context.Context, taskID string, app *vdbmodel.Application, nodeID string,
	RevoverParam *apisdkiface.RecoverParam) error {
	engine := util.GetEngineByID(ctx, app, nodeID)
	if engine == "" {
		resource.LoggerTask.Error(ctx, "engine get by id failed", logit.String("nodeID", nodeID))
		return errors.New("engine get by id failed")
	}

	logicZone := ""
	resourceID := ""
	diskSize := int64(0)

	switch engine {
	case vdbmodel.EngineVDBDataNode:
		node, cluster, err := buildmeta.FindNodeByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}

		logicZone = node.LogicZone
		resourceID = node.ResourceID
		diskSize = cluster.DiskSize

	case vdbmodel.EngineVDBProxy:
		resource.LoggerTask.Error(ctx, "wrong engine", logit.String("engine", engine))
		return errors.New("engine not support")

	case vdbmodel.EngineVDBMaster:
		master, err := buildmeta.FindMasterByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "master not found", logit.Error("error", err))
			return err
		}

		logicZone = master.LogicZone
		resourceID = master.ResourceID
		diskSize = master.DiskSize
	}

	// 尝试从 broker-Redis 中获取 newCDS ID
	newCDSID, err := LoadSnapshotRestoreNewCDSID(ctx, taskID, nodeID)
	if err == nil && len(newCDSID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have create newCDSID: %s", newCDSID))
	}

	// 若无 newCDSID 则创建 CDS ID
	if newCDSID == "" {
		snapshotID := getSnapshotID(ctx, app, nodeID, RevoverParam)
		if snapshotID == "" {
			return errors.New("snapshotID is not valid")
		}

		// create cds
		newCDSID, err = bccresource.BccResourceOp().CreateCds(ctx, &bccresource.CreateCdsParam{
			UserID:      app.UserID,
			Name:        getNewCDSName(taskID, nodeID),
			Description: "for recover",
			CdsSizeInGB: int(diskSize),
			SnapshotID:  snapshotID,
			ZoneName:    logicZone,
		})

		if err != nil {
			resource.LoggerTask.Warning(ctx, "create cds fail",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.Error("Error", err))
			return err
		}

		if err := saveSnapshotRestoreNewCDSID(ctx, taskID, nodeID, newCDSID); err != nil {
			resource.LoggerTask.Warning(ctx, "save new cdsid to redis failed",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.String("newCDSID:", base_utils.Format(newCDSID)),
				logit.Error("error", err))
		}
	}

	// 对 CDS 进行 attach 操作
	for {
		// 获取 CDS 详情
		resp, err := bccresource.BccResourceOp().GetCdsDetail(ctx, &bccresource.GetCdsDetailParam{
			UserID:   app.UserID,
			VolumeID: newCDSID,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cds detail faield",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.String("newCDSID:", base_utils.Format(newCDSID)),
				logit.Error("error", err))
			return err
		}

		switch resp.Status {
		case "Available":
			err := bccresource.BccResourceOp().AttachCds(ctx, &bccresource.AttachCdsParam{
				UserID:   app.UserID,
				VolumeID: newCDSID,
				VmID:     resourceID,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "cds detail failed",
					logit.String("NodeID:", base_utils.Format(nodeID)),
					logit.String("appID:", base_utils.Format(app.AppID)),
					logit.String("newCDSID:", base_utils.Format(newCDSID)),
					logit.Error("error", err))
				return err
			}
		case "InUse":
			resource.LoggerTask.Notice(ctx, "cds status is inuse",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.String("newCDSID:", base_utils.Format(newCDSID)))
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}
