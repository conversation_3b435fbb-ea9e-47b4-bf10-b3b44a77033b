/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/25
 * File: version.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package util TODO package function desc
package util

import (
	"strings"

	"github.com/spf13/cast"
)

func GetBigVersion(version string) int {
	splitV := strings.Split(version, ".")
	if len(version) == 0 || len(splitV) == 0 {
		return -1
	}
	return cast.ToInt(splitV[0])
}
