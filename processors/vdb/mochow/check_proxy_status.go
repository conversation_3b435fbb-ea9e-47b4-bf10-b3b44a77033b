/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func checkProxyStatusIsAlive(ctx context.Context, app *vdbmodel.Application) error {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	listReq := mochow.ListProxyRequest{
		Token: "default_token",
	}

	listRsp, err := mochow.ListProxy(ctx, masterServer, &listReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list proxy failed", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "list proxy", logit.String("rsp :", base_utils.Format(listRsp)))

	if listRsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list proxy failed", logit.Int32("status_code", listRsp.Status.Code))
		return fmt.Errorf(listRsp.Status.Msg)
	}

	if len(listRsp.ProxyList) == 0 {
		resource.LoggerTask.Error(ctx, "proxy list is empty", logit.String("rsp :", base_utils.Format(listRsp)))
		return fmt.Errorf("proxy list is empty")
	}

	for _, proxy := range listRsp.ProxyList {
		resource.LoggerTask.Trace(ctx, "check proxy", logit.String("proxy :", base_utils.Format(proxy)))
		if !proxy.IsAlive {
			resource.LoggerTask.Error(ctx, "proxy is not alive", logit.String("proxy_info", base_utils.Format(proxy)))
			return fmt.Errorf("proxy is not alive")
		}
	}

	return nil
}
