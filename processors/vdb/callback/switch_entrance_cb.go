/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
暂停成功后回调操作
*/

package callback

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessSwitchEntranceCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	firstAppID := param.SwitchEntranceParam.FirstAppID
	SecondAppID := param.SwitchEntranceParam.SecondAppID

	err = switchBlbInfo(ctx, firstAppID, SecondAppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "switch blb info error", logit.Error("error", err))
		return err
	}

	firstApp, err := vdbmodel.ApplicationGetByAppID(ctx, firstAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get first app fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	SecondApp, err := vdbmodel.ApplicationGetByAppID(ctx, SecondAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get second app fail", logit.String("appId", SecondAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	firstAppDomain := firstApp.Domain
	firstAppEIP := firstApp.EIP
	firstAppPubilicDomain := firstApp.PublicDomain
	firstAppPort := firstApp.Port

	secondAppDomain := SecondApp.Domain
	secondAppEIP := SecondApp.EIP
	secondAppPubilicDomain := SecondApp.PublicDomain
	secondAppPort := SecondApp.Port

	firstApp.Status = vdbmodel.AppStatusRunning
	firstApp.Domain = secondAppDomain
	if secondAppEIP != "" {
		firstApp.EIP = secondAppEIP
	}

	if secondAppPubilicDomain != "" {
		firstApp.PublicDomain = secondAppPubilicDomain
	}

	SecondApp.Status = vdbmodel.AppStatusRunning
	SecondApp.Domain = firstAppDomain

	if firstAppEIP != "" {
		SecondApp.EIP = firstAppEIP
	}

	if firstAppPubilicDomain != "" {
		SecondApp.PublicDomain = firstAppPubilicDomain
	}

	if firstAppPort != secondAppPort {
		firstApp.Port = secondAppPort
		SecondApp.Port = firstAppPort
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{firstApp}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{SecondApp}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}

func switchBlbInfo(ctx context.Context, firstAppID string, secondAppID string) error {
	firstAppBlbList, err := vdbmodel.BLBGetAllByCond(ctx, "app_id = ?", firstAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get first app blb list fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	secondAppBlbList, err := vdbmodel.BLBGetAllByCond(ctx, "app_id = ?", secondAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get second app blb list fail", logit.String("appId", secondAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	for i := range firstAppBlbList {
		firstAppBlbList[i].AppID = secondAppID
		firstAppBlbList[i].Name = strings.ReplaceAll(firstAppBlbList[i].Name, firstAppID, secondAppID)
	}

	for i := range secondAppBlbList {
		secondAppBlbList[i].AppID = firstAppID
		secondAppBlbList[i].Name = strings.ReplaceAll(secondAppBlbList[i].Name, secondAppID, firstAppID)
	}

	if err := vdbmodel.BLBsSave(ctx, firstAppBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", firstAppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if err := vdbmodel.BLBsSave(ctx, secondAppBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", secondAppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}
