/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* md5get.go */
/*
modification history
--------------------
2023/04/19 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package remotessh

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"golang.org/x/crypto/ssh"
)

func GetFileMd5SumBySSH(ctx context.Context, filePATH string, rootPassword string, floatingIP string, resourceID string, resourceType string) (string, error) {
	if filePATH == "" {
		return "", errors.New("file path is empty")
	}
	sshConf := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.Password(rootPassword),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         2 * time.Minute,
	}
	sshCmd := fmt.Sprintf("md5sum %s| awk '{print $1}'", filePATH)
	if resourceType == "container" {
		sshCmd = fmt.Sprintf("docker exec %s bash -c \"md5sum %s| awk '{print $1}'\"", resourceID, filePATH)
	}
	md5str, err := RunCmdRemote(ctx, sshConf, floatingIP, "22", sshCmd)
	return strings.TrimSpace(md5str), err
}
