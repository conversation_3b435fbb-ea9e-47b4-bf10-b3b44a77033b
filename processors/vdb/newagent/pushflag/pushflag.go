package pushflag

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

type XagentRequest struct {
	TargetPushFlag bool `json:"target_push_flag"`
}

func ProcessUpdatePushFlagAllTrue(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processUpdatePushFlag(ctx, teu, map[string]bool{"*": true})
}

func ProcessUpdatePushFlagForReplaceNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processUpdatePushFlag(ctx, teu, map[string]bool{"*": true, vdbmodel.NodeOrProxyStatusToCreate: true,
		vdbmodel.NodeOrProxyStatusToDelete: false, vdbmodel.NodeOrProxyStatusToFakeDelete: false})
}

func processUpdatePushFlag(ctx context.Context, teu *workflow.TaskExecUnit, statusPushFlagMap map[string]bool) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}

	reqList := make([]*xagent.AsyncRequest, 0)
	toFakeDeleteReqList := make([]*xagent.AsyncRequest, 0)
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != vdbmodel.NodeOrProxyStatusToDelete && node.Status != vdbmodel.NodeOrProxyStatusToFakeDelete &&
				node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "update_push_flag",
				Params: &XagentRequest{
					TargetPushFlag: getPushFlag(node.Status, statusPushFlagMap),
				},
			}
			if node.Status == vdbmodel.NodeOrProxyStatusToCreate {
				reqList = append(reqList, req)
			} else {
				toFakeDeleteReqList = append(toFakeDeleteReqList, req)
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToDelete && proxy.Status != vdbmodel.NodeOrProxyStatusToFakeDelete &&
				proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "update_push_flag",
				Params: &XagentRequest{
					TargetPushFlag: getPushFlag(proxy.Status, statusPushFlagMap),
				},
			}
			if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate {
				reqList = append(reqList, req)
			} else {
				toFakeDeleteReqList = append(toFakeDeleteReqList, req)
			}
		}
	}
	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToDelete && master.Status != vdbmodel.NodeOrProxyStatusToFakeDelete &&
			master.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}
		req := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "update_push_flag",
			Params: &XagentRequest{
				TargetPushFlag: getPushFlag(master.Status, statusPushFlagMap),
			},
			Product: vdbmodel.ProductVDB,
		}
		if master.Status == vdbmodel.NodeOrProxyStatusToCreate {
			reqList = append(reqList, req)
		} else {
			toFakeDeleteReqList = append(toFakeDeleteReqList, req)
		}
	}

	g := gtask.Group{Concurrent: 25}
	for _, req := range reqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update push flag fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "tocreate node update push flag fail,",
			logit.Error("err", err))
		return err
	}

	// 非创建场景下允许设置失败
	gg := gtask.Group{Concurrent: 25, AllowSomeFail: true}
	for _, req := range toFakeDeleteReqList {
		req := req
		gg.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update push flag fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "to fake delete node update push flag fail, ignore...",
			logit.Error("err", err))
		return nil
	}
	return nil
}

func getPushFlag(status string, statusPushFlagMap map[string]bool) bool {
	if _, found := statusPushFlagMap[status]; found {
		return statusPushFlagMap[status]
	}
	if statusPushFlagMap["*"] {
		return true
	}
	return false
}
