/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/11/30, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Backend的接口定义
*/

package backend

import (
	"context"
	"time"
)

// Worker的执行状态的定义
const (
	// 空闲状态
	WorkerStatusIdle string = "idle"

	// 运行状态
	WorkerStatusRunning string = "running"

	// 非正常状态，如连续的访问redis报错
	WorkerStatusAbnormal string = "abnormal"
)

// TaskExecResult Status的定义
const (
	TaskExecResultStatusSuccess = "success"
	TaskExecResultStatusError   = "error"
	TaskExecResultStatusRunning = "running"
)

// TaskExecResult 框架Worker执行完成后，生成的执行结果
type TaskExecResult struct {
	TaskExecUnitID string
	TaskID         string
	TaskBatchID    string
	Entity         string
	StartAt        time.Time
	EndAt          time.Time
	Status         string
	Message        string
}

// WorkerStatus Worker的状态信息
type WorkerStatus struct {
	WorkerID string
	Status   string
}

// Backend 接口定义
type Backend interface {
	// 报告TaskExecUnit的执行结果
	Report(ctx context.Context, taskExecRet *TaskExecResult) error

	// 获取TaskExecUnit的执行结果
	GetReports(ctx context.Context, batchID string) ([]TaskExecResult, error)

	// 清除Batch的执行结果
	DeleteReports(ctx context.Context, batchID string) error

	// 清除某个TaskExecResult
	DeleteReport(ctx context.Context, taskExecRet *TaskExecResult) error

	// 报告Worker的状态，由Worker的主线程定时执行
	ReportWorkerStatus(ctx context.Context, workerStatus *WorkerStatus) error

	// 获取Workers的状态
	GetWorkerStatus(ctx context.Context) ([]WorkerStatus, error)
}
