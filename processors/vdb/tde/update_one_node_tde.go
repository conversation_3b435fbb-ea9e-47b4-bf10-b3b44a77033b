/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package tde

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/kms"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-api/servers/httpserver/util/common"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

type UpdateTDEXagentRequest struct {
	EnvDir             string `json:"env_dir"`
	WorkDir            string `json:"work_dir"`
	Engine             string `json:"engine"`
	EnableEnCryption   string `json:"enable_encryption"`
	EnCryptionAlgoName string `json:"encryption_algorithm_name"`
	EnCryptionKey      string `json:"encryption_key"`
}

func ExecuteOpenMasterTDE(ctx context.Context, app *vdbmodel.Application, master *vdbmodel.Master) error {
	resource.LoggerTask.Trace(ctx, "start open master tde", logit.String("masterID", master.MasterID))

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: master.FloatingIP,
			Port: int32(master.XagentPort),
		},
		Action: "update_tde",
		Params: &UpdateTDEXagentRequest{
			EnvDir:           "/home/<USER>",
			WorkDir:          "/home/<USER>/mochow-master",
			Engine:           vdbmodel.EngineVDBMaster,
			EnableEnCryption: "true",
		},
		Product:    vdbmodel.ProductVDB,
		TimeoutSec: 180,
	}

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "open mochow-master tde fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessOpenOneMasterTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetMaster *vdbmodel.Master
	find := false

OuterLoop:
	for _, master := range app.Masters {
		if master.MasterID == param.TargetNodeID && master.Status == vdbmodel.NodeOrProxyStatusModifying {
			targetMaster = master
			find = true
			break OuterLoop
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target master", logit.String("masterID", param.TargetNodeID))
		return errors.New("can not find target master")
	}

	err = ExecuteOpenMasterTDE(ctx, app, targetMaster)
	if err != nil {
		resource.LoggerTask.Error(ctx, "updtae master tde error", logit.String("master", base_utils.Format(targetMaster)), logit.Error("error", err))
		return err
	}

	return nil
}

func ExecuteOpenDatanodeTDE(ctx context.Context, app *vdbmodel.Application, node *vdbmodel.Node) error {
	TDEs, err := vdbmodel.TDEGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get tde error", logit.Error("error", err))
		return err
	}

	if len(TDEs) != 1 {
		// 已经有TDE记录,不允许再次创建
		resource.LoggerTask.Warning(ctx, "no tde record.", logit.Error("err", err))
		return common.BceInternalErrorErr
	}

	encryptionAlgoName := TDEs[0].EncryptionAlgorithm
	TDE := TDEs[0]
	decryptParams := kms.DecryptKeyParams{
		UserID:      app.UserID,
		MasterKeyID: TDE.MasterKeyID,
		TdeKey:      TDE.TdeKey,
	}

	tdeKey, err := kms.Instance().DecryptKey(ctx, &decryptParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "decrypt key failed.", logit.Error("err", err))
		return err
	}
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: "update_tde",
		Params: &UpdateTDEXagentRequest{
			EnvDir:             "/home/<USER>",
			WorkDir:            "/home/<USER>/mochow-datanode",
			Engine:             vdbmodel.EngineVDBDataNode,
			EnCryptionAlgoName: encryptionAlgoName,
			EnCryptionKey:      tdeKey,
		},
		Product:    vdbmodel.ProductVDB,
		TimeoutSec: 180,
	}

	_, err = xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "open mochow-datanode tde fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessOpenOneDatanodeTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	find := false

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusModifying {
				targetNode = node
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target node", logit.String("nodeID", param.TargetNodeID))
		return errors.New("can not find target node")
	}

	err = ExecuteOpenDatanodeTDE(ctx, app, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "update datanode error", logit.String("node", base_utils.Format(targetNode)), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessUpdateOneNodeTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.TargetNodeEngine == vdbmodel.EngineVDBMaster {
		return ProcessOpenOneMasterTDE(ctx, teu)
	} else if param.TargetNodeEngine == vdbmodel.EngineVDBDataNode {
		return ProcessOpenOneDatanodeTDE(ctx, teu)
	}

	return errors.New("not support engine")
}
