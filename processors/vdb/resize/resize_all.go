/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package resize

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	apisdkiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	WorkflowModifyNodeSpec = "vdb-modify-node-spec"
)

func ProcessResizeAllDataNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("app %s ProcessResizeAllDataNode", appID))

	for {
		complete := true
		if err := checkSubTask(ctx, teu); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "subtask status check failed", logit.Error("err", err))
		} else {
			if err := checkAndCreateResizeDataNodeSubTask(ctx, teu, appID); err != nil {
				complete = false
				resource.LoggerTask.Notice(ctx, "datanode status check failed", logit.Error("err", err))
			}
		}

		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			continue
		}
	}
}

func checkSubTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	if subTasksStatus != taskIface.SubTasksStatusSuccess {
		resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
		return fmt.Errorf("sub tasks status is not success")
	}

	return nil
}

func checkAndCreateResizeDataNodeSubTask(ctx context.Context, teu *workflow.TaskExecUnit, appID string) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	//寻找是否还有未修改的节点
	var TgtNode *vdbmodel.Node
	find := false
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToModify {
				TgtNode = node
				find = true
				break
			}
		}
	}

	if !find {
		resource.LoggerTask.Notice(ctx, "all node is modified")
		return nil
	}

	param, err := apisdkiface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	subParams := param
	subParams.TargetNodeID = TgtNode.NodeID

	err = resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowModifyNodeSpec,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "-" + TgtNode.NodeID,
		Entity:     teu.Entity,
		Parameters: subParams,
	}})
	if err != nil {
		resource.LoggerTask.Error(ctx, "create sub tasks error", logit.Error("error", err))
		return err
	}

	return fmt.Errorf("sub tasks is created ")
}
