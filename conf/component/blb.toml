[BLBConf]
    HealthCheck = "TCP"
    HealthCheckTimeoutInSecond = 3
    HealthCheckUpRetry = 3
    HealthCheckDownRetry = 3
    HealthCheckIntervalInSecond = 3
    Internal = "true"
    Product = "vdb"

[BLBTCPListenerConf]
    TCPSessionTimeout = 4000

[BLBHTTPListenerConf]
    TcpSessionTimeoutInMiliSecond = 4000
    KeepSession = false
    KeepSessionTimeout = 3600
    XForwardedFor = false
    XForwardedProto = false
    ServerTimeout = 30

[BLBEnv]
    # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/l2h2MOSkO9/SQc8DuVrFpUAjp
    ResourcePrivateUserId = "0c0b3c9dbb6e41308d3bfd587d908922"
    ResourcePrivateVpcId = "d50f04f3-bb01-4b31-9c09-ba46939308ab"
    ResourceCloudUserId = "ea2c4a2286ca4540afcb7f7d4ba2d199"
    ResourceCloudVpcId = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"

    # Sandbox privite
    [[BLBEnv.ResourcePrivateSubnet]]
        Zone = "AZONE-gzns"
        SubnetId = "e12447e0-73e3-4b7a-8097-1472e6cc91b1"
    [[BLBEnv.ResourcePrivateSubnet]]
        Zone = "AZONE-gzhxy"
        SubnetId = "ab34c1c9-7132-480d-9ed8-0dfc10728cd0"

    # Sandbox cloud
    [[BLBEnv.ResourceCloudSubnet]]
        Zone = "AZONE-gzns"
        SubnetId = "8a079a8f-c4dc-4262-912d-41b5753de450"
    [[BLBEnv.ResourceCloudSubnet]]
        Zone = "AZONE-gzhxy"
        SubnetId = "de5bff33-13bc-4588-bfe9-4fb3e08ac681"
    [[BLBEnv.ResourceCloudSubnet]]
        Zone = "AZONE-bjdd"
        SubnetId = "6d2aa95b-7932-4da2-9747-c3e0568825f6"

[BLBEnv.IDC_SPEC.testnew1]
    ResourcePrivateUserId = "0c0b3c9dbb6e41308d3bfd587d908922"

    ResourceCloudUserId = "3f201035e46c47f79dcd210505e73acb"
    ResourceCloudVpcId = "d50f04f3-bb01-4b31-9c09-ba46939308ab"
    [[BLBEnv.IDC_SPEC.testnew1.ResourceCloudSubnet]]
        Zone = "AZONE-gzns"
        SubnetId = "e12447e0-73e3-4b7a-8097-1472e6cc91b1"
    [[BLBEnv.IDC_SPEC.testnew1.ResourceCloudSubnet]]
        Zone = "AZONE-gzhxy"
        SubnetId = "ab34c1c9-7132-480d-9ed8-0dfc10728cd0"

    [BLBEnv.IDC_SPEC.bdtest]
    # BDRP "0c0b3c9dbb6e41308d3bfd587d908922"
    ResourcePrivateUserId = "0c0b3c9dbb6e41308d3bfd587d908922"
    ResourcePrivateVpcId = "8a62f411-addd-4b48-bf2c-6f61c0d1e60d"
    # SCS "38a382d468db4740837fbdca0a5e14bf"
    ResourceCloudUserId = "38a382d468db4740837fbdca0a5e14bf "
    ResourceCloudVpcId = "65de3e72-b0d8-4c62-b9ca-7df4044f44df"
    [[BLBEnv.IDC_SPEC.bdtest.ResourceCloudSubnet]]
        Zone = "AZONE-bdbl"
        SubnetId = "878bad8e-c5e0-4f51-b731-b80dd4e3e794"
    [[BLBEnv.IDC_SPEC.bdtest.ResourceCloudSubnet]]
        Zone = "AZONE-bddwd"
        SubnetId = "8a4764ba-5734-4eaf-967d-2cead5fc3eff"
    [[BLBEnv.IDC_SPEC.bdtest.ResourceCloudSubnet]]
        Zone = "AZONE-bddx"
        SubnetId = "0fe51b41-8262-4a5b-b659-755f94cc1521"
