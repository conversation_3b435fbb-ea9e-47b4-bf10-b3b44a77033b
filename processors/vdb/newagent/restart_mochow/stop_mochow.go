package restartmochow

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	StandaloneWorkDir = "/home/<USER>/mochow-standalone"
)

type XagentRequest struct {
	WorkDir string `json:"work_dir"`
}

func ProcessStopMochow(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to stop mochow in non-standalone mode")
		return nil
	}

	toCreateNodes := make([]*vdbmodel.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			toCreateNodes = append(toCreateNodes, node)
		}
	}

	if len(toCreateNodes) == 0 {
		resource.LoggerTask.Notice(ctx, "no tocreate node found")
		return errors.New("tocreate node not found")
	}
	if len(toCreateNodes) > 1 {
		resource.LoggerTask.Error(ctx, "too many tocreate nodes.", logit.String("toCreateNodes", base_utils.Format(toCreateNodes)))
		return errors.New("too many tocreate nodes")
	}

	toCreateNode := toCreateNodes[0]

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: toCreateNode.FloatingIP,
			Port: int32(toCreateNode.XagentPort),
		},
		Action: "stop_mochow",
		Params: &XagentRequest{
			WorkDir: StandaloneWorkDir,
		},
		Product: vdbmodel.ProductVDB,
	}

	g := gtask.Group{Concurrent: 5}
	g.Go(func() error {
		_, err := xagent.Instance().DoAsync(ctx, req).Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "stop mochow fail",
				logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
				logit.Error("err", err))
		}
		return err
	})

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "stop mochow failed", logit.Error("err", err))
		return err
	}

	return nil
}
