/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/04/26, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
增加node修改元数据
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func findMinZone(zoneCountMap map[string]int) (string, bool) {
	if len(zoneCountMap) == 0 {
		// 如果 map 是空的，返回默认值和标志位 false
		return "", false
	}

	minZone := ""
	minCount := math.MaxInt // 设置初始最小值为 int 的最大值

	for zone, count := range zoneCountMap {
		if count < minCount {
			minCount = count
			minZone = zone
		}
	}

	// 返回最小的 zone 和 标志位 true
	return minZone, true
}

// 选择指定zone下节点数最少的subnet
func selectMinSubnet(zoneSubnetCount map[string]map[string]int, zoneSubnets map[string][]string, zone string) (string, error) {
	minSubnet := ""
	minSubnetCount := -1
	subnets, ok := zoneSubnets[zone]
	if !ok || len(subnets) == 0 {
		return "", fmt.Errorf("no subnets found for zone %s", zone)
	}
	for _, subnet := range subnets {
		cnt := zoneSubnetCount[zone][subnet]
		if minSubnetCount == -1 || cnt < minSubnetCount {
			minSubnet = subnet
			minSubnetCount = cnt
		}
	}
	return minSubnet, nil
}

// 选择节点数最少的zone和该zone下节点数最少的subnet
func selectMinZoneAndSubnet(zoneSubnetCount map[string]map[string]int, zoneSubnets map[string][]string) (string, string, error) {
	minZone := ""
	minZoneCount := -1
	for zone := range zoneSubnets {
		total := 0
		for _, cnt := range zoneSubnetCount[zone] {
			total += cnt
		}
		if minZoneCount == -1 || total < minZoneCount {
			minZone = zone
			minZoneCount = total
		}
	}
	if minZone == "" {
		return "", "", fmt.Errorf("no zone found")
	}
	minSubnet := ""
	minSubnetCount := -1
	subnets, ok := zoneSubnets[minZone]
	if !ok || len(subnets) == 0 {
		return "", "", fmt.Errorf("no subnets found for zone %s", minZone)
	}
	for _, subnet := range subnets {
		cnt := zoneSubnetCount[minZone][subnet]
		if minSubnetCount == -1 || cnt < minSubnetCount {
			minSubnet = subnet
			minSubnetCount = cnt
		}
	}
	return minZone, minSubnet, nil
}

func addDataNodes(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	maxClusterIdx, err := util.GetMaxClusterIdx(ctx, app.Clusters)
	if err != nil {
		return err
	}
	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get max node index failed", logit.Error("error", err))
		return err
	}
	curCount := app.DataNodeNum
	firstCluster := app.Clusters[0]
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("to add %d clusters", param.DataNodeCount-curCount))

	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.AzDeployInfo), &replicas); err != nil {
		resource.LoggerTask.Error(ctx, "Unmarshal  AzDeployInfo failed", logit.Error("error", err))
		return err
	}

	// 1. 构建 zone->[]subnet 映射
	zoneSubnets := make(map[string][]string)
	for _, replica := range replicas {
		zoneSubnets[replica.Zone] = append(zoneSubnets[replica.Zone], replica.SubnetID)
	}

	// 2. 统计 zone+subnet 下已有节点数
	zoneSubnetCount := make(map[string]map[string]int)
	for zone, subnets := range zoneSubnets {
		zoneSubnetCount[zone] = make(map[string]int)
		for _, subnet := range subnets {
			zoneSubnetCount[zone][subnet] = 0
		}
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if _, ok := zoneSubnetCount[node.LogicZone]; ok {
				zoneSubnetCount[node.LogicZone][node.SubnetID]++
			}
		}
	}

	for i := 0; i < param.DataNodeCount-curCount; i++ {
		maxClusterIdx++
		cluster := &vdbmodel.Cluster{
			ClusterID:       app.AppID + "-c-" + strconv.Itoa(maxClusterIdx),
			AppID:           app.AppID,
			Engine:          vdbmodel.EngineVDBDataNode,
			Port:            firstCluster.Port,
			Status:          vdbmodel.NodeOrProxyStatusInUse,
			CreateTime:      time.Now(),
			UpdateTime:      time.Now(),
			Spec:            firstCluster.Spec,
			DestSpec:        firstCluster.DestSpec,
			StoreType:       vdbmodel.StoreTypeDRAM,
			CPU:             firstCluster.CPU,
			ActualCPU:       firstCluster.ActualCPU,
			MemSize:         firstCluster.MemSize,
			ActualMemSize:   firstCluster.ActualMemSize,
			DiskSize:        firstCluster.DiskSize,
			ActualDiskSize:  firstCluster.ActualDiskSize,
			SysDiskSize:     firstCluster.SysDiskSize,
			AvailableVolume: firstCluster.AvailableVolume,
		}
		app.Clusters = append(app.Clusters, cluster)

		maxNodeIdx++
		cluster.MaxNodeIndex = maxNodeIdx

		// 使用封装的函数选择节点数最少的zone和subnet
		minZone, minSubnet, err := selectMinZoneAndSubnet(zoneSubnetCount, zoneSubnets)
		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found zone or subnet: %v", err))
			return cerrs.Errorf("not found zone or subnet: %v", err)
		}

		azone, found := zoneMapper(minZone, true)
		if !found {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", minZone))
			return cerrs.Errorf("not found azone for lzone %s", minZone)
		}

		cluster.Nodes = append(cluster.Nodes, &vdbmodel.Node{
			NodeID:     app.AppID + "-n-" + strconv.Itoa(maxNodeIdx),
			ClusterID:  cluster.ClusterID,
			AppID:      app.AppID,
			Engine:     cluster.Engine,
			Port:       cluster.Port,
			Region:     app.Region,
			LogicZone:  minZone,
			Azone:      azone,
			VpcID:      app.VpcID,
			SubnetID:   minSubnet,
			XagentPort: vdbmodel.DefaultXagentPort,
			Status:     vdbmodel.NodeOrProxyStatusToCreate,
			Basedir:    util.DefaultBaseDir,
			NodeFixID:  app.AppID + "-n-" + strconv.Itoa(maxNodeIdx),
		})

		zoneSubnetCount[minZone][minSubnet]++
	}

	return nil
}

func addProxies(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	totalProxyCount := param.ProxyNodeCount
	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.AzDeployInfo), &replicas); err != nil {
		resource.LoggerTask.Error(ctx, "Unmarshal  AzDeployInfo failed", logit.Error("error", err))
		return err
	}

	// zoneCount 逻辑保留
	zoneCount := 0
	if len(replicas) > 1 {
		zoneCount = len(replicas)
	} else {
		zoneCount = 2
	}

	// 构建 zone->[]subnet 映射
	zoneSubnets := make(map[string][]string)
	for _, replica := range replicas {
		zoneSubnets[replica.Zone] = append(zoneSubnets[replica.Zone], replica.SubnetID)
	}

	// 统计 zone+subnet 下已有proxy数
	zoneSubnetCount := make(map[string]map[string]int)
	for zone, subnets := range zoneSubnets {
		zoneSubnetCount[zone] = make(map[string]int)
		for _, subnet := range subnets {
			zoneSubnetCount[zone][subnet] = 0
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if _, ok := zoneSubnetCount[proxy.LogicZone]; ok {
				zoneSubnetCount[proxy.LogicZone][proxy.SubnetID]++
			}
		}
	}

	if zoneCount == 0 {
		resource.LoggerTask.Error(ctx, "no available zone/subnet for proxy allocation")
		return fmt.Errorf("no available zone/subnet for proxy allocation")
	}

	totalItfCount := totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		totalItfCount++
	}

	curItfCount := len(app.Interfaces)

	needNewProxyCount := totalProxyCount - app.ProxyNum
	needNewItfCount := totalItfCount - curItfCount

	maxItfID, err := util.GetMaxInterfaceIdx(ctx, app.Interfaces)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get max interface index failed", logit.Error("error", err))
		return err
	}
	maxProxyID, err := util.GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get max proxy index failed", logit.Error("error", err))
		return err
	}
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}
	firstItf := app.Interfaces[0]

	proxyPerItf := totalProxyCount / totalItfCount
	if totalProxyCount%totalItfCount != 0 {
		proxyPerItf++
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("needNewProxyCount %d needNewItfCount %d maxItfID %d maxProxyID %d proxyPerItf %d",
		needNewProxyCount, needNewItfCount, maxItfID, maxProxyID, proxyPerItf))

	for i := 0; i < needNewItfCount; i++ {
		maxItfID++
		itf := &vdbmodel.Interface{
			InterfaceID:    app.AppID + "-itf-" + strconv.Itoa(maxItfID),
			AppID:          app.AppID,
			Engine:         vdbmodel.EngineVDBProxy,
			Port:           firstItf.Port,
			Status:         vdbmodel.NodeOrProxyStatusInUse,
			CreateTime:     time.Now(),
			UpdateTime:     time.Now(),
			StoreType:      vdbmodel.StoreTypeDRAM,
			Spec:           firstItf.Spec,
			DestSpec:       firstItf.DestSpec,
			CPU:            firstItf.CPU,
			ActualCPU:      firstItf.ActualCPU,
			MemSize:        firstItf.MemSize,
			ActualMemSize:  firstItf.ActualMemSize,
			DiskSize:       firstItf.DiskSize,
			ActualDiskSize: firstItf.ActualDiskSize,
			SysDiskSize:    firstItf.SysDiskSize,
		}

		app.Interfaces = append(app.Interfaces, itf)

		itf.MaxNodeIndex = maxProxyID
	}

	newProxyNum := 0

Outloop:
	for _, itf := range app.Interfaces {
		curProxyCountInItf := len(itf.Proxies)
		if curProxyCountInItf >= proxyPerItf {
			continue
		}
		for i := 0; i < proxyPerItf-curProxyCountInItf; i++ {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("itf_id:%s loop:%d curProxyCountInItf %d proxyPerItf %d",
				itf.InterfaceID, i, curProxyCountInItf, proxyPerItf))

			// 选择节点数最少的zone和subnet
			logicZone, subnet, err := selectMinZoneAndSubnet(zoneSubnetCount, zoneSubnets)
			if err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found zone or subnet: %v", err))
				return cerrs.Errorf("not found zone or subnet: %v", err)
			}

			azone, found := zoneMapper(logicZone, true)
			if !found {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", logicZone))
				return cerrs.Errorf("not found azone for lzone %s", logicZone)
			}

			maxProxyID++
			itf.Proxies = append(itf.Proxies, &vdbmodel.Proxy{
				ProxyID:     app.AppID + "-p-" + strconv.Itoa(maxProxyID),
				InterfaceID: itf.InterfaceID,
				AppID:       app.AppID,
				Engine:      itf.Engine,
				Port:        itf.Port,
				Region:      app.Region,
				LogicZone:   logicZone,
				Azone:       azone,
				VpcID:       app.VpcID,
				SubnetID:    subnet,
				XagentPort:  vdbmodel.DefaultXagentPort,
				Status:      vdbmodel.NodeOrProxyStatusToCreate,
				Basedir:     util.DefaultBaseDir,
				StatPort:    22222,
				NodeFixID:   app.AppID + "-p-" + strconv.Itoa(maxProxyID),
			})

			zoneSubnetCount[logicZone][subnet]++
			newProxyNum++
			if newProxyNum >= needNewProxyCount {
				resource.LoggerTask.Trace(ctx, fmt.Sprintf("itf_id:%s newProxyNum %d needNewProxyCount %d", itf.InterfaceID,
					newProxyNum, needNewProxyCount))
				break Outloop
			}
		}
	}

	return nil
}

func markShrinkDataNodes(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	needMarkNum := app.DataNodeNum - param.DataNodeCount

	for i := 0; i < needMarkNum; i++ {
		index := 0
		var lastNode *vdbmodel.Node
		// 获取当前used状态的最大索引的data node,标记为toDelete状态
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				chunks := strings.Split(node.NodeID, "-")
				curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
				if err != nil {
					resource.LoggerTask.Error(ctx, "parse node id error", logit.String("node_id", node.NodeID), logit.Error("error", err))
					return err
				}

				if node.Status == vdbmodel.NodeOrProxyStatusInUse && curIdx > index {
					index = curIdx
					lastNode = node
				}
			}
		}
		if lastNode.Status == vdbmodel.NodeOrProxyStatusInUse {
			lastNode.Status = vdbmodel.NodeOrProxyStatusToFakeDelete
			if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
				lastNode.Status = vdbmodel.NodeOrProxyStatusToDelete
			}
		}
	}

	return nil
}

func markShrinkProxies(ctx context.Context, app *vdbmodel.Application, param *iface.TaskParameters) error {
	needMarkNum := app.ProxyNum - param.ProxyNodeCount

	for i := 0; i < needMarkNum; i++ {
		index := 0
		var lastProxy *vdbmodel.Proxy
		// 获取当前used状态的最大索引的proxy,标记为toDelete状态
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxies {
				chunks := strings.Split(proxy.ProxyID, "-")
				curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
				if err != nil {
					resource.LoggerTask.Error(ctx, "parse proxy id error", logit.String("node_id", proxy.ProxyID), logit.Error("error", err))
					return err
				}

				if proxy.Status == vdbmodel.NodeOrProxyStatusInUse && curIdx > index {
					index = curIdx
					lastProxy = proxy
				}
			}
		}
		if lastProxy.Status == vdbmodel.NodeOrProxyStatusInUse {
			lastProxy.Status = vdbmodel.NodeOrProxyStatusToFakeDelete
			if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
				lastProxy.Status = vdbmodel.NodeOrProxyStatusToDelete
			}
		}
	}

	return nil
}

func ProcessModifyScale(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusModifying

	if param.DataNodeCount > app.DataNodeNum {
		if err := addDataNodes(ctx, app, param); err != nil {
			resource.LoggerTask.Error(ctx, "add DataNodes error", logit.Error("error", err))
			return err
		}
	}

	if param.ProxyNodeCount > app.ProxyNum {
		if err := addProxies(ctx, app, param); err != nil {
			resource.LoggerTask.Error(ctx, "add proxies error", logit.Error("error", err))
			return err
		}
	}

	if param.DataNodeCount < app.DataNodeNum {
		if app.DataNodeNum-param.DataNodeCount != 1 {
			resource.LoggerTask.Error(ctx, "remove data node error", logit.String("error", fmt.Sprintf("only support remove one data node at a time")))
			return cerrs.Errorf("only support remove one data node at a time")
		}

		if err := markShrinkDataNodes(ctx, app, param); err != nil {
			resource.LoggerTask.Error(ctx, "mark shrink data nodes error", logit.Error("error", err))
			return err
		}
	}

	if param.ProxyNodeCount < app.ProxyNum {
		if param.ProxyNodeCount < 2 {
			resource.LoggerTask.Error(ctx, "remove proxy node error", logit.String("error", fmt.Sprintf("proxy num must be greater than or equal to two")))
			return cerrs.Errorf("proxy num must be greater than or equal to two")
		}

		if err := markShrinkProxies(ctx, app, param); err != nil {
			resource.LoggerTask.Error(ctx, "mark shrink proxies error", logit.Error("error", err))
			return err
		}
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
