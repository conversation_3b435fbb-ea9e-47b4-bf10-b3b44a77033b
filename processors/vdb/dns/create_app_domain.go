/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
绑定app的blb到域名
*/

package dns

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dns"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessCreateAppDomain 创建app的域名
// 1. 从interface表获取app的blb ip、ipv6 blb ip（如果app是ipv6的）
// 2. 将blb ip绑定domain
// 相关代码 DnsExecutorProcessor::bind_cluster_domain
func ProcessCreateAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("CreateAppDomain fail : teu is nilptr")
	}

	if privatecloud.IsPrivateENV() {
		return nil
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	enableIpv6 := false
	if app.IPType == vdbmodel.Ipv6 {
		enableIpv6 = true
	}

	if len(app.BLBs) == 0 {
		resource.LoggerTask.Warning(ctx, "no blb", logit.String("appId", teu.Entity))
	}
	bindIP := ""
	bindIPV6 := ""
	for _, blb := range app.BLBs {
		if len(blb.EndpointIP) != 0 {
			bindIP = blb.EndpointIP
			enableIpv6 = false
			continue
		}
		if blb.IPType == vdbmodel.Ipv4 {
			bindIP = blb.Ovip
		}
		if blb.IPType == vdbmodel.Ipv6 {
			bindIPV6 = blb.Ipv6
		}
	}

	createDomainParam := dns.CreateDomainParam{
		UserID:     app.UserID,
		VpcID:      app.VpcID,
		BindIp:     bindIP,
		Domain:     app.Domain,
		EnableIpv6: enableIpv6,
		BindIpIpv6: bindIPV6,
	}
	err = dns.Instance().CreateDomain(ctx, &createDomainParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	// 让域名在公网环境可以解析
	err = CreateAppInetDomain(ctx, app.Domain, bindIP, app.UserID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create inet domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	// todo actionrecordmodel ?
	return nil
}

func CreateAppInetDomain(ctx context.Context, domain string, bindIP string, userID string) (err error) {
	parts := strings.Split(domain, ".")
	if len(parts) <= 3 {
		resource.LoggerTask.Warning(ctx, "create inet domain faield invalid domain",
			logit.String("app.Domain", domain))
		return err
	}

	parts = parts[:len(parts)-3]
	name := strings.Join(parts, ".")

	createInetDomainParam := &dns.CreateInetDomainParam{
		Domain: name,
		Eip:    bindIP,
		UserID: userID,
	}
	if err := dns.Instance().CreateInetDomain(ctx, createInetDomainParam); err != nil {
		resource.LoggerTask.Warning(ctx, "create inet domain failed",
			logit.String("app.Domain", domain), logit.Error("ComponentError", err))
		return err
	}

	return nil
}
