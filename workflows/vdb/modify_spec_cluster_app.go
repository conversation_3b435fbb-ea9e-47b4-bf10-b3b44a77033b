/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/05/11
*/

/*
DESCRIPTION
纵向扩缩容workflow

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"DestSpec": "vdb.dc.small"
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/resize"
	restartdatanode "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/restart"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/specification"
)

const (
	WorkflowModifySpecCluster                         = "vdb-modify-spec-cluster-app"
	StepModifySpecClusterBuildMeta                    = "vdb-modify-spec-cluster-app-build-meta"
	StepModifySpecClusterFillSpec                     = "vdb-modify-spec-cluster-fill-spec"
	StepModifySpecClusterCheckAndCreateResizeSubTasks = "vdb-modify-spec-cluster-check-and-create-resize-subtasks"
	StepModifySpecClusterCommitSpec                   = "vdb-modify-spec-cluster-commit-spec"
	StepModifySpecClusterSuccessCallback              = "vdb-modify-spec-cluster-succ-cb"

	WorkflowModifyNodeSpec            = "vdb-modify-node-spec"
	StepModifyNodeSpecBuildMeta       = "vdb-modify-node-spec-build-meta"
	StepModifyNodeSpecResizeCds       = "vdb-modify-node-spec-resize-cds"
	StepModifyNodeSpecChangeFsMeta    = "vdb-modify-node-spec-change-fs-meta"
	StepModifyNodeSpecResize          = "vdb-modify-node-spec-resize"
	StepModifyNodeSpecDataNodeReStart = "vdb-modify-node-spec-data-node-restart"
	StepModifyNodeSpecCheck           = "vdb-modify-node-spec-check"
	StepModifyNodeSpecCallback        = "vdb-modify-node-spec-cb"

	StepModifySpecClusterRollbackSpec     = "vdb-modify-spec-cluster-rollback-spec"
	StepModifySpecClusterRollbackCallback = "vdb-modify-spec-cluster-rollback-cb"
)

func init() {
	// Step-1 修改x1元数据库
	// 将所有分片的cluster.DestSpec 修改为 param.DataNodeSpec
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterBuildMeta,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForModifySpec,
		SuccessNextStep: StepModifySpecClusterFillSpec,
		ErrorNextStep:   StepModifySpecClusterBuildMeta,
	})

	// Step-2 填写详细配置
	// 将规格信息填入Cluster表或Interface表；
	// 若cluster.DestSpec != cluster.Spec则从x1-base的spec-conf中获取NodeType的具体规格
	// 包括cpu、mem、root/data disk、instanceType并填入x1元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterFillSpec,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepModifySpecClusterCheckAndCreateResizeSubTasks,
		ErrorNextStep:   StepModifySpecClusterFillSpec,
	})

	// Step-3 串行发起modify-node-spec子任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterCheckAndCreateResizeSubTasks,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     resize.ProcessResizeAllDataNode,
		SuccessNextStep: StepModifySpecClusterCommitSpec,
		ErrorNextStep:   StepModifySpecClusterCheckAndCreateResizeSubTasks},

		workflow.WithStepTimeout(24*time.Hour))

	// Step-4 填入规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterCommitSpec,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     specification.ProcessCommitSpec,
		SuccessNextStep: StepModifySpecClusterSuccessCallback,
		ErrorNextStep:   StepModifySpecClusterCommitSpec},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-5 变更成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterSuccessCallback,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     callback.ProcessModifySpecSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifySpecClusterSuccessCallback,
	})

	// Step-Error-01 回滚规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackSpec,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     specification.ProcessRollbackSpec,
		SuccessNextStep: StepModifySpecClusterRollbackCallback,
		ErrorNextStep:   StepModifySpecClusterRollbackSpec,
	})

	// Step-Error-02 失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackCallback,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     callback.ProcessModifySpecErrorCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifySpecClusterRollbackCallback,
	})

	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecBuildMeta,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     buildmeta.ProcessBuildMetaForModifyNodeSpec,
		SuccessNextStep: StepModifyNodeSpecResizeCds,
		ErrorNextStep:   StepModifyNodeSpecBuildMeta,
	})
	// Step-2 修改cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecResizeCds,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     resize.ProcessResizeNodeCds,
		SuccessNextStep: StepModifyNodeSpecChangeFsMeta,
		ErrorNextStep:   StepModifyNodeSpecResizeCds,
	})
	// Step-3 修改fs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecChangeFsMeta,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     resize.ProcessResizeFs,
		SuccessNextStep: StepModifyNodeSpecResize,
		ErrorNextStep:   StepModifyNodeSpecChangeFsMeta,
	})
	// Step-4 修改节点规格(cpu/mem)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecResize,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     resize.ProcessResizeVM,
		SuccessNextStep: StepModifyNodeSpecDataNodeReStart,
		ErrorNextStep:   StepModifyNodeSpecResize,
	})
	// Step-5 拉起进程
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecDataNodeReStart,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     restartdatanode.ProcessModifySpecRestartDataNode,
		SuccessNextStep: StepModifyNodeSpecCheck,
		ErrorNextStep:   StepModifyNodeSpecDataNodeReStart,
	})
	//Step-6 检查状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecCheck,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     mochow.ProcessCheckAllStatus,
		SuccessNextStep: StepModifyNodeSpecCallback,
		ErrorNextStep:   StepModifyNodeSpecCheck,
	}, workflow.WithStepTimeout(15*time.Minute))
	//Step-7 回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyNodeSpecCallback,
		Workflow:        WorkflowModifyNodeSpec,
		StepProcess:     callback.ProcessModifyOneNodeSpecSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyNodeSpecCallback,
	})
}
