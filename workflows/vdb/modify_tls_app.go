package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/tls"
)

const (
	WorkflowModifyTLS    = "vdb-modify-tls-app"
	StepApplyCertificate = "vdb-apply-certificate-app"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateOrUpdateCertificate,
		Workflow: WorkflowModifyTLS,

		StepProcess: tls.ProcessCreateOrUpdateCertificate,

		SuccessNextStep: StepApplyCertificate,
		ErrorNextStep:   StepCreateOrUpdateCertificate,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepApplyCertificate,
		Workflow:        WorkflowModifyTLS,
		StepProcess:     tls.ProcessApplyCertificate,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError},
		workflow.WithStepTimeout(5*time.Minute),
	)
}
