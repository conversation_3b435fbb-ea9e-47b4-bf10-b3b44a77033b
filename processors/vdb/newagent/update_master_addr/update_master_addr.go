/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
 */

/*
DESCRIPTION
更新master addr
*/

package updatemasteraddr

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/common"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessUpdateMasterAddr(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Notice(ctx, "app is not cluster, skip", logit.String("type", app.Type))
		return nil
	}

	/*检查是否有master发生自愈*/
	needUpdate := false
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
			needUpdate = true
		}
	}

	if !needUpdate {
		resource.LoggerTask.Notice(ctx, "no need to update master addr")
		return nil
	}

	/*获取最新的master addr*/
	newMasterAddr := ""
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
			continue
		}
		if newMasterAddr != "" {
			newMasterAddr += ","
		}
		newMasterAddr += fmt.Sprintf("%s:%d", master.IP, master.Port)
	}

	resource.LoggerTask.Notice(ctx, "to update", logit.String("newMasterAddr :", base_utils.Format(newMasterAddr)))

	/*发送请求*/
	confReqList := make([]*xagent.AsyncRequest, 0)
	envReqList := make([]*xagent.AsyncRequest, 0)
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status == vdbmodel.NodeOrProxyStatusToDelete || node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
				node.Status == vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			confReq := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "replace_master_addr",
				Params: &common.UpdateNewAddrXagentRequest{
					Meta: &xagent.Meta{
						Basedir: "/home/<USER>/mochow-datanode",
					},
					NewAddress: newMasterAddr,
				},
			}

			confReqList = append(confReqList, confReq)

			envReq := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "env_vars_op",
				Params: &common.UpdateEnvVarsXagentRequest{
					Meta: &xagent.Meta{
						Basedir: "/home/<USER>/",
					},
					Op: "set",
					ConfigList: []*common.ConfigItem{{
						Name:  "MASTER_ADDRS",
						Value: "list://" + newMasterAddr,
					}},
				},
			}

			envReqList = append(envReqList, envReq)
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToDelete || proxy.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
				proxy.Status == vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			confReq := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "replace_master_addr",
				Params: &common.UpdateNewAddrXagentRequest{
					Meta: &xagent.Meta{
						Basedir: "/home/<USER>/mochow-proxy",
					},
					NewAddress: newMasterAddr,
				},
			}

			confReqList = append(confReqList, confReq)

			envReq := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "env_vars_op",
				Params: &common.UpdateEnvVarsXagentRequest{
					Meta: &xagent.Meta{
						Basedir: "/home/<USER>/",
					},
					Op: "set",
					ConfigList: []*common.ConfigItem{{
						Name:  "MASTER_ADDRS",
						Value: "list://" + newMasterAddr,
					}},
				},
			}

			envReqList = append(envReqList, envReq)
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToDelete || master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}
		confReq := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "replace_master_addr",
			Params: &common.UpdateNewAddrXagentRequest{
				Meta: &xagent.Meta{
					Basedir: "/home/<USER>/mochow-master",
				},
				NewAddress: newMasterAddr,
			},
		}

		confReqList = append(confReqList, confReq)

		envReq := &xagent.AsyncRequest{
			Addr: &xagent.Addr{
				Host: master.FloatingIP,
				Port: int32(master.XagentPort),
			},
			Action: "env_vars_op",
			Params: &common.UpdateEnvVarsXagentRequest{
				Meta: &xagent.Meta{
					Basedir: "/home/<USER>/",
				},
				Op: "set",
				ConfigList: []*common.ConfigItem{{
					Name:  "MASTER_ADDRS",
					Value: "list://" + newMasterAddr,
				}},
			},
		}

		envReqList = append(envReqList, envReq)
	}

	g := gtask.Group{Concurrent: 25}
	var failedConfTasks []string
	for _, req := range confReqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update mochow conf master addr fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
				failedConfTasks = append(failedConfTasks, fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port))
			}
			return nil
		})
	}

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Some update mochow conf tasks failed", logit.String("tasks %s", strings.Join(failedConfTasks, ",")))
	}

	if len(failedConfTasks) > 0 {
		resource.LoggerTask.Warning(ctx, "Some update mochow conf tasks failed", logit.String("tasks %s", strings.Join(failedConfTasks, ",")))
	}

	gg := gtask.Group{Concurrent: 25}
	var failedEnvTasks []string
	for _, req := range envReqList {
		req := req
		gg.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update env vars master addr fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
				failedEnvTasks = append(failedEnvTasks, fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port))
			}
			return nil
		})
	}

	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Some update env tasks failed", logit.String("tasks %s", strings.Join(failedEnvTasks, ",")))
	}

	if len(failedEnvTasks) > 0 {
		resource.LoggerTask.Warning(ctx, "Some update env tasks failed", logit.String("tasks %s", strings.Join(failedEnvTasks, ",")))
	}

	return nil
}
