/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/12/11, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
在有新节点产生的流程检查mochow版本
*/

package checkversion

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/checkversion"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// 检查version的前两位版本是否一致
// (1）先检查所有存活的节点版本是否一致
// (2) 检查要使用的新包的版本是否和存活的节点使用的版本一致

func CheckMochowVersion(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	if app.Type == vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "standalone no need to check mochow version")
		return nil
	}

	err = checkversion.CheckMochowVersion(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check mochow version fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}
