/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* pause_cluster_app.go - File Description */
/*
Modification History
--------------------
2022/5/23, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
)

const (
	WorkflowReopenCluster     = "vdb-reopen-cluster-app"
	StepReopenClusterRebindRs = "vdb-reopen-cluster-app-rebind-rs"
	StepReopenClusterCallback = "vdb-reopen-cluster-app-cb"
)

func init() {
	// Step-1 重新挂载所有rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenClusterRebindRs,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     blb.ProcessRebindProxyRs,
		SuccessNextStep: StepReopenClusterCallback,
		ErrorNextStep:   StepReopenClusterRebindRs,
	})

	// Step-2 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenClusterCallback,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     callback.ProcessReOpenCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepReopenClusterCallback,
	})
}
