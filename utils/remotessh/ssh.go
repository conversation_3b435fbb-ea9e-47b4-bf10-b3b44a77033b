/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* ssh.go */
/*
modification history
--------------------
2023/04/19 , by <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com) , create
*/
/*
DESCRIPTION
todo
*/

package remotessh

import (
	"context"
	"fmt"

	"golang.org/x/crypto/ssh"
)

func RunCmdRemote(ctx context.Context, sshConf *ssh.ClientConfig, host string, port string, cmd string) (string, error) {
	conn, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", host, port), sshConf)
	if err != nil {
		return "", fmt.Errorf("connect to remote fail")
	}
	defer conn.Close()
	session, err := conn.NewSession()
	if err != nil {
		return "", fmt.Errorf("build a new ssh session fail")
	}
	defer session.Close()
	remoteResp, err := session.CombinedOutput(cmd)
	if err != nil {
		return "", fmt.<PERSON>rrorf("run remote cmd fail,err:%s", err.Error())
	}
	return string(remoteResp), nil
}

func RunCmdRemoteIgnoreErr(ctx context.Context, sshConf *ssh.ClientConfig, host string, port string, cmd string) (string, error) {
	conn, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", host, port), sshConf)
	if err != nil {
		return "", fmt.Errorf("connect to remote fail")
	}
	defer conn.Close()
	session, err := conn.NewSession()
	if err != nil {
		return "", fmt.Errorf("build a new ssh session fail")
	}
	defer session.Close()
	remoteResp, err := session.CombinedOutput(cmd)
	return string(remoteResp), err
}
