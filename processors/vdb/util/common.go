package util

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	subnet "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/subnet"
	zoneComponent "icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	apisdkIface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/preparerecover"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	backupRepo = "backup_bos"
)

func GetEngineByID(ctx context.Context, app *vdbmodel.Application, ID string) string {
	masterPrefix := app.AppID + "-m-"
	proxyPrefix := app.AppID + "-p-"
	datanodePrefix := app.AppID + "-n-"

	if strings.HasPrefix(ID, masterPrefix) {
		return vdbmodel.EngineVDBMaster
	} else if strings.HasPrefix(ID, proxyPrefix) {
		return vdbmodel.EngineVDBProxy
	} else if strings.HasPrefix(ID, datanodePrefix) {
		return vdbmodel.EngineVDBDataNode
	}

	return ""
}

func GetStep(teu *workflow.TaskExecUnit) string {
	chunks := strings.Split(teu.TaskBatchID, "|")
	if len(chunks) > 1 {
		return chunks[1]
	}
	return ""
}

func GetImageIDAndVersion(dbImageID string) (imageID string, version string) {
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		imageID = dbImageID
		version = ""
	} else {
		if strings.Contains(dbImageID, ":") {
			imageID = strings.Split(dbImageID, ":")[0]
			version = strings.Split(dbImageID, ":")[1]
		} else {
			imageID = dbImageID
			version = ""
		}
	}
	return
}

// Returns: AZONE-xxxx, zoneX, err
func GetZoneBySubnetID(ctx context.Context, userID string, subnetID string) (azone string, logicZone string, err error) {
	var zoneName string // cn-<region>-<x>
	var zoneNameSlice []string

	subnetDetail, err := subnet.SubnetResourceOp().GetSubnetDetail(ctx, &subnet.GetSubnetDetailReq{
		UserID:   userID,
		SubnetID: subnetID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get subnet detail failed", logit.Error("error", err))
		return "", "", err
	}

	zoneName = subnetDetail.Subnet.ZoneName
	zoneNameSlice = strings.Split(zoneName, "-")
	logicZone = "zone" + strings.ToUpper(zoneNameSlice[len(zoneNameSlice)-1])

	zoneMapperFunc, err := zoneComponent.ZoneOp().GetZoneMap(ctx, userID)
	azone, found := zoneMapperFunc(logicZone, true)
	if !found {
		return "", "", errors.New("zone not found")
	}
	return azone, logicZone, nil
}

func RegisterBackupPolicyIfNeeded(ctx context.Context, appID string) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		resource.LoggerTask.Notice(ctx, "private env do not need to register backup policy", logit.String("appID", appID))
		return nil
	}

	dataType := vdbmodel.ProductVDB

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type == vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "standalone app do not need to register backup policy",
			logit.String("cacheCluster:", base_utils.Format(appID)))
		return nil
	}

	backupConfig := app.BackupConfig
	if backupConfig == "" {
		errMsg := "req.AutoBackupConfig is null, please check request"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(appID)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	// backup_config 格式转化 utc
	backupConfigUtc := BackupConfigFormatUtc(backupConfig)
	resource.LoggerTask.Notice(ctx, "backupConfigUtc", logit.String("backupConfigUtc:", base_utils.Format(backupConfigUtc)))
	if backupConfigUtc == "" {
		errMsg := backupConfig + " BackupConfigFormatUtc failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(appID)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	// dbrs注册备份配置
	backupConfigUtcSplit := strings.Split(backupConfigUtc, ";")

	// dataStorages 赋值
	dataStoragesType := "cds"
	if app.BackupType == "phy" {
		dataStoragesType = "s3"
	}

	dataStoragesID := env.IDC() + "-vdb-databackup-0000"

	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		dataStoragesType = "s3"
	}

	createAppBackupParams := &dbrsComp.CreatePolicyConf{
		DataType:              dataType,
		AppID:                 appID,
		DataBackupTime:        backupConfigUtcSplit[1] + "Z",
		DayLevelRetentionTime: cast.ToInt64(backupConfigUtcSplit[2]),
		Type:                  dataStoragesType,
		BucketId:              dataStoragesID,
		Region:                env.IDC(),
		Product:               vdbmodel.ProductVDB,
	}

	if app.BackupType == "phy" {
		createAppBackupParams.PolicyType = "physical_data_backup"
	}

	// 区分没有开启备份的集群
	if backupConfigUtcSplit[0] != "" {
		createAppBackupParams.DataBackupWeekDay = GetWeekday(backupConfigUtcSplit[0])
	}
	resource.LoggerTask.Notice(ctx, "CreateBackupPolicy params",
		logit.String("createAppBackupParams:", base_utils.Format(createAppBackupParams)))

	createAppBackupRsp, err := dbrsComp.DbrsResourceOp().CreateBackupPolicy(ctx, createAppBackupParams)
	if err != nil {
		errMsg := "call dbrs api create app backup policy failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	resource.LoggerTask.Notice(ctx, "call dbrs api create app backup policy success",
		logit.String("createAppBackupRsp", base_utils.Format(createAppBackupRsp)))

	return nil
}

func DeleteBackupPolicyIfNeeded(ctx context.Context, appID string) error {
	dataType := vdbmodel.ProductVDB

	deleteAppBackupParams := &dbrsComp.CommonPolicyParams{
		AppID:    appID,
		DataType: dataType,
		Product:  vdbmodel.ProductVDB,
	}
	deleteAppBackupRsp, err := dbrsComp.DbrsResourceOp().DeleteBackupPolicy(ctx, deleteAppBackupParams)
	if err != nil {
		errMsg := "call dbrs api delete app backup policy failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	resource.LoggerTask.Notice(ctx, "call dbrs api delete app backup policy success",
		logit.String("deleteAppBackupRsp", base_utils.Format(deleteAppBackupRsp)))

	return nil
}

func GetTimeUptoNow(startTime time.Time) int64 {
	now := time.Now()
	return cast.ToInt64(now.Sub(startTime).Seconds())
}

func BackupConfigFormatUtc(localBackupConfig string) string {
	backupConfigSplit := strings.Split(localBackupConfig, ";")
	if len(backupConfigSplit) != 3 {
		return ""
	}

	timeConfigs := strings.Split(backupConfigSplit[1], ":")
	backupConfigUtc := ""
	backupConfigUtc += backupConfigSplit[0]
	backupConfigUtc += ";"
	backupConfigUtc += modifyTime(timeConfigs[0])
	backupConfigUtc += ":"
	backupConfigUtc += modifyTime(timeConfigs[1])
	backupConfigUtc += ":"
	backupConfigUtc += modifyTime(timeConfigs[2])
	backupConfigUtc += ";"
	backupConfigUtc += backupConfigSplit[2]
	return backupConfigUtc
}

func modifyTime(time string) string {
	formatTime := time
	if len(time) == 1 {
		formatTime = "0" + time
	}
	return formatTime
}

func GetWeekday(scsWeekday string) []string {
	weekdayMap := map[string]string{
		"Mon": "Monday",
		"Tue": "Tuesday",
		"Wed": "Wednesday",
		"Thu": "Thursday",
		"Fri": "Friday",
		"Sta": "Saturday",
		"Sun": "Sunday",
	}
	dataBackupWeekDay := []string{}
	for _, day := range strings.Split(scsWeekday, ",") {
		dataBackupWeekDay = append(dataBackupWeekDay, weekdayMap[day])
	}
	return dataBackupWeekDay
}

func GetAllNeedShutdownNodeIDs(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	var ret []string
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			ret = append(ret, node.NodeID)
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			ret = append(ret, proxy.ProxyID)
		}
	}

	for _, master := range app.Masters {
		ret = append(ret, master.MasterID)
	}

	return ret, nil
}

func GetAllNeedOperateCDSNodeIDs(ctx context.Context, task *workflow.Task) ([]string, error) {
	param, err := apisdkIface.GetParameters(ctx, task.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return nil, cerrs.ErrInvalidParams.Wrap(err)
	}

	var ret []string
	for _, item := range param.RevoverParam.NodeRecoverItems {
		ret = append(ret, item.RecoverNodeID)
	}

	resource.LoggerTask.Trace(ctx, "recover get all need operate cds node ids params",
		logit.String("params :", base_utils.Format(param)),
		logit.String("appID", base_utils.Format(task.Entity)),
		logit.String("ret", base_utils.Format(ret)))

	return ret, nil
}

func GetNeedRemoveDataMasterIDs(ctx context.Context, task *workflow.Task) ([]string, error) {
	param, err := apisdkIface.GetParameters(ctx, task.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return nil, cerrs.ErrInvalidParams.Wrap(err)
	}

	resource.LoggerTask.Trace(ctx, "recover get all need operate cds node ids params",
		logit.String("params :", base_utils.Format(param)),
		logit.String("appID", base_utils.Format(task.Entity)))

	app, err := vdbmodel.ApplicationGetByAppID(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}

	var masterIDHasSnapshot string
	masterCount := 0
	for _, item := range param.RevoverParam.NodeRecoverItems {
		engine := GetEngineByID(ctx, app, item.RecoverNodeID)
		if engine == "" {
			resource.LoggerTask.Error(ctx, "engine get by id failed", logit.String("nodeID", item.RecoverNodeID))
			return nil, cerrs.ErrInvalidParams
		}

		if engine == vdbmodel.EngineVDBMaster {
			masterIDHasSnapshot = item.RecoverNodeID
			masterCount++
		}
	}

	if masterCount != 1 {
		resource.LoggerTask.Error(ctx, "only one master should have snapshot", logit.Int("count", masterCount))
		return nil, cerrs.ErrInvalidParams
	}

	var ret []string
	for _, master := range app.Masters {
		if master.MasterID == masterIDHasSnapshot {
			continue
		}

		ret = append(ret, master.MasterID)
	}

	resource.LoggerTask.Trace(ctx, "get need remove data master ids", logit.String("masterIDHasSnapshot", masterIDHasSnapshot),
		logit.String("ret", base_utils.Format(ret)))

	return ret, nil
}

func CreateCloneRecoverTaskIfNeeded(ctx context.Context, app *vdbmodel.Application, needSwitchEntrance bool) error {
	appID := app.AppID
	srcAppID := app.CloneDataAppID
	backupStatus := app.BackupStatus
	appBackupID := app.CloneDataAppBackupID
	if srcAppID == "" || appBackupID == "" {
		return nil
	}

	resource.LoggerTask.Notice(ctx, "check cluster is clone recover or not",
		logit.String("backupStatus:", base_utils.Format(backupStatus)),
		logit.String("srcAppID:", base_utils.Format(srcAppID)),
		logit.String("appBackupID:", base_utils.Format(appBackupID)),
		logit.String("destAppID:", base_utils.Format(appID)))

	if app.BackupStatus != vdbmodel.BackupWaitRecover {
		resource.LoggerTask.Notice(ctx, "backupStatus is not BackupWaitRecover",
			logit.String("backupStatus", base_utils.Format(app.BackupStatus)))
		return nil
	}

	//copy备份记录
	backupModels, err := vdbmodel.AppBackupGetAllByCond(ctx, "app_id = ?  and app_backup_id = ?",
		srcAppID, appBackupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get backupModels error", logit.Error("error", err))
		return err
	}

	if len(backupModels) != 1 {
		resource.LoggerTask.Notice(ctx, "backup no found or more than one", logit.String("appBackupID:",
			base_utils.Format(appBackupID)), logit.Int("len(backupModels)", len(backupModels)))
		return nil
	}

	cloneApp, err := vdbmodel.ApplicationGetByAppID(ctx, srcAppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get instance failed", logit.String("appid", srcAppID), logit.Error("error", err))
		return err
	}

	cloneApp.Status = vdbmodel.AppStatusRunning
	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{cloneApp}); err != nil {
		resource.LoggerTask.Error(ctx, "save cloneApp model failed", logit.Error("error", err))
		return err
	}

	srcBackup := backupModels[0]

	tgtBackup := &vdbmodel.AppBackup{
		AppID:       appID,
		AppBackupID: srcBackup.AppBackupID,
		Status:      vdbmodel.BackupSuccess,
		BackupType:  srcBackup.BackupType,
		Comment:     "clone recover backup from " + srcAppID,
		Expairation: srcBackup.Expairation,
		StartTime:   time.Now(),
	}

	/*
		for _, item := range srcBackup.AppBackupItems {
			tgtBackup.AppBackupItems = append(tgtBackup.AppBackupItems, &vdbmodel.AppBackupItem{
				AppBackupID: item.AppBackupID,
				BackupID:    item.BackupID,
				NodeID:      item.NodeID,
				StartTime:   item.StartTime,
				Duration:    item.Duration,
				Status:      item.Status,
				Bucket:      item.Bucket,
				ObjectKey:   item.ObjectKey,
				Access:      item.Access,
				ObjectSize:  item.ObjectSize,
			})
		}
	*/

	if err := vdbmodel.SaveBackup(ctx, []*vdbmodel.AppBackup{tgtBackup}); err != nil {
		resource.LoggerTask.Error(ctx, "save backup error", logit.Error("error", err))
		return err
	}

	//发起恢复任务
	app.Status = vdbmodel.AppStatusRecovering
	app.BackupStatus = vdbmodel.BackupSuccess
	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Error(ctx, "save app fail", logit.Error("error", err))
		return err
	}

	if srcBackup.BackupStorageType == "phy" {
		return CreateDbrsRecoverTask(ctx, app, tgtBackup, srcAppID, needSwitchEntrance)
	}

	return CreateCdsRecoverTask(ctx, app, srcBackup, needSwitchEntrance)
}

func CreateCdsRecoverTask(ctx context.Context, app *vdbmodel.Application, backupModel *vdbmodel.AppBackup,
	needSwitchEntrance bool) error {
	nodeRecoverItems, err := preparerecover.RecoverPrepareParams(ctx, app, backupModel)
	if err != nil {
		errMsg := "prepare recover params failed."
		resource.LoggerTask.Error(ctx, errMsg, logit.String("backupModel:", base_utils.Format(backupModel)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	taskParams := &apisdkIface.TaskParameters{
		AppID: app.AppID,
		RevoverParam: &apisdkIface.RecoverParam{
			SrcAppID:         app.CloneDataAppID,
			AppBackupID:      backupModel.AppBackupID,
			NodeRecoverItems: nodeRecoverItems,
			SwitchEntrance:   needSwitchEntrance,
		},
	}

	workFlow := "vdb-recover-in-original-cluster-cds-app"
	tk := &iface.Task{
		TaskID:     uuid.NewString(),
		WorkFlow:   workFlow,
		Entity:     app.AppID,
		EntityDim:  "app",
		Status:     iface.TaskStatusWaiting,
		CreatedAt:  time.Now(),
		Schedule:   time.Now().Add(1 * time.Second),
		Deadline:   time.Now().Add(12 * time.Hour),
		Mutex:      "n_" + app.AppID,
		Parameters: base_utils.Format(taskParams),
	}
	if err := resource.TaskOperator.CreateTask(ctx, tk); err != nil {
		errorMessage := "create cds recover task failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("backupModel:", base_utils.Format(backupModel)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func CreateDbrsRecoverTask(ctx context.Context, app *vdbmodel.Application, backupModel *vdbmodel.AppBackup,
	srcAppID string, needSwitchEntrance bool) error {
	taskParams := &apisdkIface.TaskParameters{
		AppID: app.AppID,
		RevoverParam: &apisdkIface.RecoverParam{
			AppBackupID:    backupModel.AppBackupID,
			SrcAppID:       srcAppID,
			SwitchEntrance: needSwitchEntrance,
		},
	}

	workFlow := "vdb-recover-in-new-cluster-dbrs-app"
	tk := &iface.Task{
		TaskID:     uuid.NewString(),
		WorkFlow:   workFlow,
		Entity:     app.AppID,
		EntityDim:  "app",
		Status:     iface.TaskStatusWaiting,
		CreatedAt:  time.Now(),
		Schedule:   time.Now().Add(1 * time.Second),
		Deadline:   time.Now().Add(12 * time.Hour),
		Mutex:      "n_" + app.AppID,
		Parameters: base_utils.Format(taskParams),
	}
	if err := resource.TaskOperator.CreateTask(ctx, tk); err != nil {
		errorMessage := "create dbrs recover task failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("backupModel:", base_utils.Format(backupModel)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func CreateSwitchEntranceTask(ctx context.Context, firstAppID, secondAppID string) error {
	taskParams := &apisdkIface.TaskParameters{
		AppID: firstAppID,
		SwitchEntranceParam: &apisdkIface.SwitchEntranceParam{
			FirstAppID:  firstAppID,
			SecondAppID: secondAppID,
		},
	}

	workflow := "vdb-switch-cluster-entrance"

	err := resource.TaskOperator.CreateTask(ctx, &iface.Task{
		TaskID:     uuid.New().String(),
		WorkFlow:   workflow,
		Entity:     firstAppID,
		EntityDim:  "app",
		Status:     iface.TaskStatusWaiting,
		Deadline:   time.Now().Add(time.Hour * 24),
		Mutex:      "n_" + firstAppID,
		Parameters: base_utils.Format(taskParams),
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create task failed", logit.Error("err", err))
		return fmt.Errorf("create task failed: %s", err.Error())
	}

	return nil
}
