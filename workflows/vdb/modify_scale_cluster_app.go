/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* modify_scale_cluster_app.go */
/*
modification history
--------------------
2024/5/30, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
集群版扩缩容
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/applyresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	checksubnets "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/check_subnets"
	checkversion "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/check_version"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/delresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/deploy"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/pushflag"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/opmonitor"
	securitygroup "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/security_group"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowModifyScaleUpCluster                       = "vdb-modify-scale-up-cluster-app"
	StepModifyScaleUpClusterBuildMeta                  = "vdb-modify-scale-up-cluster-app-build-meta"
	StepModifyScaleUpClusterCheckVersion               = "vdb-modify-scale-up-cluster-check-version"
	StepModifyScaleUpClusterCheckSubnetsEnoughIps      = "vdb-modify-scale-up-cluster-check-subnets-enough-ips"
	StepModifyScaleUpClusterApplyResources             = "vdb-modify-scale-up-cluster-apply-resources"
	StepModifyScaleUpClusterUpdateSecurityGroups       = "vdb-modify-scale-up-cluster-update-security-groups"
	StepModifyScaleUpClusterDeploy                     = "vdb-modify-scale-up-cluster-deploy"
	StepModifyScaleUpClusterAddNodes                   = "vdb-modify-scale-up-cluster-add-nodes"
	StepModifyScaleUpClusterUpdateAppTopologyInXmaster = "vdb-modify-scale-up-cluster-update-app-topo-in-xmaster"
	StepModifyScaleUpClusterSetRs                      = "vdb-modify-scale-up-cluster-set-rs"
	StepModifyScaleUpInitOpMonitor                     = "vdb-modify-scale-up-init-op-monitor"
	StepModifyScaleUpClusterPushFlag                   = "vdb-modify-scale-up-cluster-push-flag"
	StepModifyScaleUpClusterSuccessCallback            = "vdb-modify-scale-up-cluster-succ-cb"

	StepModifyScaleUpClusterRollbackReleaseResources = "vdb-modify-scale-up-cluster-rollback-release-resource"
	StepModifyScaleUpClusterRollbackMeta             = "vdb-modify-scale-up-cluster-rollback-meta"
	StepModifyScaleUpClusterRollbackCallback         = "vdb-modify-scale-up-cluster-rollback-cb"
)

const (
	WorkflowModifyScaleDownCluster                       = "vdb-modify-scale-down-cluster-app"
	StepModifyScaleDownClusterBuildMeta                  = "vdb-modify-scale-down-cluster-app-build-meta"
	StepModifyScaleDownClusterCheckDataNodeStatus        = "vdb-modify-scale-down-cluster-check-datanode-status"
	StepModifyScaleDownClusterDropNodes                  = "vdb-modify-scale-down-cluster-drop-nodes"
	StepModifyScaleDownClusterSetRs                      = "vdb-modify-scale-down-cluster-set-rs"
	StepModifyScaleDownClusterPushFlag                   = "vdb-modify-scale-down-cluster-push-flag"
	StepModifyScaleDownClusterDelNodes                   = "vdb-modify-scale-down-cluster-del-nodes"
	StepModifyScaleDownClusterUpdateAppTopologyInXmaster = "vdb-modify-scale-down-cluster-update-app-topo-in-xmaster"
	StepModifyScaleDownClusterSuccessCallback            = "vdb-modify-scale-down-cluster-succ-cb"

	StepModifyScaleDownClusterRollbackCallback = "vdb-modify-scale-down-cluster-rollback-cb"
)

func init() {
	/**************************************集群版增加节点*******************************************/
	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterBuildMeta,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     buildmeta.ProcessModifyScale,
		SuccessNextStep: StepModifyScaleUpClusterCheckVersion,
		ErrorNextStep:   StepModifyScaleUpClusterBuildMeta,
	})

	// Step-2 检查集群版本是否支持扩容
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterCheckVersion,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     checkversion.CheckMochowVersion,
		SuccessNextStep: StepModifyScaleUpClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyScaleUpClusterCheckVersion,
	})

	// Step-3 检查子网IP是否足够
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterCheckSubnetsEnoughIps,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepModifyScaleUpClusterApplyResources,
		ErrorNextStep:   StepModifyScaleUpClusterCheckSubnetsEnoughIps,
	})

	// Step-4 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterApplyResources,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepModifyScaleUpClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyScaleUpClusterApplyResources,
	})

	// Step-5 更新安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterUpdateSecurityGroups,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: StepModifyScaleUpClusterDeploy,
		ErrorNextStep:   StepModifyScaleUpClusterUpdateSecurityGroups,
	})

	// Step-6 部署
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterDeploy,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepModifyScaleUpClusterAddNodes,
		ErrorNextStep:   StepModifyScaleUpClusterDeploy},

		workflow.WithStepTimeout(time.Minute*15))

	// Step-7 master注册节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterAddNodes,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     mochow.ProcessAddNode,
		SuccessNextStep: StepModifyScaleUpClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyScaleUpClusterAddNodes,
	})

	// Step-8 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyScaleUpClusterSetRs,
		ErrorNextStep:   StepModifyScaleUpClusterUpdateAppTopologyInXmaster,
	})

	// Step-9 绑定新的proxy rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterSetRs,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepModifyScaleUpInitOpMonitor,
		ErrorNextStep:   StepModifyScaleUpClusterSetRs,
	})

	// Step-10 更新监控
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpInitOpMonitor,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyScaleUpClusterPushFlag,
		ErrorNextStep:   StepModifyScaleUpInitOpMonitor,
	})

	// Step-11 推送监控配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterPushFlag,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyScaleUpClusterSuccessCallback,
		ErrorNextStep:   StepModifyScaleUpClusterPushFlag,
	})

	// Step-12 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterSuccessCallback,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     callback.ProcessModifyScaleSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyScaleUpClusterSuccessCallback,
	})

	// Step-Error-01 回滚资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterRollbackReleaseResources,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifyScaleUpClusterRollbackMeta,
		ErrorNextStep:   StepModifyScaleUpClusterRollbackReleaseResources,
	})

	// Step-Error-02 回滚Meta
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterRollbackMeta,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifyScaleUpClusterRollbackCallback,
		ErrorNextStep:   StepModifyScaleUpClusterRollbackMeta,
	})

	// Step-Error-03 回滚Callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleUpClusterRollbackCallback,
		Workflow:        WorkflowModifyScaleUpCluster,
		StepProcess:     callback.ProcessModifyScaleErrorCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyScaleUpClusterRollbackCallback,
	})

	/**************************************集群版减少节点*******************************************/
	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterBuildMeta,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     buildmeta.ProcessModifyScale,
		SuccessNextStep: StepModifyScaleDownClusterCheckDataNodeStatus,
		ErrorNextStep:   StepModifyScaleDownClusterBuildMeta,
	})

	// Step-2 检查数据节点状态是否允许缩容
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterCheckDataNodeStatus,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     mochow.ProcessCheckDataNodeCanBeShrink,
		SuccessNextStep: StepModifyScaleDownClusterDropNodes,
		ErrorNextStep:   StepModifyScaleDownClusterCheckDataNodeStatus},
		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 master drop node
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterDropNodes,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     mochow.ProcessDropNode,
		SuccessNextStep: StepModifyScaleDownClusterSetRs,
		ErrorNextStep:   StepModifyScaleDownClusterDropNodes,
	})

	// Step-4 更新proxy rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterSetRs,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepModifyScaleDownClusterPushFlag,
		ErrorNextStep:   StepModifyScaleDownClusterSetRs,
	})

	// Step-5 推送监控配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterPushFlag,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyScaleDownClusterDelNodes,
		ErrorNextStep:   StepModifyScaleDownClusterPushFlag,
	})

	// Step-6 删除资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterDelNodes,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyScaleDownClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyScaleDownClusterDelNodes,
	})

	// Step-7 xmaster拓扑更新
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmasterForScale,
		SuccessNextStep: StepModifyScaleDownClusterSuccessCallback,
		ErrorNextStep:   StepModifyScaleDownClusterUpdateAppTopologyInXmaster,
	})

	// Step-8 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterSuccessCallback,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     callback.ProcessModifyScaleSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyScaleDownClusterSuccessCallback,
	})

	// Step-Error-01 失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyScaleDownClusterRollbackCallback,
		Workflow:        WorkflowModifyScaleDownCluster,
		StepProcess:     callback.ProcessModifyScaleErrorCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyScaleDownClusterRollbackCallback,
	})
}
