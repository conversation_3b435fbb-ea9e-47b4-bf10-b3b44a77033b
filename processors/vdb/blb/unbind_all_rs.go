/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2022/03/31
 * File: unbind_all_rs.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package blb TODO package function desc
package blb

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/errors"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessUnbindAllProxys process proxy rs
func ProcessUnbindAllProxys(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	err = clusterUnbindAllProxys(ctx, app, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "unbind rs fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func clusterUnbindAllProxys(ctx context.Context, BlbApp *vdbmodel.Application, RsApp *vdbmodel.Application) error {
	var userID string
	blbIds := make([]string, 0)
	appBlbIds := make([]*vdbmodel.BLB, 0)
	for _, blb := range BlbApp.BLBs {
		if blb.RsRange == vdbmodel.BLBRsRangeAZone {
			continue
		}
		if len(blb.ResourceUserID) == 0 {
			userID = BlbApp.UserID
		} else {
			userID = blb.ResourceUserID
		}
		if blb.Type == vdbmodel.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbID)
		}
		if blb.Type == vdbmodel.BLBTypeApp {
			appBlbIds = append(appBlbIds, blb)
		}
	}

	toUnbindRsIDList := []string{}
	toUnbindNodeList := []*vdbmodel.Proxy{}
	for _, itf := range RsApp.Interfaces {
		for _, proxy := range itf.Proxies {
			var uuid string
			if RsApp.ResourceType == "container" {
				uuid = proxy.ContainerID
			} else {
				uuid = proxy.ResourceID
			}

			toUnbindRsIDList = append(toUnbindRsIDList, uuid)
			toUnbindNodeList = append(toUnbindNodeList, proxy)
		}
	}
	if len(blbIds) > 0 {
		if len(toUnbindRsIDList) > 0 {
			unbindReq := &blb.UnbindRsParam{
				UserID: userID,
				BLBIDs: blbIds,
				UUIDs:  toUnbindRsIDList,
			}
			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind rs fail",
					logit.String("BlbAppID", BlbApp.AppID),
					logit.String("RsAppID", RsApp.AppID),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
				return errors.UnBindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIds) > 0 {
		// 处理ip组
		IPList := make([]string, 0)
		for _, n := range toUnbindNodeList {
			rsIPPort := fmt.Sprintf("%s:%d", n.IP, n.Port)
			IPList = append(IPList, rsIPPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range appBlbIds {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userID,
				BLBID:      blb.BlbID,
				IPGroup:    blb.IPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
			totalCount++
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("BlbAppID", BlbApp.AppID),
				logit.String("RsAppID", RsApp.AppID),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}

	err := UnbindRangeBLB(ctx, BlbApp, RsApp)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "unbind range blb fail",
			logit.String("BlbAppID", BlbApp.AppID),
			logit.String("RsAppID", RsApp.AppID),
			logit.Error("err", err))
		return err
	}

	return nil
}

func UnbindRangeBLB(ctx context.Context, BlbApp *vdbmodel.Application, RsApp *vdbmodel.Application) error {
	var userID string

	for _, b := range BlbApp.BLBs {
		if b.RsRange != vdbmodel.BLBRsRangeAZone {
			continue
		}

		if len(b.ResourceUserID) == 0 {
			userID = BlbApp.UserID
		} else {
			userID = b.ResourceUserID
		}

		if b.Type == vdbmodel.BLBTypeNormal {
			toUnbindRsIDList := []string{}
			for _, itf := range RsApp.Interfaces {
				for _, proxy := range itf.Proxies {
					if proxy.LogicZone != b.MasterAZ {
						continue
					}
					var uuid string
					if RsApp.ResourceType == "container" {
						uuid = proxy.ContainerID
					} else {
						uuid = proxy.ResourceID
					}

					toUnbindRsIDList = append(toUnbindRsIDList, uuid)
				}
			}

			blbIds := []string{b.BlbID}

			if len(toUnbindRsIDList) > 0 {
				unbindReq := &blb.UnbindRsParam{
					UserID: userID,
					BLBIDs: blbIds,
					UUIDs:  toUnbindRsIDList,
				}
				if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
					resource.LoggerTask.Warning(ctx, "unbind rs fail",
						logit.String("BlbAppID", BlbApp.AppID),
						logit.String("RsAppID", RsApp.AppID),
						logit.String("blbIds", base_utils.Format(blbIds)),
						logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
					return errors.UnBindRsFail.Wrap(err)
				}
			}
		} else if b.Type == vdbmodel.BLBTypeApp {
			IPList := make([]string, 0)
			for _, itf := range RsApp.Interfaces {
				for _, proxy := range itf.Proxies {
					if proxy.LogicZone != b.MasterAZ {
						continue
					}

					rsIPPort := fmt.Sprintf("%s:%d", proxy.IP, proxy.Port)
					IPList = append(IPList, rsIPPort)
				}
			}

			if len(IPList) > 0 {
				unbindReq := &blbv2.UnbindRsParams{
					UserID:     userID,
					BLBID:      b.BlbID,
					IPGroup:    b.IPGroupID,
					MemberList: IPList,
				}

				err := blbv2.Instance().UnbindRs(ctx, unbindReq)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "unbind rs fail",
						logit.String("BlbAppID", BlbApp.AppID),
						logit.String("RsAppID", RsApp.AppID),
						logit.Error("doblbv2UnBindRs", err))
					return err
				}
			}
		}
	}

	return nil
}

// ProcessUnbindAllRsStandaloneRs 摘除所有Rs
func ProcessUnbindAllStandalones(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有节点的blb绑定状态
	if err := unbindAllRs(ctx, app); err != nil {
		return err
	}

	return nil
}

func unbindAllRs(ctx context.Context, app *vdbmodel.Application) error {
	// 获得normal和readonly类型的blb列表
	blbList := make([]*vdbmodel.BLB, 0)
	appblbList := make([]*vdbmodel.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.BlbID) == 0 {
			continue
		}
		if b.Type == vdbmodel.BLBTypeNormal {
			blbList = append(blbList, b)
		}
		if b.Type == vdbmodel.BLBTypeApp {
			appblbList = append(appblbList, b)
		}
	}

	// 获取节点
	nodes := make([]*vdbmodel.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			nodes = append(nodes, node)
		}
	}

	// 更新normal类型的blb绑定状态
	if err := updateRsBindingByBLBList(ctx, app, blbList, nil, nodes); err != nil {
		return err
	}

	// 更新app类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appblbList, nil, nodes); err != nil {
		return err
	}

	return nil
}

func ProcessSwitchEntranceUnbindFirstAppAllProxys(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	firstAppID := param.SwitchEntranceParam.FirstAppID

	app, err := vdbmodel.ApplicationGetByAppID(ctx, firstAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	err = clusterUnbindAllProxys(ctx, app, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "unbind rs fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func ProcessSwitchEntranceUnbindSecondAppAllProxys(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	SecondAppID := param.SwitchEntranceParam.SecondAppID

	app, err := vdbmodel.ApplicationGetByAppID(ctx, SecondAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", SecondAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	err = clusterUnbindAllProxys(ctx, app, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "unbind rs fail", logit.String("appId", SecondAppID),
			logit.Error("dbError", err))
		return err
	}

	return nil
}
