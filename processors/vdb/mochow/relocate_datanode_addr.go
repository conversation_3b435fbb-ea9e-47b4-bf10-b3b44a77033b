/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	apisdkiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func RelocateDatanodeAddr(ctx context.Context, master *vdbmodel.Master, RevoverParam *apisdkiface.RecoverParam) error {
	listReq := mochow.ListDataNodeRequest{
		Token: "default_token",
	}

	listRsp, err := mochow.ListDataNode(ctx, master, &listReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list datanode failed before relocate", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "list datanode before relocate", logit.String("rsp :", base_utils.Format(listRsp)))

	if listRsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list datanode failed before relocate", logit.Int32("status_code", listRsp.Status.Code))
		return fmt.Errorf(listRsp.Status.Msg)
	}

	if len(listRsp.NodeList) == 0 {
		resource.LoggerTask.Error(ctx, "datanode list is empty before relocate", logit.String("rsp :", base_utils.Format(listRsp)))
		return fmt.Errorf("datanode list is empty")
	}

	for _, dn := range listRsp.NodeList {
		resource.LoggerTask.Trace(ctx, "check datanode before relocate", logit.String("datanode :", base_utils.Format(dn)))
		for _, item := range RevoverParam.NodeRecoverItems {
			backupIP := item.BackupNodeIP
			backupPort := item.BackupNodePort
			recoverIP := item.RecoverNodeIP
			recoverPort := item.RecoverPort
			if backupIP == recoverIP && backupPort == recoverPort {
				resource.LoggerTask.Trace(ctx, "skip datanode before relocate", logit.String("datanode :", base_utils.Format(dn)))
				continue
			}

			if int64(dn.NodeAddr.IP) == util.LittleEndianInetAtoN(backupIP) && dn.NodeAddr.Port == int32(backupPort) {
				req := mochow.RelocateDatanodeAddrRequest{
					Token: "default_token",
					OriginalAddr: mochow.NodeID{
						IP:   dn.NodeAddr.IP,
						Port: dn.NodeAddr.Port,
					},
					UpdatedAddr: mochow.NodeID{
						IP:   int32(util.LittleEndianInetAtoN(recoverIP)),
						Port: int32(recoverPort),
					},
				}

				rsp, err := mochow.RelocateDatanodeAddr(ctx, master, &req)
				if err != nil {
					resource.LoggerTask.Error(ctx, "relocate datanode addr failed", logit.String("IP", base_utils.Format(master.IP)),
						logit.Error("error", err))
					return err
				}

				if rsp.Status.Code != 0 {
					resource.LoggerTask.Error(ctx, "relocate datanode addr failed", logit.String("IP", base_utils.Format(master.IP)),
						logit.Int32("status_code", rsp.Status.Code))
					return fmt.Errorf(rsp.Status.Msg)
				}

				resource.LoggerTask.Trace(ctx, "relocate datanode addr success", logit.String("OriginalAddr", base_utils.Format(backupIP)),
					logit.String("UpdatedAddr", base_utils.Format(recoverIP)))
			}
		}
	}

	return nil
}
