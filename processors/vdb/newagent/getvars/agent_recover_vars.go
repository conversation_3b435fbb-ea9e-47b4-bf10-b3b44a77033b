package getvars

import (
	"context"
	"strconv"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func GetDataNodeRecoverConf(ctx context.Context, param *Parameter) (*DataNodeConf, error) {
	r := &DataNodeConf{
		InstanceType: "datanode",
		AppID:        param.App.AppID,
		ClusterID:    param.Cluster.ClusterID,
		NodeShowID:   param.Node.NodeFixID,
		Port:         param.Node.Port,
	}
	return r, nil
}

func GetProxyRecoverConf(ctx context.Context, param *Parameter) (*ProxyConf, error) {
	r := &ProxyConf{
		InstanceType: "proxy",
		AppID:        param.App.AppID,
		NodeShowID:   param.Proxy.NodeFixID,
		ProxyID:      param.Proxy.ProxyID,
		Port:         param.Proxy.Port,
	}
	return r, nil
}

func GetMasterRecoverConf(ctx context.Context, param *Parameter) (*MasterConf, error) {
	r := &MasterConf{
		InstanceType: "master",
		AppID:        param.App.AppID,
		NodeShowID:   param.Master.NodeFixID,
		MasterID:     param.Master.MasterID,
		Port:         param.Master.Port,
	}
	return r, nil
}

func GetInstanceMetadata(ctx context.Context, param *Parameter) (*InstanceMetadata, error) {
	r := &InstanceMetadata{
		Port: func() int {
			if param.Node != nil {
				return param.Node.Port
			} else if param.Proxy != nil {
				return param.Proxy.Port
			} else if param.Master != nil {
				return param.Master.Port
			}
			return 0
		}(),
		Type: func() string {
			if param.EngineType == vdbmodel.EngineVDBDataNode {
				return "datanode"
			} else if param.EngineType == vdbmodel.EngineVDBProxy {
				return "proxy"
			} else if param.EngineType == vdbmodel.EngineVDBMaster {
				return "master"
			}
			return ""
		}(),
		ProcName: func() string {
			switch param.EngineType {
			case vdbmodel.EngineVDBDataNode:
				if param.App.Type == vdbmodel.AppTypeStandalone {
					return "mochow-standalone"
				} else {
					return "mochow-datanode"
				}
			case vdbmodel.EngineVDBProxy:
				return "mochow-proxy"
			case vdbmodel.EngineVDBMaster:
				return "mochow-master"
			default:
				return ""
			}
		}(),
		ResourceType: param.App.ResourceType,
		IsPrivate:    privatecloud.IsPrivateENV(),
		PrivateType:  privatecloud.GetPrivateEnvType(),
	}
	return r, nil
}

func GetBcmConf(ctx context.Context, param *Parameter) (*BcmConf, error) {
	bcmPushCycleStr := "60"
	bcmPushCycle, err := strconv.Atoi(bcmPushCycleStr)
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get bcm_push_cycle flag error", logit.Error("error", err))
		bcmPushCycle = 60
	}
	if in, _ := base_utils.InArray(bcmPushCycle, []int{5, 10, 30, 60}); !in {
		bcmPushCycle = 60
	}

	r := &BcmConf{
		Cycle:         bcmPushCycle,
		RefactoringV1: 0,
	}

	return r, nil
}

func GetMonitorConf(ctx context.Context, param *Parameter) (*Monitor, error) {
	opMonitor := opmonitor.Instance()
	var noahBns string
	var err error
	var fixIP string
	if param.EngineType == vdbmodel.EngineVDBProxy {
		fixIP = param.Proxy.IP
		noahBns = opMonitor.GenerateInterfaceBnsInstanceName(int64(param.App.ID), param.Proxy.ID)
	} else if param.EngineType == vdbmodel.EngineVDBMaster {
		fixIP = param.Master.IP
		noahBns, err = opMonitor.GenerateBackendBnsInstanceName(int64(param.App.ID), param.Master.ID,
			vdbmodel.EngineVDBMaster)
		if err != nil {
			return nil, err
		}
	} else {
		fixIP = param.Node.IP
		noahBns, err = opMonitor.GenerateBackendBnsInstanceName(int64(param.App.ID),
			param.Node.ID, vdbmodel.EngineVDBDataNode)

		if err != nil {
			return nil, err
		}
	}
	r := &Monitor{
		HashName:        "",
		HashID:          "",
		Version:         0,
		StoreType:       0,
		NoahBns:         noahBns,
		NoahEndpoint:    opMonitor.GetEndPoint(),
		ProxyInstanceID: 0,
		FixIP:           fixIP,
	}

	return r, nil
}

func GetBlbInfo(ctx context.Context, param *Parameter) (*Blb, error) {
	blb, err := vdbmodel.BLBGetByAppID(ctx, param.App.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get blb error", logit.Error("error", err))
		return nil, err
	}
	r := &Blb{
		ElbIP:   blb.Ovip,
		ElbPort: param.App.Port,
	}

	return r, nil
}
func GetAgentRecover(ctx context.Context, param *Parameter) (*AgentRecovers, error) {
	r := &AgentRecovers{}
	var err error
	var UUID string
	var nodeID string
	if param.EngineType == vdbmodel.EngineVDBDataNode {
		dataNodeConf, err := GetDataNodeRecoverConf(ctx, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, "GetDataNodeRecoverConf error", logit.Error("error", err))
			return nil, err
		}
		r.DataNodeConf = dataNodeConf
		UUID = param.Node.ResourceID
		nodeID = param.Node.NodeID
	}
	if param.EngineType == vdbmodel.EngineVDBProxy {
		proxyConf, err := GetProxyRecoverConf(ctx, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, "GetProxyRecoverConf error", logit.Error("error", err))
			return nil, err
		}
		r.ProxyConf = proxyConf
		UUID = param.Proxy.ResourceID
		nodeID = param.Proxy.ProxyID
	}

	if param.EngineType == vdbmodel.EngineVDBMaster {
		masterConf, err := GetMasterRecoverConf(ctx, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, "GetMasterRecoverConf error", logit.Error("error", err))
			return nil, err
		}
		r.MasterConf = masterConf
		UUID = param.Master.ResourceID
		nodeID = param.Master.MasterID
	}

	r.ID = &ID{
		UUID:   UUID,
		UserID: param.App.UserID,
		AppID:  param.App.AppID,
		NodeID: nodeID,
	}

	r.InstanceMetadata, err = GetInstanceMetadata(ctx, param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "GetInstanceMetadata error", logit.Error("error", err))
		return nil, err
	}
	r.BcmConf, err = GetBcmConf(ctx, param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "GetBcmConf error", logit.Error("error", err))
		return nil, err
	}

	r.Blb, err = GetBlbInfo(ctx, param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "GetBlbInfo error", logit.Error("error", err))
		return nil, err
	}

	r.Monitor, err = GetMonitorConf(ctx, param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "GetMonitorConf error", logit.Error("error", err))
		return nil, err
	}
	resource.LoggerTask.Trace(ctx, "AgentRecover result", logit.String("AgentRecover :", base_utils.Format(r)))
	return r, nil
}
