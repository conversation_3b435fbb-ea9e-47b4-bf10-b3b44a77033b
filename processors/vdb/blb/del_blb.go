/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/errors"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessDelBLB 删除BLB
// 1. 从interface表中找到所有BLB
// 2. 发送请求进行删除
// 相关代码 ElbComponents::delete_elb
// 不涉及解绑eip等，这里默认已经操作过
func ProcessDelBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 1. 删除BLB并Save
	if err := deleteBLB(ctx, app); err != nil {
		return err
	}

	return nil
}

// deleteBLB 删除BLB并Save
func deleteBLB(ctx context.Context, app *vdbmodel.Application) error {
	// 获取待删除的BLB列表
	var deleteBLBIDs []string
	var deleteBlbList []*vdbmodel.BLB
	var deleteAppBlbIDs []string
	var deleteAppBlbList []*vdbmodel.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.BlbID) == 0 || b.Status == vdbmodel.BLBStatusDeleted {
			continue
		}
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		if b.Type == vdbmodel.BLBTypeNormal {
			deleteBLBIDs = append(deleteBLBIDs, b.BlbID)
			deleteBlbList = append(deleteBlbList, b)
		}

		if b.Type == vdbmodel.BLBTypeApp {
			deleteAppBlbIDs = append(deleteAppBlbIDs, b.BlbID)
			deleteAppBlbList = append(deleteAppBlbList, b)
		}
	}

	if len(deleteBLBIDs) == 0 && len(deleteAppBlbIDs) == 0 {
		return nil
	}

	if len(deleteBLBIDs) > 0 {
		// 删除BLB
		deleteBlbParams := &blb.DeleteBLBParam{
			UserID: userID,
			BLBIDs: deleteBLBIDs,
		}
		err := blb.Instance().DeleteBLB(ctx, deleteBlbParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "delete blb fail",
				logit.String("appId", app.AppID),
				logit.String("blbIds", base_utils.Format(deleteBLBIDs)))
			// return errors.DeleteBlbFail.Wrap(err)
		}

		// 更新数据库
		for _, b := range deleteBlbList {
			b.Status = vdbmodel.BLBStatusDeleted
		}
		if err := vdbmodel.BLBsSave(ctx, deleteBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	if len(deleteAppBlbIDs) > 0 {
		// 删除app BLB
		for _, b := range deleteAppBlbIDs {
			deleteBlbParams := &blbv2.CommonBLBParams{
				UserID: userID,
				ElbID:  b,
			}
			err := blbv2.Instance().DeleteAppBLB(ctx, deleteBlbParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "delete blb fail",
					logit.String("appId", app.AppID),
					logit.String("blbId", base_utils.Format(b)))
				return errors.DeleteBlbFail.Wrap(err)
			}
		}

		// 更新数据库
		for _, b := range deleteAppBlbList {
			b.Status = vdbmodel.BLBStatusDeleted
		}
		if err := vdbmodel.BLBsSave(ctx, deleteAppBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}
