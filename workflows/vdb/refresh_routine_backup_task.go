package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/backup"
)

const (
	WorkflowRefreshRoutineBackupTask    = "vdb-refresh-routine-backup-task"
	StepCheckAndUpdateBackupTask        = "vdb-check-and-update-backup-task"
	StepCheckAndClearExpireBackupRecord = "vdb-check-and-clear-expire-backup-record" // 清理过期的备份任务
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCheckAndUpdateBackupTask,
		Workflow:        WorkflowRefreshRoutineBackupTask,
		StepProcess:     backup.ProcessRefreshBackupTask,
		SuccessNextStep: StepCheckAndClearExpireBackupRecord,
		ErrorNextStep:   StepCheckAndUpdateBackupTask},
		// 备份任务最大执行时间 1h
		workflow.WithStepTimeout(1*60*time.Minute))

	/*
		检查&清理过期的备份任务
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCheckAndClearExpireBackupRecord,
		Workflow:        WorkflowRefreshRoutineBackupTask,
		StepProcess:     backup.ProcessCheckAndClearExpireBackupRecord,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCheckAndClearExpireBackupRecord,
	})
}
