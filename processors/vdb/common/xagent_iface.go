package common

import "icode.baidu.com/baidu/scs/x1-base/component/xagent"

type UpdateNewAddrXagentRequest struct {
	NewAddress string       `json:"new_address"`
	Meta       *xagent.Meta `json:"meta"`
}

type ConfigItem struct {
	Name  string `json:"conf_name"`
	Value string `json:"conf_value"`
}

type UpdateEnvVarsXagentRequest struct {
	Op         string        `json:"op"`
	Meta       *xagent.Meta  `json:"meta"`
	ConfigList []*ConfigItem `json:"conf_list"`
}
