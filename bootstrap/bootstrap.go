// Copyright(C) 2021 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2021-11-30, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package bootstrap

import (
	"context"
	"io"
	"log"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/bootstrap"
	baseResource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	tresrouce "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
)

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile | log.Lmicroseconds)

	compo_utils.RegisterIdcSpecConfigHookForServicer()
}

// MustInit 组件初始化，若失败，会panic
func MustInit(ctx context.Context) {
	initVdbConf(ctx)
	initRal(ctx)
	loadServicer(ctx)
	MustInitX1Task(ctx)
}

// MustInitX1Task init x1 task
func MustInitX1Task(ctx context.Context) {
	x1GormLogger := initLoggers(ctx)
	initMySQL(ctx, x1GormLogger)
	initRedis(ctx)

	specification.MustLoadConf(ctx)
	if err := bootstrap.MustInit(ctx); err != nil {
		panic(err.Error())
	}
	resource.TaskOperator = baseResource.TaskOperator

	bccresource.MustInit(ctx, &x1model.Conf{
		GormLogger:   x1GormLogger,
		ServicerName: "x1-model",
	}, tresrouce.TaskOperator)
	resource.PackageConf = deploy.MustLoadConf("deploy").Packages["xcache"]
}

// initLoggers 初始化logger
// http://gdp.baidu-int.com/gdp2/docs/examples/foundation/12_log/
func initLoggers(ctx context.Context) (x1GormLogger logit.Logger) {
	{
		taskLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/task.toml"))
		if err != nil {
			panic(err.Error())
		}
		resource.LoggerTask = taskLogger

		TryRegisterCloser(taskLogger) // 注册 closer,这样可以保证程序进程退出前，日志全部落盘
	}
	{
		gormLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/gorm.toml"))
		if err != nil {
			panic(err.Error())
		}
		resource.LoggerGorm = gormLogger

		TryRegisterCloser(gormLogger)
	}
	{
		optConfigFile := logit.OptConfigFile("logit/base.toml")

		defaultLogger := logger.NewLogger(ctx, optConfigFile)
		sdkLogger := logger.NewLogger(ctx, optConfigFile, logger.WithModuleName("SDK"))
		componentLogger := logger.NewLogger(ctx, optConfigFile, logger.WithModuleName("COMPO"))

		logger.SetDefaultLogger(defaultLogger)
		logger.SetSdkLogger(sdkLogger)
		logger.SetComponentLogger(componentLogger)

		TryRegisterCloser(defaultLogger) // 注册 closer,这样可以保证程序进程退出前，日志全部落盘
		TryRegisterCloser(sdkLogger)
		TryRegisterCloser(componentLogger)
	}
	var err error
	x1GormLogger, err = logit.NewLogger(ctx, logit.OptConfigFile("logit/gorm.toml"))
	if err != nil {
		panic(err.Error())
	}
	return
}

// initRal 初始化服务配置 以及ral组件
// http://gdp.baidu-int.com/gdp2/docs/examples/client/31_ral/
func initRal(ctx context.Context) {
	if err := ral.InitDefault(ctx); err != nil {
		panic(err.Error())
	}
}

// loadServicer 加载服务配置
// http://gdp.baidu-int.com/gdp2/docs/examples/client/30_servicer/
func loadServicer(ctx context.Context) {
	pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
}

// initMySQL 初始化mysql
// http://gdp.baidu-int.com/gdp2/docs/examples/client/32_mysql/
func initMySQL(ctx context.Context, x1GormLogger logit.Logger) {
	// client 初始化为单例，放到 resource 里去

	TryRegisterCloser(x1GormLogger) // 注册 closer,这样可以保证程序进程退出前，日志全部落盘
	vdbmodel.Init(context.Background(), vdbmodel.Conf{GormLogger: x1GormLogger, ServicerName: "x1-model"})
}

// initRedis 初始化redis
// http://gdp.baidu-int.com/gdp2/docs/examples/client/33_redis/
func initRedis(_ context.Context) {
	cli := mustInitOneRedis("broker-redis")
	resource.RedisClient = cli
	lock.MustInit(cli)
}

func mustInitOneRedis(name string) redis.Client {
	opts := []redis.ClientOption{
		// redis.OptHooker(redis.NewMetricsHook(nil)),
		// redis.OptHooker(redis.NewLogHook()),
	}

	client, err := redis.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}

var closeFns []func() error

// TryRegisterCloser 注册 close 方法
//
//	若有资源需要在程序退出前进行清理，请使用该方法注册
//	可以注册实现了 io.Closer 的对象
//	或者是直接注册close方法，类型是 func()error
func TryRegisterCloser(comp any) {
	if c, ok := comp.(io.Closer); ok {
		closeFns = append(closeFns, c.Close)
		return
	}
	if fn, ok := comp.(func() error); ok {
		closeFns = append(closeFns, fn)
	}
}

// BeforeShutdown 退出前执行，资源清理、日志落盘等
func BeforeShutdown() {
	for _, fn := range closeFns {
		_ = fn()
	}
}

func initVdbConf(ctx context.Context) {
	err := conf.MustLoadVdbConf()
	if err != nil {
		panic(err.Error())
	}
	err = conf.InitPkgManConf()
	if err != nil {
		panic(err.Error())
	}
}
