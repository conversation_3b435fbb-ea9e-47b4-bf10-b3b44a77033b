/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/11/30, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
使用Redis实现的的Backend 单元测试
*/

package backend

import (
	"context"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

var (
	redisClient redis.Client
)

func init() {
	unittest.UnitTestInit(1)
	opts := []redis.ClientOption{
		redis.OptHooker(redis.NewLogHook()),
	}
	client, err := redis.NewClient("broker-redis", opts...)
	if err != nil {
		panic(err.Error())
	}
	redisClient = client
}

func TestBackend(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := redisClient.Do(ctx, "FLUSHALL").Err(); err != nil {
		t.Fatalf("flush redis error; err: %s", err.Error())
	}
	be := NewRedisBackend(redisClient)
	ters := []TaskExecResult{{
		TaskExecUnitID: "unit-001",
		TaskBatchID:    "batch-01",
	}, {
		TaskExecUnitID: "unit-002",
		TaskBatchID:    "batch-01",
	}, {
		TaskExecUnitID: "unit-003",
		TaskBatchID:    "batch-02",
	}}
	for _, ter := range ters {
		if err := be.Report(ctx, &ter); err != nil {
			t.Fatalf("report task ret failed")
		}
	}
	{
		bRet, err := be.GetReports(ctx, "batch-01")
		if err != nil {
			t.Fatalf("get reports failed")
		}
		if len(bRet) != 2 {
			t.Fatalf("expect get 2 reports, actual %d", len(bRet))
		}
	}
	{
		bRet, err := be.GetReports(ctx, "batch-02")
		if err != nil {
			t.Fatalf("get reports failed")
		}
		if len(bRet) != 1 {
			t.Fatalf("expect get 1 reports, actual %d", len(bRet))
		}
	}
	if err := be.DeleteReports(ctx, "batch-02"); err != nil {
		t.Fatalf("del reports failed")
	}
	{
		bRet, err := be.GetReports(ctx, "batch-02")
		if err != nil {
			t.Fatalf("get reports failed")
		}
		if len(bRet) != 0 {
			t.Fatalf("expect get 0 reports, actual %d", len(bRet))
		}
	}
	if err := be.DeleteReport(ctx, &ters[1]); err != nil {
		t.Fatalf("del reports failed")
	}
	{
		bRet, err := be.GetReports(ctx, "batch-01")
		if err != nil {
			t.Fatalf("get reports failed")
		}
		if len(bRet) != 1 {
			t.Fatalf("expect get 1 reports, actual %d", len(bRet))
		}
	}
}

func TestBackendWorkStatus(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := redisClient.Do(ctx, "FLUSHALL").Err(); err != nil {
		t.Fatalf("flush redis error; err: %s", err.Error())
	}
	be := NewRedisBackend(redisClient)
	wss := []WorkerStatus{{
		WorkerID: "worker-01",
		Status:   "normal",
	}, {
		WorkerID: "worker-02",
		Status:   "normal",
	}, {
		WorkerID: "worker-03",
		Status:   "normal",
	}}
	for _, ws := range wss {
		if err := be.ReportWorkerStatus(ctx, &ws); err != nil {
			t.Fatalf("report work status failed")
		}
	}
	bwss, err := be.GetWorkerStatus(ctx)
	if err != nil {
		t.Fatalf("get work status failed")
	}
	if len(bwss) != 3 {
		t.Fatalf("expect get 3 reports, actual %d", len(bwss))
	}
}
