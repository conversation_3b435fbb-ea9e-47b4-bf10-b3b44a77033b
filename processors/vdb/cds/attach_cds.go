package cds

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessAttachDataCds 新bcc挂载数据盘cds资源
// 1. 获取todelete node，拿到待挂载的cds
// 2. 调用bcc接口查询cds是否挂载在新bcc上，如果已挂载直接返回，否则挂载
// 2. 调用bcc接口挂载cds
func ProcessAttachCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to attach cds in non-standalone mode")
		return nil
	}

	toCreateNode, err := getToCreateNode(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get tocreate node error", logit.String("appId", app.AppID), logit.Error("error", err))
		return err
	}
	if toCreateNode.VolumeID != "" {
		resource.LoggerTask.Notice(ctx, "cds already attached", logit.String("volumeId", toCreateNode.VolumeID))
		return nil
	}

	bccResourceID := toCreateNode.ResourceID
	if bccResourceID == "" {
		resource.LoggerTask.Error(ctx, "get bcc resource id in tocreate node error", logit.String("nodeId", toCreateNode.NodeID))
		return errors.New("get bcc resource id in tocreate node error")
	}

	toDeleteNode, err := getToDeleteNode(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get todelete node error", logit.String("appId", app.AppID), logit.Error("error", err))
		return err
	}
	volumeID := toDeleteNode.VolumeID
	if volumeID == "" {
		resource.LoggerTask.Error(ctx, "no cds volume id found in todelete node", logit.String("nodeId", toDeleteNode.NodeID))
		return errors.New("no cds volume id found in todelete node")
	}

	err = attachCdsToNewBcc(ctx, bccResourceID, volumeID, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "attach cds volume id failed", logit.Error("error", err))
		return err
	}

	toCreateNode.VolumeID = volumeID
	if err := vdbmodel.NodesSave(ctx, []*vdbmodel.Node{toCreateNode}); err != nil {
		resource.LoggerTask.Error(ctx, "save node error", logit.Error("error", err))
		return err
	}
	toDeleteNode.VolumeID = ""
	if err := vdbmodel.NodesSave(ctx, []*vdbmodel.Node{toDeleteNode}); err != nil {
		resource.LoggerTask.Error(ctx, "save node error", logit.Error("error", err))
		return err
	}

	return nil
}

// getToCreateNode 获取tocreate node
func getToCreateNode(ctx context.Context, app *vdbmodel.Application) (*vdbmodel.Node, error) {
	toCreateNodes := make([]*vdbmodel.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			toCreateNodes = append(toCreateNodes, node)
		}
	}

	if len(toCreateNodes) == 0 {
		resource.LoggerTask.Notice(ctx, "no tocreate node found")
		return nil, errors.New("tocreate node not found")
	}
	if len(toCreateNodes) > 1 {
		resource.LoggerTask.Error(ctx, "too many tocreate nodes.", logit.String("toCreateNodes", base_utils.Format(toCreateNodes)))
		return nil, errors.New("too many tocreate nodes")
	}

	return toCreateNodes[0], nil
}

// getToDeleteNode 获取todelete node
func getToDeleteNode(ctx context.Context, app *vdbmodel.Application) (*vdbmodel.Node, error) {
	toDeleteNodes := make([]*vdbmodel.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToDelete && node.Status != vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}
			toDeleteNodes = append(toDeleteNodes, node)
		}
	}

	if len(toDeleteNodes) == 0 {
		resource.LoggerTask.Notice(ctx, "no todelete node found")
		return nil, errors.New("todelete node not found")
	}
	if len(toDeleteNodes) > 1 {
		resource.LoggerTask.Error(ctx, "too many todelete nodes.", logit.String("toDeleteNodes", base_utils.Format(toDeleteNodes)))
		return nil, errors.New("too many todelete nodes")
	}

	return toDeleteNodes[0], nil
}

// attachCdsToNewBcc 调用bcc接口挂载cds到新bcc
func attachCdsToNewBcc(ctx context.Context, bccResourceID string, volumeID string, userID string) error {
	// check bcc cds info
	volumes, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
		VmID:      bccResourceID,
		UserID:    userID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cds info failed", logit.Error("error", err))
		return err
	}

	cdsAttached := false
	for _, volume := range volumes {
		if volume.Id == volumeID {
			cdsAttached = true
			break
		}
	}
	if cdsAttached {
		resource.LoggerTask.Notice(ctx, "no need to attach cds due to cds is attached", logit.String("volumeID", volumeID),
			logit.String("bccSourceID", bccResourceID))
		return nil
	}

	// attach cds
	err = bccresource.BccResourceOp().AttachCds(ctx, &bccresource.AttachCdsParam{
		VmID:      bccResourceID,
		VolumeID:  volumeID,
		UserID:    userID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "attach cds failed", logit.String("volumeID", volumeID),
			logit.String("bccSourceID", bccResourceID))
		return err
	}

	return nil
}
