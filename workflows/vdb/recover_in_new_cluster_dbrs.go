package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/recover"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowVdbRecoverInNewClusterDbrs      = "vdb-recover-in-new-cluster-dbrs-app"
	StepRecoverInNewClusterDbrsCloseHa      = "vdb-recover-in-new-cluster-dbrs-close-ha"
	StepRecoverInNewClusterDbrsDispatchTask = "vdb-recover-in-new-cluster-dbrs-dispatch-task"
	StepRecoverInNewClusterDbrsCheckStatus  = "vdb-recover-in-new-cluster-dbrs-check-status"
	StepRecoverInNewClusterDbrsRecoverHa    = "vdb-recover-in-new-cluster-dbrs-recover-ha"
	StepRecoverInNewClusterDbrsCallBack     = "vdb-recover-in-new-cluster-dbrs-cb"
)

func init() {
	// Step-1 关闭HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDbrsCloseHa,
		Workflow:        WorkflowVdbRecoverInNewClusterDbrs,
		StepProcess:     xmaster.ProcessCloseAppHaStatusInXmaster,
		SuccessNextStep: StepRecoverInNewClusterDbrsDispatchTask,
		ErrorNextStep:   StepRecoverInNewClusterDbrsCloseHa,
	})

	// Step-2 dispatch task
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDbrsDispatchTask,
		Workflow:        WorkflowVdbRecoverInNewClusterDbrs,
		StepProcess:     recover.ProcessDbrsRecoverInNewCluster,
		SuccessNextStep: StepRecoverInNewClusterDbrsCheckStatus,
		ErrorNextStep:   StepRecoverInNewClusterDbrsDispatchTask},

		workflow.WithStepTimeout(60*time.Minute))

	// Step-3 检查状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDbrsCheckStatus,
		Workflow:        WorkflowVdbRecoverInNewClusterDbrs,
		StepProcess:     mochow.ProcessCheckAllStatus,
		SuccessNextStep: StepRecoverInNewClusterDbrsRecoverHa,
		ErrorNextStep:   StepRecoverInNewClusterDbrsCheckStatus},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-4 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDbrsRecoverHa,
		Workflow:        WorkflowVdbRecoverInNewClusterDbrs,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: StepRecoverInNewClusterDbrsCallBack,
		ErrorNextStep:   StepRecoverInNewClusterDbrsRecoverHa,
	})

	// Step-5 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDbrsCallBack,
		Workflow:        WorkflowVdbRecoverInNewClusterDbrs,
		StepProcess:     callback.ProcessRecoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverInNewClusterDbrsCallBack,
	})
}
