package backup

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/component/repo"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	vdbIface "icode.baidu.com/baidu/vdb/x1-api/servers/httpserver/ifaces/vdb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessCheckAndUpdataManualBackupTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appID := teu.Entity

	// 获取备份集Id
	params := &dbrs.CreateBackupTaskResponse{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), params)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal request fail", logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal request fail,err:%s", unmarshalError.Error())
	}
	appBackupID := params.AppDataBackupID

	repoConf := repo.GetRepo(backupRepo).Conf

out:
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)

			getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
				DataType:             vdbmodel.ProductVDB,
				AppID:                appID,
				AppDataBackupID:      params.AppDataBackupID,
				DownloadUrlExpireSec: 43200,
				Product:              vdbmodel.ProductVDB,
			}

			appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
			if err != nil {
				errMsg := "call dbrs api get app backup detail failed"
				resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
				return cerrs.ErrInvalidParams.Errorf(errMsg)
			}

			isAllDone := true
			isAllSuccess := true
			// 获取目标备份集
			validBackupRecord, err := vdbmodel.GetValidBackupByAppID(ctx, appID)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get valid backup by appID failed", logit.Error("err", err))
				return err
			}
			var appBackup *vdbmodel.AppBackup
			for _, backupRecord := range validBackupRecord {
				if backupRecord.AppBackupID == appBackupID {
					resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
					appBackup = backupRecord
				}
			}
			if appBackup == nil {
				errMsg := "get appBackup failed"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
				return cerrs.ErrInvalidParams.Errorf(errMsg)
			}

			if appBackupDetail.Status == "failed" {
				appBackup.Status = vdbmodel.BackupFailed
				saveErr := vdbmodel.SaveBackup(ctx, []*vdbmodel.AppBackup{appBackup})
				if saveErr != nil {
					resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
					return err
				}

				errMsg := "dbrs get app backup detail return failed status"
				resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
				return nil
			}

			if len(appBackupDetail.AppDataBackupShows) == 0 {
				resource.LoggerTask.Notice(ctx, "appBackup is doing", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
				isAllDone = false
				isAllSuccess = false
			} else {
				for _, backupRecord := range appBackupDetail.AppDataBackupShows {
					if appBackupDetail.AppDataBackupID == appBackupID {
						// 更新集群备份状态
						if backupRecord.StartDateTime != "" {
							if backupRecord.Status != vdbmodel.BackupSuccess && backupRecord.Status != vdbmodel.BackupFailed {
								isAllDone = false
							}
							if backupRecord.Status == vdbmodel.BackupFailed {
								isAllSuccess = false
							}
						} else {
							resource.LoggerTask.Notice(ctx, "appBackup is doing", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
							isAllDone = false
							isAllSuccess = false
						}
					}
				}
			}
			// 判断是否全部备份完成，如果全部备份完成，修改isAllDone为true，并且修改appBackup.status字段
			if isAllDone {
				errMsg := "appBackup all done"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
			}

			if isAllSuccess {
				errMsg := "appBackup all success"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
				appBackup.Status = vdbmodel.BackupSuccess
			}

			if !isAllDone {
				resource.LoggerTask.Notice(ctx, appID+" "+appBackupID+" is not all done, continue.")
				continue
			} else {
				// 更新信息到数据库
				for _, backupRecord := range appBackupDetail.AppDataBackupShows {
					if appBackupDetail.AppDataBackupID == appBackupID {
						// 更新集群备份状态
						startTime, err := time.Parse(time.RFC3339, backupRecord.StartDateTime)
						if err != nil {
							resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
							return err
						}
						access := ""
						if len(backupRecord.OuterLinks) > 0 {
							access = backupRecord.OuterLinks[0]
						}
						appBackup.AppBackupItems = append(appBackup.AppBackupItems, &vdbmodel.AppBackupItem{
							AppBackupID: appBackupID,
							BackupID:    backupRecord.DataBackupID,
							NodeID:      backupRecord.ClusterID,
							StartTime:   startTime,
							Duration:    GetDuration(backupRecord.Duration),
							Status:      backupRecord.Status,
							Bucket:      repoConf.BosBucket,
							ObjectKey:   backupRecord.DstStorageObjectID,
							Access:      access,
							ObjectSize:  backupRecord.BackupSizeBytes,
						})
					}
				}
				// 保存数据库
				saveErr := vdbmodel.SaveBackup(ctx, []*vdbmodel.AppBackup{appBackup})
				if saveErr != nil {
					resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
					return err
				}
			}
			break out
		}
	}
	return nil
}

// 例行备份已结束,更新备份结果至VDB数据库
func ProcessRefreshBackupTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appID := teu.Entity

	taskParams := &vdbIface.NoticeRoutineBackupTaskRequest{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), taskParams)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal teu.Parameters fail", logit.String("params:", base_utils.Format(taskParams)), logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal teu.Parameters fail,err:%s", unmarshalError.Error())
	}
	appBackupID := taskParams.BackupID
	dataType := vdbmodel.ProductVDB

	// 调用dbrs接口获取备份结果
	getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
		DataType:             dataType,
		AppID:                appID,
		AppDataBackupID:      appBackupID,
		DownloadUrlExpireSec: 43200,
		Product:              vdbmodel.ProductVDB,
	}

	appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
	if err != nil {
		errMsg := "call dbrs api get app backup detail failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	resource.LoggerTask.Trace(ctx, "get app backup detail success", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))

	// 获取目标备份集
	validBackupRecord, err := vdbmodel.GetValidBackupByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appID failed", logit.Error("err", err))
		return err
	}
	var appBackup *vdbmodel.AppBackup
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == appBackupID {
			resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
			appBackup = backupRecord
		}
	}
	if appBackup == nil {
		errMsg := "get appBackup failed"
		resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	repoConf := repo.GetRepo(backupRepo).Conf

	for _, backupRecord := range appBackupDetail.AppDataBackupShows {
		if appBackupDetail.AppDataBackupID == appBackupID {
			// 更新集群备份状态
			startTime, err := time.Parse(time.RFC3339, backupRecord.StartDateTime)
			if err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
				return err
			}

			// 备份下载链接
			access := ""
			if len(backupRecord.OuterLinks) > 0 {
				access = backupRecord.OuterLinks[0]
			}
			appBackup.AppBackupItems = append(appBackup.AppBackupItems, &vdbmodel.AppBackupItem{
				AppBackupID: appBackupID,
				BackupID:    backupRecord.DataBackupID,
				NodeID:      backupRecord.ClusterID,
				StartTime:   startTime,
				Duration:    GetDuration(backupRecord.Duration),
				Status:      backupRecord.Status,
				Bucket:      repoConf.BosBucket,
				ObjectKey:   backupRecord.DstStorageObjectID,
				Access:      access, // 没有下载链接,此值为空
				ObjectSize:  backupRecord.BackupSizeBytes,
			})
		}
	}
	appBackup.Status = vdbmodel.BackupSuccess
	if taskParams.Status == "failed" {
		appBackup.Status = vdbmodel.BackupFailed
	}

	saveErr := vdbmodel.SaveBackup(ctx, []*vdbmodel.AppBackup{appBackup})
	if saveErr != nil {
		resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessDeleteBackupPolicy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appID := teu.Entity

	if err := util.DeleteBackupPolicyIfNeeded(ctx, appID); err != nil {
		resource.LoggerTask.Error(ctx, "check and delete backup policy failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessCheckAndClearExpireBackupRecord(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appID := teu.Entity
	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 获取备份配置
	backupConfig := app.BackupConfig
	if backupConfig == "" {
		errMsg := "app.BackupConfig is null"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("app:", base_utils.Format(appID)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	backupConfigSplit := strings.Split(backupConfig, ";")
	if len(backupConfigSplit) != 3 {
		errMsg := "app.BackupConfig format error"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("app:", base_utils.Format(appID)),
			logit.String("backupConfig:", base_utils.Format(backupConfig)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	retentionTime := backupConfigSplit[2]
	var backupModels []*vdbmodel.AppBackup
	// 获取自动备份数量
	backupModels, err = vdbmodel.AppBackupGetAllByCond(ctx, "app_id = ? and backup_type = ?", appID, vdbmodel.BackupModeAutomatic)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get backup record failed", logit.String("app:", base_utils.Format(appID)),
			logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "backup record detail", logit.String("app:", base_utils.Format(appID)),
		logit.String("len(backupModels):", base_utils.Format(len(backupModels))),
		logit.String("backupModels:", base_utils.Format(backupModels)))
	if len(backupModels) <= cast.ToInt(retentionTime) {
		// 无需清理过期备份记录
		resource.LoggerTask.Notice(ctx, "need not clear backup record", logit.String("app:", base_utils.Format(appID)),
			logit.String("len(backupModels):", base_utils.Format(len(backupModels))),
			logit.String("retentionTime:", base_utils.Format(retentionTime)))
	} else {
		/*
			清理过期备份记录,清理策略:
			1、保留时间*2 以外的备份记录直接删除
			2、保留时间-保留时间*2 之间的备份记录标记为过期
		*/
		for _, backupRecord := range backupModels {
			// 自动备份 备份时间超过最大保留时间*2，则删除
			if util.GetTimeUptoNow(backupRecord.StartTime) > 2*backupRecord.Expairation*86400 {
				resource.LoggerTask.Notice(ctx, "to delete backup record", logit.String("app:", base_utils.Format(appID)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
				// 删除记录
				toDeleteAppBackus := []*vdbmodel.AppBackup{}
				toDeleteAppBackus = append(toDeleteAppBackus, backupRecord)
				if err := vdbmodel.BackupDeleteMulti(ctx, toDeleteAppBackus); err != nil {
					resource.LoggerTask.Error(ctx, "delete backup meta failed", logit.Error("error", err))
					return err
				}

				resource.LoggerTask.Notice(ctx, "delete backup record success", logit.String("app:", base_utils.Format(appID)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
			} else if util.GetTimeUptoNow(backupRecord.StartTime) > backupRecord.Expairation*86400 {
				resource.LoggerTask.Notice(ctx, "expire backup record", logit.String("app:", base_utils.Format(appID)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
				// 将备份记录设置为过期
				backupRecord.Status = vdbmodel.BackupExpired
				if saveErr := vdbmodel.SaveBackup(ctx, []*vdbmodel.AppBackup{backupRecord}); saveErr != nil {
					resource.LoggerTask.Error(ctx, "update backup record failed", logit.String("backupRecord :",
						base_utils.Format(backupRecord)), logit.Error("saveErr", saveErr))
					return err
				}
				resource.LoggerTask.Notice(ctx, "expire backup record success", logit.String("app:", base_utils.Format(appID)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
			} else {
				resource.LoggerTask.Notice(ctx, "backup record do nothing", logit.String("app:", base_utils.Format(appID)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
			}
		}
	}
	return nil
}

func GetDuration(duration string) int64 {
	//"0d 0h 2m 31s"   转成s
	if duration == "" {
		return 0
	}

	split := strings.Split(duration, " ")
	dayStr := split[0]
	hourStr := split[1]
	minuteStr := split[2]
	secondStr := split[3]
	day := strings.Split(dayStr, "d")[0]
	hour := strings.Split(hourStr, "h")[0]
	minute := strings.Split(minuteStr, "m")[0]
	second := strings.Split(secondStr, "s")[0]
	cost := cast.ToInt64(day)*24*3600 + cast.ToInt64(hour)*3600 + cast.ToInt64(minute)*60 + cast.ToInt64(second)
	fmt.Println(cost)
	return cost
}
