/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package resize

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/common"
)

func sendResizeFsToXagent(ctx context.Context, app *vdbmodel.Application, node *vdbmodel.Node) error {
	if app.ResourceType == "container" {
		resource.LoggerTask.Warning(ctx, "resize fs not support container")
		return nil
	}
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: "resize_fs",
	}

	resource.LoggerTask.Trace(ctx, "send resize fs task", logit.String("raw params", base_utils.Format(req)))

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resize fs fail",
			logit.String("nodeId", node.NodeID),
			logit.Error("err", err))
	}
	return err
}

func sendUpdateMaxSpaceToXagent(ctx context.Context, app *vdbmodel.Application, node *vdbmodel.Node, cluster *vdbmodel.Cluster) error {
	if app.ResourceType != "container" {
		resource.LoggerTask.Warning(ctx, "update max space no need for bcc")
		return nil
	}

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: "env_vars_op",
		Params: &common.UpdateEnvVarsXagentRequest{
			Meta: &xagent.Meta{
				Basedir: "/home/<USER>/",
			},
			Op: "set",
			ConfigList: []*common.ConfigItem{{
				Name:  "MAX_SPACE",
				Value: base_utils.ToString(cluster.DiskSize),
			}},
		},
	}

	resource.LoggerTask.Trace(ctx, "send update env vars task", logit.String("raw params", base_utils.Format(req)))

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update env vars fail",
			logit.String("nodeId", node.NodeID),
			logit.Error("err", err))
	}
	return err
}

func ProcessResizeFs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, fmt.Sprintf("app %s ProcessResizeFs", app.AppID))

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster
	find := false

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusModifying {
				targetNode = node
				targetCluster = cluster
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "target node not found", logit.String("nodeId", param.TargetNodeID))
		return fmt.Errorf("target node %s not found", param.TargetNodeID)
	}

	if targetCluster.ActualDiskSize >= int(targetCluster.DiskSize) {
		resource.LoggerTask.Notice(ctx, "disk size no need resizefs", logit.String("nodeID", param.TargetNodeID))
		return nil
	}

	err = sendResizeFsToXagent(ctx, app, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "resize fs failed", logit.String("nodeId", param.TargetNodeID))
		return err
	}

	err = sendUpdateMaxSpaceToXagent(ctx, app, targetNode, targetCluster)
	if err != nil {
		resource.LoggerTask.Error(ctx, "update max space failed", logit.String("nodeId", param.TargetNodeID))
		return err
	}

	return nil
}

func ProcessStandaloneResizeFs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			targetNode = node
			targetCluster = cluster
			break OuterLoop
		}
	}

	if targetCluster.ActualDiskSize >= int(targetCluster.DiskSize) {
		resource.LoggerTask.Notice(ctx, "disk size no need resizefs", logit.String("nodeID", targetNode.NodeID))
		return nil
	}

	err = sendResizeFsToXagent(ctx, app, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "resize fs failed", logit.String("nodeId", targetNode.NodeID))
		return err
	}

	err = sendUpdateMaxSpaceToXagent(ctx, app, targetNode, targetCluster)
	if err != nil {
		resource.LoggerTask.Error(ctx, "update max space failed", logit.String("nodeId", targetNode.NodeID))
		return err
	}

	return nil
}
