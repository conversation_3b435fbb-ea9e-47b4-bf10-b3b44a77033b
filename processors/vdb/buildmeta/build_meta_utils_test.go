package buildmeta

import (
	"context"
	"testing"
)

func TestGetDeployConfVersion(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "test-get-deploy-version",
			args: args{
				ctx: context.Background(),
			},
			want:    "20220216190700",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := GetDeployConfVersion(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeployConfVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
