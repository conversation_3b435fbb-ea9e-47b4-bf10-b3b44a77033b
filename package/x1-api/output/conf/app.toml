# app.toml: 应用主配置文件

[Base]
# 应用名称，代码里可通过 env.AppName() 方法读取到
AppName = "x1-api"

# 运行模式，可配置值：
# debug    : 调试，    对应常量 env.RunModeDebug
# test     : 测试，    对应常量 env.RunModeTest
# release  : 线上发布， 对应常量 env.RunModeRelease
# 程序代码可以通过 env.RunMode() 获取该值
# 详见 https://gdp.baidu-int.com/api/baidu/gdp/env/
RunMode = "debug"

# 逻辑 IDC
# 代码里可通过 env.IDC() 方法读取到
IDC = "test"

[Meta]
# 定义应用使用的端口情况，可选，0-N 个
[[Meta.Ports]]
Name="Main"                  # 端口名称，必填，标识符类型（注①）
DevDefault = 8080            # 开发测试阶段使用的默认端口，必填。当从环境变量 LISTEN_PORT_1 没有取到值的时候使用
# 取值来源，可选。
# 当值为 env，从环境变量中读取（key 定义在 Value 字段）
# 当值为空，或者是 Manual 时，使用 Value 里存储的端口 （ ECI 集群不支持 ）
From = "Manual"
Value = "8200"        # 取值字段，必填

[[Meta.Ports]]
Name="Admin"
DevDefault = 8081
From = "Manual"
Value = "8201"

# 定义在 PaaS 平台上，（实例列表页面或者实例详情页面）需要展示的连接
# 可以有 0-N 个
[[Meta.Links]]
Name = "GDP应用面板"              # Name 展现名称，必填
URI = "/debug/panel/"            # URI 链接地址，URI 和 URL 不同时存在，但应存在一个。URI 字段优先级更高
# 当 URI 有值的时候，必填。在拼接链接的时候会使用，拼接效果  http://$hostname:$PortName$URI
PortName = "Admin"
