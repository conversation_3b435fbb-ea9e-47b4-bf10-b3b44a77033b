/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package xmaster

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/ha"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessCloseAppHaStatusInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	err = ha.SetHASwitchInXmaster(ctx, app.AppID, "close")
	if err != nil {
		resource.LoggerTask.Error(ctx, "set app ha close failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessRecoverAppHaStatusInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	haStatus := app.HAStatus
	if haStatus == "close" {
		resource.LoggerTask.Trace(ctx, "app ha status is close,no need to recover", logit.String("haStatus", haStatus))
		return nil
	}

	err = ha.SetHASwitchInXmaster(ctx, app.AppID, "open")
	if err != nil {
		resource.LoggerTask.Error(ctx, "set app ha open failed", logit.Error("error", err))
		return err
	}

	return nil
}
