# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Build Commands
```bash
# Full build process (prepare, compile, test, package)
make all

# Individual stages
make prepare    # Download dependencies and go mod tidy
make compile    # Build binary to bin/x1-task-manager
make build      # Alias for compile
make test       # Run tests with coverage, outputs to coverage.out and unittest.txt
make package    # Package output to output/ directory (includes conf_online as conf/)
make clean      # Remove build artifacts
```

### Development Commands
```bash
# Run with different configurations
go run main.go                        # Using conf/ directory
go run main.go -conf conf/app.toml     # Using conf/ directory
go run main.go -conf conf_qa/app.toml  # Using conf_qa/ directory

# Testing
go test -race -v -cover ./...          # Run all tests with race detection
go test -race -timeout=120s -v -cover $(GOPKGS) -coverprofile=coverage.out | tee unittest.txt  # Full test command

# Update GDP components
gdp get
# or
go get icode.baidu.com/baidu/gdp/...

# Single test package
go test -race -v -cover ./processors/vdb/buildmeta
```

## Architecture Overview

### Core Components

**Task Manager**: This is a VDB (Vector Database) task manager built on the GDP2 framework. It manages complex workflows for creating, modifying, and maintaining VDB cluster instances.

**Workflow Engine**: The system uses a step-based workflow engine where each operation (like creating a cluster) is broken down into discrete steps with success/error handling and rollback capabilities.

**Key Modules**:
- `workflows/vdb/` - Main workflow definitions (create cluster, modify specs, etc.)
- `processors/vdb/` - Individual step processors organized by functionality
- `bootstrap/` - Application initialization and dependency setup
- `backend/` - Task execution backend interface (Redis-based)
- `library/` - Shared utilities and types

### Workflow Structure

Workflows are defined with sequential steps, each having:
- Success next step
- Error next step (for retry or rollback)
- Timeout configurations
- Rollback procedures

Example workflow steps for cluster creation:
1. Build metadata → Fill specifications → Check subnets → Create security groups
2. Apply resources → Deploy nodes → Initialize cluster → Success callback
3. Error handling with rollback steps to clean up resources

### Configuration System

**Multi-environment support**:
- `conf/` - Development configuration (ignored in builds)
- `conf_online/` - Production configuration (packaged as conf/ in builds)
- `conf_qa/` - Testing configuration (ignored in builds)

**Key config files**:
- `conf/app.toml` - Main application configuration
- `conf/server.toml` - Server ports and timeouts
- `conf/logit/*.toml` - Logging configuration per component
- `conf/servicer/*.toml` - Downstream service configurations

### Resource Management

The system manages cloud resources through:
- **BCC Resources**: Virtual machines and compute resources
- **BLB (Load Balancer)**: Traffic distribution
- **Security Groups**: Network access control
- **DNS**: Domain name management
- **CDS**: Cloud disk storage
- **Endpoints**: Service endpoints

### Task Execution

- **Redis Backend**: Uses Redis for task queuing and state management
- **Worker Status**: Tracks worker health (idle/running/abnormal)
- **Task Results**: Stores execution results with success/error status
- **Rollback Support**: Automatic cleanup on failures

## Important Notes

- Built on Baidu's GDP2 framework
- Requires specific Baidu internal dependencies (icode.baidu.com/baidu/*)
- Uses GORM for database operations
- Implements comprehensive logging with multiple loggers
- Supports both standalone and cluster VDB deployments
- Includes monitoring and health check capabilities