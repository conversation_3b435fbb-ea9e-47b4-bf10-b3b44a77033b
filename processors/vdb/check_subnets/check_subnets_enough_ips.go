/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
检查子网ip是否充足，是否需要使用备用子网
*/

package checksubnets

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	subnet "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/subnet"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// CheckEnoughIPs 检查子网ip是否充足，是否需要使用备用子网
// (1) 获取子网剩余ips，OpenStackSDK::get_subnet_total_ip_num - OpenStackSDK::get_subnet_used_ip_num 如果没有可用子网，则报错退出
// (2) 检查所有需要新创建的node、proxy；subnet字段填入ip数量充足的子网, 优先级低
// 参考代码位于FillSubnetIdProcessor::process
func CheckEnoughIPs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err // todo 细化err
	}

	if app.ResourceType == "container" {
		return nil
	}

	iamUserID := app.UserID

	replicas := param.Replicas
	if len(replicas) == 0 {
		err = json.Unmarshal([]byte(app.AzDeployInfo), &replicas)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "unmarshal replicas fail", logit.String("app.Replicas", app.AzDeployInfo),
				logit.Error("error", err))
			return err // todo 细化err
		}
	}
	subnetList := make([]string, 0)
	mapLogicZone2Subnet := make(map[string][]string) // 逻辑可用区=>subnetIds ，用于寻找同Az下其他subnetId
	for _, v := range replicas {
		subnetID := v.SubnetID
		subnetList = append(subnetList, subnetID)
		mapLogicZone2Subnet[v.Zone] = append(mapLogicZone2Subnet[v.Zone], subnetID)

	}
	subnetCompent := subnet.Instance()
	mapSubnetID2AvailableIPNum, err := subnetCompent.GetAvailableIPCount(ctx, &subnet.GetAvailableIPCountParams{
		UserID: iamUserID,
		Subnet: subnetList,
	}) // subnetId=>可用Ip数量

	if err != nil {
		resource.LoggerTask.Warning(ctx, "get available ip error", logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return err // todo 细化err
	}

	resource.LoggerTask.Notice(ctx, "get available ip suc", logit.String("mapSubnetId2IpNums", base_utils.Format(mapSubnetID2AvailableIPNum)))

	for _, cluster := range app.Clusters {
		if err := checkClusterIPEnough(ctx, cluster, mapLogicZone2Subnet, mapSubnetID2AvailableIPNum); err != nil {
			return err
		}
	}

	for _, itf := range app.Interfaces {
		if err := checkInterfaceIPEnough(ctx, itf, mapLogicZone2Subnet, mapSubnetID2AvailableIPNum); err != nil {
			return err
		}
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err // todo 细化err
	}
	return nil
}

func checkClusterIPEnough(ctx context.Context, cluster *vdbmodel.Cluster, mapLogicZone2Subnet map[string][]string,
	mapSubnetID2AvailableIPNum map[string]int64) error {

	for _, node := range cluster.Nodes {
		// 只处理待创建节点
		if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}
		az := node.LogicZone
		origSubnetID := node.SubnetID

		// 优先用节点原分配的子网
		if mapSubnetID2AvailableIPNum[origSubnetID] > 0 {
			mapSubnetID2AvailableIPNum[origSubnetID]--
			continue
		}

		// 原子网不够，尝试同AZ下其他子网
		found := false
		for _, candidateSubnetID := range mapLogicZone2Subnet[az] {
			if candidateSubnetID == origSubnetID {
				continue // 跳过原子网
			}
			if mapSubnetID2AvailableIPNum[candidateSubnetID] > 0 {
				resource.LoggerTask.Notice(ctx, "switch to another subnet due to ip shortage",
					logit.String("az", az),
					logit.String("from_subnet", origSubnetID),
					logit.String("to_subnet", candidateSubnetID))
				node.SubnetID = candidateSubnetID
				mapSubnetID2AvailableIPNum[candidateSubnetID]--
				found = true
				break
			}
		}
		if !found {
			resource.LoggerTask.Warning(ctx, "not enough ips for node",
				logit.String("az", az),
				logit.String("node_id", node.NodeID))
			return fmt.Errorf("not enough ips in az %s for node %s", az, node.NodeID)
		}
	}
	return nil
}

func checkInterfaceIPEnough(ctx context.Context, itf *vdbmodel.Interface, mapLogicZone2Subnet map[string][]string,
	mapSubnetID2AvailableIPNum map[string]int64) error {
	for _, proxy := range itf.Proxies {
		// 只处理待创建状态的Proxy
		if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}
		az := proxy.LogicZone
		origSubnetID := proxy.SubnetID

		// 优先用Proxy原分配的子网
		if mapSubnetID2AvailableIPNum[origSubnetID] > 0 {
			mapSubnetID2AvailableIPNum[origSubnetID]--
			continue
		}

		// 原子网不够，尝试同AZ下其他子网
		found := false
		for _, candidateSubnetID := range mapLogicZone2Subnet[az] {
			if candidateSubnetID == origSubnetID {
				continue // 跳过原子网
			}
			if mapSubnetID2AvailableIPNum[candidateSubnetID] > 0 {
				resource.LoggerTask.Notice(ctx, "switch to another subnet due to ip shortage",
					logit.String("az", az),
					logit.String("from_subnet", origSubnetID),
					logit.String("to_subnet", candidateSubnetID))
				proxy.SubnetID = candidateSubnetID
				mapSubnetID2AvailableIPNum[candidateSubnetID]--
				found = true
				break
			}
		}
		if !found {
			resource.LoggerTask.Warning(ctx, "not enough ips for proxy",
				logit.String("az", az),
				logit.String("proxy_id", proxy.ProxyID))
			return fmt.Errorf("not enough ips in az %s for proxy %s", az, proxy.ProxyID)
		}
	}
	return nil
}
