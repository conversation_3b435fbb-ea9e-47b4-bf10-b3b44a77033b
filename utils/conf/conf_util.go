/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/25 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file conf_util.go
 * <AUTHOR>
 * @date 2022/07/25 15:16:03
 * @brief conf util
 *
 **/

package conf

import (
	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/scs/x1-base/utils/mapstruct"
)

// IdcSpecialKey key for special
const IdcSpecialKey = "IDC_SPEC"

// LoadConf 增加IDC特异化 confDir 仅支持一层目录
func LoadConf(confDir, confName string, obj any) error {
	cf := map[string]any{}
	if err := conf.Parse(confDir+"/"+confName+".toml", &cf); err != nil {
		return err
	}

	cf = mergeIdcSpecial(cf, env.IDC())

	return mapstruct.DecodeEx(cf, obj, true, "toml")
}

func mergeIdcSpecial(cf map[string]any, idc string) map[string]any {
	var merged = map[string]any{}
	var idcSpecial map[string]any

	for k, v := range cf {
		if _v, ok := v.(map[string]any); ok {
			if k == IdcSpecialKey {
				if _v, has := _v[idc]; has {
					if _v, ok := _v.(map[string]any); ok {
						idcSpecial = _v
					}
				}
				continue
			}

			v = mergeIdcSpecial(_v, idc)
		}

		merged[k] = v
	}

	for k, v := range idcSpecial {
		merged[k] = v
	}

	return merged
}
