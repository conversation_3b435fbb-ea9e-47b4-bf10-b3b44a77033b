/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package resize

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	ResizeTimeout = 8 * time.Minute
)

func resizeNode(ctx context.Context, app *vdbmodel.Application, cluster *vdbmodel.Cluster, node *vdbmodel.Node) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			// pass
		}
		var err error
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("resize node %s", node.NodeID))
		hasResized := false
		resizeCtx := &bccresource.ResizeContext{}
		isResizing := false
		for i := 0; i < 5; i++ {
			resizeCtx = &bccresource.ResizeContext{}
			isResizing, err = bccresource.BccResourceOp().IsVmResizeInOperation(ctx, &bccresource.IsVmResizeInOperationParam{
				VmID:    node.ResourceID,
				UserID:  app.UserID,
				Context: resizeCtx,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get resize info failed", logit.Error("error", err))
				continue
			}
			break
		}
		if err != nil {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("node %s is not support resize for get info failed", node.NodeID))
			return err
		}
		if isResizing {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("node %s is resizing", node.NodeID),
				logit.String("node_info", base_utils.Format(resizeCtx.VmInfo)),
				logit.String("volumn_info", base_utils.Format(resizeCtx.DataVolume)))
			continue
		}

		if !hasResized {
			err = bccresource.BccResourceOp().ResizeVm(ctx, &bccresource.ResizeVmParam{
				VmID:                 node.ResourceID,
				UserID:               app.UserID,
				TargetCPUCount:       cluster.CPU,
				TargetMemorySizeInGB: cluster.MemSize,
				LiveResize:           false,
				Context:              resizeCtx,
			})
			if err == nil {
				resource.LoggerTask.Trace(ctx, fmt.Sprintf("send resize request target cpu %d, target mem %d", cluster.CPU, cluster.MemSize))
				hasResized = true
				continue
			} else if !cerrs.Is(err, cerrs.ErrBCCHasResized) {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("node %s resize failed", node.NodeID), logit.Error("error", err))
				return err
			}

			resource.LoggerTask.Notice(ctx, "vm has resized")
		}

		return nil
	}
}

func ProcessResizeVM(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster
	find := false

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusModifying {
				targetNode = node
				targetCluster = cluster
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target node", logit.String("nodeID", param.TargetNodeID))
		return errors.New("can not find target node")
	}

	if targetCluster.DestSpec == targetCluster.Spec {
		resource.LoggerTask.Notice(ctx, "target cluster spec not change", logit.String("clusterID", targetCluster.ClusterID))
		return nil
	}

	err = resizeNode(ctx, app, targetCluster, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "resize node error", logit.String("node", base_utils.Format(targetNode)), logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessStandaloneResizeVM(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			targetNode = node
			targetCluster = cluster
			break OuterLoop
		}
	}

	if targetCluster.DestSpec == targetCluster.Spec {
		resource.LoggerTask.Notice(ctx, "target cluster spec not change", logit.String("clusterID", targetCluster.ClusterID))
		return nil
	}

	err = resizeNode(ctx, app, targetCluster, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "resize node error", logit.String("node", base_utils.Format(targetNode)), logit.Error("error", err))
		return err
	}

	return nil
}
