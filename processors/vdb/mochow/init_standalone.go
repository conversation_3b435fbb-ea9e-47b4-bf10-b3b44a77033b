/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessInitStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	//设置初始密码
	err = setRootPasswordForStandalone(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set root password error", logit.Error("error", err))
		return err
	}

	return nil
}

func setRootPasswordForStandalone(ctx context.Context, app *vdbmodel.Application, status string) error {
	userAuth, err := vdbmodel.UserAuthGetInUse(ctx, app.AppID, "root")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get applications user password fail", logit.Error("err", err))
		return err
	}

	password, err := crypto_utils.DecryptKey(userAuth.Password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "decrypt password failed", logit.Error("err", err))
		return fmt.Errorf("decrypt password failed: %s", err.Error())
	}

	var standaloneServer *vdbmodel.Node
outer:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == status {
				standaloneServer = node
				break outer
			}
		}
	}

	if standaloneServer == nil {
		resource.LoggerTask.Warning(ctx, "inuse node server is nil")
		return fmt.Errorf("node server is nil")
	}

	changePasswordReq := mochow.ChangePasswordRequest{
		UserName:    "root",
		NewPassword: password,
	}

	rsp, err := mochow.StandaloneChangePassword(ctx, standaloneServer, &changePasswordReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "call ChangePassword", logit.Error("error", err))
		return err
	}

	if rsp.Code != 0 {
		resource.LoggerTask.Error(ctx, "ChangePassword ret code failed", logit.Int("status_code", rsp.Code))
		return fmt.Errorf(rsp.Msg)
	}

	return nil
}
