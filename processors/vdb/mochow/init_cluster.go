/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessInitCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	master, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	//在master上注册datanodes
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			req := mochow.AddDataNodeRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(node.Port),
				},
				AZName: node.Azone,
			}
			rsp, err := mochow.AddDataNode(ctx, master, &req)
			if err != nil {
				resource.LoggerTask.Error(ctx, "add data node failed", logit.Error("error", err))
				return err
			}

			if rsp.Status.Code != 0 {
				if rsp.Status.Code == 8004 {
					resource.LoggerTask.Trace(ctx, "data node already exist", logit.Int32("status_code", rsp.Status.Code),
						logit.String("status_msg", rsp.Status.Msg))
				} else {
					resource.LoggerTask.Error(ctx, "add data node failed", logit.Int32("status_code", rsp.Status.Code),
						logit.String("status_msg", rsp.Status.Msg))
					return fmt.Errorf(rsp.Status.Msg)
				}
			}
		}
	}

	//设置初始密码
	err = setRootPasswordForCluster(ctx, app, vdbmodel.NodeOrProxyStatusToCreate)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set root password error", logit.Error("error", err))
		return err
	}

	return nil
}

func getMasterLeader(ctx context.Context, app *vdbmodel.Application, status string) (*vdbmodel.Master, error) {
	//初始化master,创建阶段随便找一个master作为server
	var masterServer *vdbmodel.Master

	if app.Type == vdbmodel.AppTypeStandalone {
		masterServer, err := getStandaloneMasterServer(ctx, app)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get standalone master server error", logit.Error("error", err))
			return nil, err
		}
		resource.LoggerTask.Trace(ctx, "get master leader", logit.String("master_ip", masterServer.IP), logit.Int32("port", int32(masterServer.Port)))
		return masterServer, nil
	}

	//find one master node
	for _, master := range app.Masters {
		if master.Status == status ||
			master.Status == vdbmodel.NodeOrProxyStatusToUpgrade ||
			master.Status == vdbmodel.NodeOrProxyStatusUpgrading ||
			master.Status == vdbmodel.NodeOrProxyStatusUpgradeSucc ||
			master.Status == vdbmodel.NodeOrProxyStatusToModify ||
			master.Status == vdbmodel.NodeOrProxyStatusModifying ||
			master.Status == vdbmodel.NodeOrProxyStatusModifySucc {
			masterServer = master
			break
		}
	}

	if masterServer == nil {
		resource.LoggerTask.Warning(ctx, "master node is nil")
		return nil, fmt.Errorf("master node is nil")
	}

	resource.LoggerTask.Trace(ctx, "get one master", logit.String("master_ip", masterServer.IP), logit.Int32("port", int32(masterServer.Port)))

	req := mochow.ListDataNodeRequest{
		Token: "default_token",
	}

	rsp, err := mochow.ListDataNode(ctx, masterServer, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list data node failed", logit.Error("error", err))
		return nil, err
	}

	resource.LoggerTask.Trace(ctx, "list datanode", logit.String("rsp :", base_utils.Format(rsp)))

	if rsp.Status.Code == 0 {
		resource.LoggerTask.Trace(ctx, "get master leader", logit.String("master_ip", masterServer.IP), logit.Int32("port", int32(masterServer.Port)))
		return masterServer, nil
	}

	if rsp.Status.Code == 8008 && rsp.Status.Msg == "Not Leader" {
		leadIPInt := rsp.Status.LeaderHint.IP
		leadPort := rsp.Status.LeaderHint.Port
		for _, master := range app.Masters {
			resource.LoggerTask.Trace(ctx, "traver master", logit.String("master_ip", master.IP), logit.Int32("master_port", int32(master.Port)),
				logit.String("lead_ip", util.LittleEndianInetNtoA(int64(leadIPInt))))
			if master.IP == util.LittleEndianInetNtoA(int64(leadIPInt)) && master.Port == int(leadPort) {
				masterServer = master
				resource.LoggerTask.Trace(ctx, "get master leader", logit.String("master_ip", masterServer.IP), logit.Int32("port", int32(masterServer.Port)))
				return masterServer, nil
			}
		}
	}

	resource.LoggerTask.Error(ctx, "get master leader failed", logit.Int32("status_code", rsp.Status.Code))
	return nil, fmt.Errorf("get master leader failed")
}

func getStandaloneMasterServer(ctx context.Context, app *vdbmodel.Application) (*vdbmodel.Master, error) {
	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Warning(ctx, "not standalone type application")
		return nil, fmt.Errorf("not standalone type application")
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			return &vdbmodel.Master{
				IP:         node.IP,
				FloatingIP: node.FloatingIP,
				Port:       int(node.Port + 1),
			}, nil

		}
	}

	return nil, fmt.Errorf("not found standalone master")
}

func setRootPasswordForCluster(ctx context.Context, app *vdbmodel.Application, status string) error {
	userAuth, err := vdbmodel.UserAuthGetInUse(ctx, app.AppID, "root")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get applications user password fail", logit.Error("err", err))
		return err
	}

	password, err := crypto_utils.DecryptKey(userAuth.Password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "decrypt password failed", logit.Error("err", err))
		return fmt.Errorf("decrypt password failed: %s", err.Error())
	}

	var proxyServer *vdbmodel.Proxy
outer:
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == status {
				proxyServer = proxy
				break outer
			}
		}
	}

	if proxyServer == nil {
		resource.LoggerTask.Warning(ctx, "inuse proxy node is nil")
		return fmt.Errorf("proxy node is nil")
	}

	changePasswordReq := mochow.ChangePasswordRequest{
		UserName:    "root",
		NewPassword: password,
	}

	rsp, err := mochow.ProxyChangePassword(ctx, proxyServer, &changePasswordReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "call ProxyChangePassword", logit.Error("error", err))
		return err
	}

	if rsp.Code != 0 {
		resource.LoggerTask.Error(ctx, "ProxyChangePassword ret code failed", logit.Int("status_code", rsp.Code))
		return fmt.Errorf(rsp.Msg)
	}

	return nil
}
