#!/bin/bash
cd $(dirname $0)
cd ../
WORK_DIR=$(pwd)

[[ -z $1 ]] || sed -i  "s/^IDC = .*/IDC = \"${1}\"/g" ${WORK_DIR}/conf/app.toml

if [[ "$1" == "dbstack"* ]]; then
    current_ip=$(hostname -I | awk '{print $1}')
    if [ -z "$current_ip" ]; then
        echo "Failed to retrieve current IP address."
        exit 1
    fi

    sed -i "s/127.0.0.1/$current_ip/g" ${WORK_DIR}/conf/component/pkg_man.toml
    sed -i "s/127.0.0.1/$current_ip/g" ${WORK_DIR}/conf/vdb/xmaster_ha.toml
fi

trap 'echo signal received!; kill $(jobs -p); wait;' SIGINT SIGTERM SIGUSR2 SIGQUIT

${WORK_DIR}/bin/x1-task-manager &

wait
