[Image]
Bcc = "5540395b-4285-4c4f-a2ff-a0dfa3db6e52"
DockerImage = "registry.baidubce.com/scs_test_cce/vdb-binary-centos6u3-xagent-dbstack:2024071303"

[PkgConf]
AgentBins = ["slot-redis", "redis", "PegaDB2", "proxy-slot", "nutcraker"]
NeedExcutePkgs = ["agent", "monitor-agent", "cron", "mochow-master","mochow-proxy","mochow-datanode","mochow-standalone"]
CorePkgs = ["slot-redis", "redis", "PegaDB2", "proxy-slot", "nutcraker", "sync-agent"]
