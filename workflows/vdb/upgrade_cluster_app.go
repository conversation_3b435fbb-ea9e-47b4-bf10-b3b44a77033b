/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* upgrade_cluster_app.go - the controller of upgrade cluster app  */
/*
modification history
--------------------
2024/09/2, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
执行节点升级

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"UpgradeParam":  ..
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/upgrade"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowUpgradeCluster                    = "vdb-upgrade-cluster-app"
	StepUpgradeClusterBuildMeta               = "vdb-upgrade-cluster-app-build-meta"
	StepUpgradeClusterCloseHa                 = "vdb-upgrade-cluster-app-close-ha"
	StepUpgradeClusterUpgradeMasterSubTasks   = "vdb-upgrade-cluster-upgrade-master-sub-tasks"
	StepUpgradeClusterUpgradeDatanodeSubTasks = "vdb-upgrade-cluster-upgrade-datanode-sub-tasks"
	StepUpgradeClusterUpgradeProxySubTasks    = "vdb-upgrade-cluster-upgrade-proxy-sub-tasks"
	StepUpgradeClusterRecoverHa               = "vdb-upgrade-cluster-app-recover-ha"
	StepUpgradeClusterCallback                = "vdb-upgrade-cluster-cb"

	StepUpgradeClusterRollback          = "vdb-upgrade-cluster-app-rollback"
	StepUpgradeClusterRollbackRecoverHa = "vdb-upgrade-cluster-app-rollback-recover-ha"

	WorkflowUpgradeNode      = "vdb-upgrade-node"
	StepUpgradeNodeBuildMeta = "vdb-upgrade-node-build-meta"
	StepUpgradeNodeUpgrade   = "vdb-upgrade-node-upgrade"
	StepUpgradeNodeCheck     = "vdb-upgrade-node-check"
	StepUpgradeNodeCallback  = "vdb-upgrade-node-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// Step-0 构建元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterBuildMeta,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForUpgrade,
		SuccessNextStep: StepUpgradeClusterCloseHa,
		ErrorNextStep:   StepUpgradeClusterBuildMeta,
	})

	// Step-1 关闭HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterCloseHa,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     xmaster.ProcessCloseAppHaStatusInXmaster,
		SuccessNextStep: StepUpgradeClusterUpgradeMasterSubTasks,
		ErrorNextStep:   StepUpgradeClusterCloseHa,
	})

	// Step-2 执行master节点的升级
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterUpgradeMasterSubTasks,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     upgrade.ProcessUpgradeAllMaster,
		SuccessNextStep: StepUpgradeClusterUpgradeDatanodeSubTasks,
		ErrorNextStep:   StepUpgradeClusterUpgradeMasterSubTasks},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 执行datanode节点的升级
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterUpgradeDatanodeSubTasks,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     upgrade.ProcessUpgradeAllDatanode,
		SuccessNextStep: StepUpgradeClusterUpgradeProxySubTasks,
		ErrorNextStep:   StepUpgradeClusterUpgradeDatanodeSubTasks},

		workflow.WithStepTimeout(24*time.Hour))

	// Step-4 执行proxy节点的升级
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterUpgradeProxySubTasks,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     upgrade.ProcessUpgradeAllProxy,
		SuccessNextStep: StepUpgradeClusterRecoverHa,
		ErrorNextStep:   StepUpgradeClusterUpgradeProxySubTasks},

		workflow.WithStepTimeout(24*time.Hour))

	// Step-5 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterRecoverHa,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: StepUpgradeClusterCallback,
		ErrorNextStep:   StepUpgradeClusterRecoverHa,
	})

	// Step-6 更新成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterCallback,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     callback.ProcessUpgradeSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpgradeClusterCallback,
	})

	/////////////////////////////////////////////////////////
	// errStep-1 恢复集群和节点状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterRollback,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     callback.ProcessUpgradeRollbackMeta,
		SuccessNextStep: StepUpgradeClusterRollbackRecoverHa,
		ErrorNextStep:   StepUpgradeClusterRollback},

		workflow.WithStepTimeout(15*time.Minute))

	// errStep-2 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterRollbackRecoverHa,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepUpgradeClusterRollbackRecoverHa,
	})

	//----------------一个节点的升级流程--------------------------------------------
	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeNodeBuildMeta,
		Workflow:        WorkflowUpgradeNode,
		StepProcess:     buildmeta.ProcessBuildMetaForUpgradeOneNode,
		SuccessNextStep: StepUpgradeNodeUpgrade,
		ErrorNextStep:   StepUpgradeNodeBuildMeta,
	})

	// Step-2 升级一个节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeNodeUpgrade,
		Workflow:        WorkflowUpgradeNode,
		StepProcess:     upgrade.ProcessUpgradeOneNode,
		SuccessNextStep: StepUpgradeNodeCheck,
		ErrorNextStep:   StepUpgradeNodeUpgrade,
	})

	//Step-3 检查状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeNodeCheck,
		Workflow:        WorkflowUpgradeNode,
		StepProcess:     mochow.ProcessCheckAllStatus,
		SuccessNextStep: StepUpgradeNodeCallback,
		ErrorNextStep:   StepUpgradeNodeCheck,
	}, workflow.WithStepTimeout(15*time.Minute))

	//Step-4 回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeNodeCallback,
		Workflow:        WorkflowUpgradeNode,
		StepProcess:     callback.ProcessUpgradeOneNodeSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpgradeNodeCallback,
	})
}
