package cds

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessDetachDataCds 卸载故障节点的数据盘cds资源
// 1. 获取todelete node，如果存在多个todelete node，则报错
// 2. 调用bcc接口查询cds是否挂载在bcc上，如果存在则卸载，如果不存在则直接返回
// 3. 调用bcc接口卸载cds
func ProcessDetachCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to detach cds in non-standalone mode")
		return nil
	}

	toDeleteNode, err := getToDeleteNode(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get todelete node error", logit.String("appId", app.AppID), logit.Error("error", err))
		return err
	}
	volumeID := toDeleteNode.VolumeID

	if volumeID == "" {
		resource.LoggerTask.Error(ctx, "no cds volume id founds", logit.String("nodeID", toDeleteNode.NodeID))
		return errors.New("no cds volume id found")
	}

	bccResourceID := toDeleteNode.ResourceID

	err = doDetachCds(ctx, bccResourceID, toDeleteNode.VolumeID, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "detach cds volume id failed", logit.Error("error", err))
		return err
	}

	return nil
}

// doDetachCds 调用bcc接口卸载cds
func doDetachCds(ctx context.Context, bccResourceID string, volumeID string, userID string) error {
	// check bcc cds info
	volumes, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
		VmID:      bccResourceID,
		UserID:    userID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cds info failed", logit.Error("error", err))
		return err
	}

	cdsAttached := false
	for _, volume := range volumes {
		if volume.Id == volumeID {
			cdsAttached = true
			break
		}
	}
	if !cdsAttached {
		resource.LoggerTask.Notice(ctx, "no need to detach cds due to cds is not attached", logit.String("volumeID", volumeID),
			logit.String("bccSourceID", bccResourceID))
		return nil
	}

	// detach cds
	err = bccresource.BccResourceOp().DetachCds(ctx, &bccresource.DetachCdsParam{
		VmID:      bccResourceID,
		VolumeID:  volumeID,
		UserID:    userID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "detach cds failed", logit.String("volumeID", volumeID),
			logit.String("bccSourceID", bccResourceID), logit.Error("error", err))
		return err
	}
	return nil
}
