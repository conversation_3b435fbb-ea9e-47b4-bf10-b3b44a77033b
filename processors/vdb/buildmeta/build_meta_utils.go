/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/component/deploy"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

const (
	PintTestTimeout        = 1
	MaxSelfHealDataNodeNum = 1
	MaxSelfHealMasterNum   = 1
)

func FindClusterByID(ctx context.Context, app *vdbmodel.Application, ID string) (*vdbmodel.Cluster, error) {
	for _, cluster := range app.Clusters {
		if cluster.ClusterID == ID {
			return cluster, nil
		}
	}
	return nil, fmt.Errorf("cannot find cluster by id %s", ID)
}

func FindNodeByID(ctx context.Context, cluster *vdbmodel.Cluster, ID string) (*vdbmodel.Node, error) {
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.NodeID == ID {
			return node, nil
		}
	}
	return nil, fmt.Errorf("cannot find node by id %s", ID)
}

func FindNodeByIDInApp(ctx context.Context, app *vdbmodel.Application, ID string) (*vdbmodel.Node, *vdbmodel.Cluster, error) {
	for _, cluster := range app.Clusters {
		node, err := FindNodeByID(ctx, cluster, ID)
		if err == nil {
			return node, cluster, nil
		}
	}
	return nil, nil, fmt.Errorf("cannot find node by id %s", ID)
}

func FindProxyByIDInApp(ctx context.Context, app *vdbmodel.Application, ID string) (*vdbmodel.Proxy, error) {
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.ProxyID == ID {
				return proxy, nil
			}
		}
	}
	return nil, fmt.Errorf("cannot find node by id %s", ID)
}

func FindMasterByIDInApp(ctx context.Context, app *vdbmodel.Application, ID string) (*vdbmodel.Master, error) {
	for _, master := range app.Masters {
		if master.MasterID == ID {
			return master, nil
		}
	}
	return nil, fmt.Errorf("cannot find master by id %s", ID)
}

func GetDeployConfVersion(ctx context.Context) (string, error) {
	deployClient := deploy.NewDefaultClient()
	ret, err := deployClient.GetServerDeployConf(ctx, &deploy.GetServerDeployConfRequest{
		Conf: &deploy.CommonServerConf{
			PackageTag: "xcache",
			Version:    "",
			WorkDir:    "/home/<USER>",
			PORT:       6379,
			EnvVars:    nil,
		},
	})
	if err != nil {
		return "", err
	}
	return ret.Package.Version, nil
}
