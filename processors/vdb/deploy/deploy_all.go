package deploy

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/getvars"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/render"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/pkg_manager"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/kms"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/tls"
)

const (
	AgentDeployDir                      = "/home/<USER>"
	SamePkgVersionServerIDUseAppVersion = "APP"
)

var (
	DefaultPkgs         = []string{"agent", "monitor-agent", "xagent", "opbin"}
	CoreBin             = []string{MochowMasterPkg, MochowProxyPkg, MochowDataNodePkg, MochowStandalonePkg}
	MochowMasterPkg     = "mochow-master"
	MochowProxyPkg      = "mochow-proxy"
	MochowDataNodePkg   = "mochow-datanode"
	MochowStandalonePkg = "mochow-standalone"
)

type DeployNodeOfAllTypeParams struct {
	AppID                  string
	ClusterID              string
	ServerID               string
	MaxDiskSize            int64
	XmasterEndPoint        string
	EngineType             string
	Kernel                 string
	KernelMajorVersion     string
	AppType                string
	BaseDir                string
	XagentHost             string
	XagentPort             int
	PackageVersion         string
	NodeIP                 string
	NodePort               int
	TaskID                 string
	IsUpgrade              bool
	ForceRestart           bool
	SamePkgVersionServerID string // 用来标注需要跟这个节点一致，也可以是 "APP" 用来标注是app维度最低版本
	AllSameTypeServerIDs   []string
	NodeFixID              string
	UserID                 string
	VpcID                  string
	UseNewAgent            bool
	Entity                 string
	MasterAddrs            string
	EnableTLS              string
	CertData               string
	CertPrivateKey         string
	EnableEnCryption       string
	EnCryptionAlgoName     string
	EnCryptionKey          string
	TabletLeaderPreferAz   string
	NoUpgradeConf          string
}

type DeployRequestNew struct {
	PkgsToInstall []*PkgToInstall        `json:"pkgs_to_install"`
	Meta          *xagent.Meta           `json:"meta"`
	WorkDir       string                 `json:"work_dir"`
	EnvVars       any                    `json:"env_vars"`
	ForceRestart  bool                   `json:"force_restart"`
	IsUpgrade     bool                   `json:"is_upgrade"`
	UseNewAgent   bool                   `json:"use_new_agent"`
	NoUpgradeConf string                 `json:"no_upgrade_conf"`
	AgentRecovers *getvars.AgentRecovers `json:"agent_recovers"`
}

type PkgToInstall struct {
	DownloadURL   string                   `json:"download_url"`
	MD5           string                   `json:"md5"`
	Name          string                   `json:"name"`
	Version       string                   `json:"version"`
	DeployPath    string                   `json:"deploy_path"`
	NoNeedExecute bool                     `json:"no_need_execute"`
	RenderedConfs []*render.RenderedConfig `json:"rendered_confs"`
}

func DeployNodeOfAllTypeNew(ctx context.Context, params *DeployNodeOfAllTypeParams) error {
	params.ForceRestart = true
	pkgs, err := GetToDeployPkgs(ctx, params)
	if err != nil {
		return err
	}
	xagentReq, err := GetXagentRequest(ctx, params, pkgs)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get xagent request failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "package manager xagent req", logit.String("req", base_utils.Format(xagentReq)))
	aCtx := xagent.Instance().DoAsync(ctx, xagentReq)
	resp, err := aCtx.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy node failed", logit.Error("err", err),
			logit.String("resp", base_utils.Format(resp)), logit.String("xagentreq", base_utils.Format(xagentReq)))
		return err
	}
	resource.LoggerTask.Trace(ctx, "deploy node success", logit.String("resp", base_utils.Format(resp)))
	return nil
}

func GetXagentRequest(ctx context.Context, params *DeployNodeOfAllTypeParams, pkgs []*vdbmodel.Package) (*xagent.AsyncRequest, error) {
	var err error
	dReq := &DeployRequestNew{
		WorkDir:      AgentDeployDir,
		EnvVars:      getEnvVars(params),
		ForceRestart: params.ForceRestart,
		Meta: &xagent.Meta{
			Engine:        params.Kernel,
			EngineVersion: params.KernelMajorVersion,
			Basedir:       params.BaseDir,
			Port:          int32(params.NodePort),
			AccountName:   "",
			Password:      "",
		},
		UseNewAgent:   params.UseNewAgent,
		IsUpgrade:     params.IsUpgrade,
		NoUpgradeConf: params.NoUpgradeConf,
	}
	var getVarsParams *getvars.Parameter

	getVarsParams, err = getvars.GetParamters(ctx, params.AppID, params.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get getvars params failed", logit.Error("err", err))
		return nil, err
	}

	for _, pkg := range pkgs {
		// resource.LoggerTask.Trace(ctx, "get pkg info success", logit.String("pkg", base_utils.Format(pkg)))
		bosURL := pkg_manager.GetDownloadUrl(fmt.Sprintf("%s_%s.tar.gz", pkg.Name, pkg.FullVersion), 3600)
		resource.LoggerTask.Trace(context.Background(), "get bos url success", logit.String("bosURL", bosURL))
		var renderedConfs []*render.RenderedConfig
		if params.UseNewAgent && conf.IsCorePkg(pkg.Name) {
			renderedConfs, err = render.GetRenderdConf(ctx, getVarsParams, "", pkg.FullVersion)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get rendered conf failed",
					logit.Error("err", err), logit.String("pkg", base_utils.Format(pkg)))
				return nil, err
			}
		}
		dReq.PkgsToInstall = append(dReq.PkgsToInstall, &PkgToInstall{
			DownloadURL: bosURL,
			MD5:         pkg.MD5,
			Name:        pkg.Name,
			Version:     pkg.FullVersion,
			DeployPath: func() string {
				if conf.IsAgentsBin(pkg.Name) {
					return dReq.WorkDir + "/agentbin"
				}
				return dReq.WorkDir
			}(),
			NoNeedExecute: func() bool {
				return !conf.IsNeedExuctePkg(pkg.Name)
			}(),
			RenderedConfs: renderedConfs,
		})
	}

	dReq.AgentRecovers, err = getvars.GetAgentRecover(ctx, getVarsParams)
	if err != nil {
		return nil, err
	}

	xagentReq := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: params.XagentHost,
			Port: int32(params.XagentPort),
		},
		Action:     getAction(params),
		Params:     dReq,
		TimeoutSec: 180,
		Product:    vdbmodel.ProductVDB,
	}
	return xagentReq, nil
}

func GetToDeployPkgs(ctx context.Context, params *DeployNodeOfAllTypeParams) ([]*vdbmodel.Package, error) {
	var toDeployPkgInfos []*vdbmodel.Package
	var err error
	// 获取这个节点需要的包名字和版本过滤条件。
	pkgsRequire, versionFilter := vdbmodel.GetRequirePkgNameAndVersionFilter(params.EngineType,
		params.KernelMajorVersion, params.AppType)

	resource.LoggerTask.Trace(ctx, "get pkg name list success", logit.String("toDeployPkgInfos", base_utils.Format(pkgsRequire)),
		logit.String("versionFilter", base_utils.Format(versionFilter)))

	if params.SamePkgVersionServerID != "" {
		// 如果是版本固定，需要找到参考节点，要么是一个具体节点，要么是APPID
		referEntity := params.SamePkgVersionServerID
		if params.SamePkgVersionServerID == SamePkgVersionServerIDUseAppVersion {
			// 如果是APP维度的，则用appid作为参考值
			referEntity = params.AppID
		}
		resource.LoggerTask.Trace(ctx, "this is refer deploy task", logit.String("params", base_utils.Format(params)),
			logit.String("refer entity", referEntity))
		toDeployPkgInfos, err = vdbmodel.GetCurrentPackageRecord(ctx, referEntity, pkgsRequire, "")
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err))
			return nil, err
		}

	} else {
		// 不需要版本固定，是一个全新的节点，一般用于升级到最新版或全新部署
		resource.LoggerTask.Trace(ctx, "this is simple deploy task", logit.String("params", base_utils.Format(params)))
		toDeployPkgInfos, err = vdbmodel.GetLatestPackages(ctx, pkgsRequire, versionFilter)
		// 替换为灰度版本包（内部判断必要性）
		toDeployPkgInfos = util.MergeGreyBoxPkgs(ctx, &util.ParmasGetGreyBoxFilter{
			ServerID:    params.ServerID,
			AppID:       params.AppID,
			UserID:      params.UserID,
			VpcID:       params.VpcID,
			UseNewAgent: params.UseNewAgent,
		}, toDeployPkgInfos)

		if err != nil {
			resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err))
			return nil, err
		}
	}

	if !checkIsAllPkgGeted(ctx, toDeployPkgInfos, pkgsRequire, versionFilter) {
		return nil, errors.New("get toDeployPkgInfos fail")
	}

	// 维护这个节点的版本记录
	if err := vdbmodel.UpgradeEntityPackegeRecord(ctx, params.ServerID, pkgsRequire, toDeployPkgInfos, "task-"+params.TaskID); err != nil {
		return nil, err
	}
	if err := vdbmodel.UpgradeEntityPackegeRecord(ctx, params.AppID, pkgsRequire, toDeployPkgInfos, "task-"+params.TaskID); err != nil {
		return nil, err
	}

	resource.LoggerTask.Trace(ctx, "get package info and upgrade pkg record success", logit.String("toDeployPkgInfos", base_utils.Format(toDeployPkgInfos)))

	return toDeployPkgInfos, err
}

func checkIsAllPkgGeted(ctx context.Context, getedPkgs []*vdbmodel.Package, requiredPkgs []string, versionFilter map[string]string) bool {
	if len(getedPkgs) != len(requiredPkgs) {
		resource.LoggerTask.Warning(ctx, "package records not match require",
			logit.String("pkgs", base_utils.Format(getedPkgs)), logit.String("pkgs_require", base_utils.Format(requiredPkgs)))
		return false
	}
	for _, p := range requiredPkgs {
		get := false
		for _, toDeployPkgs := range getedPkgs {
			if toDeployPkgs.Name == p {
				get = true
				if version, ok := versionFilter[toDeployPkgs.Name]; ok {
					if version != toDeployPkgs.MajorVersion {
						resource.LoggerTask.Warning(ctx, "major version not match",
							logit.String("geted", base_utils.Format(toDeployPkgs)),
							logit.String("require version", version))
						return false
					}
				}
				break
			}
		}
		if !get {
			return false
		}
	}
	return true
}

func getEnvVars(params *DeployNodeOfAllTypeParams) map[string]any {
	envVars := map[string]any{
		"APP_ID":                    params.AppID,
		"APP_TYPE":                  params.AppType,
		"CLUSTER_ID":                params.ClusterID,
		"SERVER_ID":                 params.ServerID,
		"MAX_SPACE":                 params.MaxDiskSize,
		"XMASTER_ENDPOINT":          params.XmasterEndPoint,
		InstanceEngineKey:           params.EngineType,
		"MASTER_ADDRS":              params.MasterAddrs,
		"NODE_PORT":                 params.NodePort,
		"ENABLE_TLS":                params.EnableTLS,
		"CERT_DATA":                 params.CertData,
		"CERT_PRIVATE_KEY":          params.CertPrivateKey,
		"ENABLE_ENCRYPTION":         params.EnableEnCryption,
		"ENCRYPTION_ALGORITHM_NAME": params.EnCryptionAlgoName,
		"ENCRYPTION_KEY":            params.EnCryptionKey,
		"TABLET_LEADER_PREFER_AZ":   params.TabletLeaderPreferAz,
	}
	staticEnv := resource.PackageConf.GetStaticEnvVars()
	for k, v := range staticEnv {
		envVars[k] = v
	}

	envVars["xagent.enable_logic_inspection"] = "false"

	return envVars
}

func getAction(params *DeployNodeOfAllTypeParams) string {
	return "package_install"
}

func CommonGetPackageInfos(engineType string, kernelMajorVersion string, appType string, needSyncAgent bool,
	nonbin bool, needSmartDba bool) ([]string, map[string]string) {
	requirePkgs, versionFilter := vdbmodel.GetRequirePkgNameAndVersionFilter(engineType,
		kernelMajorVersion, appType)

	var retPkgs []string
	var retVersionFilter = make(map[string]string, 0)
	if nonbin {
		for _, pkg := range requirePkgs {
			isCoreBin, _ := base_utils.InArray(pkg, CoreBin)
			if !isCoreBin {
				retPkgs = append(retPkgs, pkg)
				if v, ok := versionFilter[pkg]; ok {
					retVersionFilter[pkg] = v
				}
			}
		}
	} else {
		return requirePkgs, versionFilter
	}

	return retPkgs, retVersionFilter
}

func GetNodeDeployParams(app *vdbmodel.Application, cluster *vdbmodel.Cluster, node *vdbmodel.Node,
	TDE *vdbmodel.Tde) *DeployNodeOfAllTypeParams {
	ret := &DeployNodeOfAllTypeParams{
		AppID:              app.AppID,
		ClusterID:          cluster.ClusterID,
		ServerID:           node.NodeID,
		MaxDiskSize:        cluster.DiskSize,
		XmasterEndPoint:    util.GetXmasterEndpoint(),
		EngineType:         cluster.Engine,
		Kernel:             cluster.Engine,
		KernelMajorVersion: cluster.EngineVersion,
		AppType:            app.Type,
		BaseDir:            node.Basedir,
		XagentHost:         node.FloatingIP,
		XagentPort:         node.XagentPort,
		PackageVersion:     util.GetVersion(app),
		NodeIP:             node.FloatingIP,
		NodePort:           node.Port,
		NodeFixID:          node.NodeFixID,
		UserID:             app.UserID,
		VpcID:              app.VpcID,
		UseNewAgent:        true,
		Entity:             node.NodeID,
	}
	if len(ret.BaseDir) == 0 {
		ret.BaseDir = "/home/<USER>"
	}

	if TDE != nil {
		ret.EnCryptionAlgoName = TDE.EncryptionAlgorithm
		ret.EnCryptionKey = TDE.TdeKey
	}
	return ret
}

func GetProxyDeployParams(ctx context.Context, app *vdbmodel.Application, itf *vdbmodel.Interface,
	proxy *vdbmodel.Proxy) *DeployNodeOfAllTypeParams {
	ret := &DeployNodeOfAllTypeParams{
		AppID:              app.AppID,
		ClusterID:          app.AppID,
		ServerID:           proxy.ProxyID,
		MaxDiskSize:        itf.DiskSize,
		XmasterEndPoint:    util.GetXmasterEndpoint(),
		EngineType:         InstanceEngineValueProxy,
		Kernel:             itf.Engine,
		KernelMajorVersion: itf.EngineVersion,
		AppType:            app.Type,
		BaseDir:            proxy.Basedir,
		XagentHost:         proxy.FloatingIP,
		XagentPort:         proxy.XagentPort,
		PackageVersion:     util.GetVersion(app),
		NodeIP:             proxy.FloatingIP,
		NodePort:           proxy.Port,
		UserID:             app.UserID,
		VpcID:              app.VpcID,
		UseNewAgent:        true,
		Entity:             proxy.ProxyID,
	}
	if len(ret.BaseDir) == 0 {
		ret.BaseDir = "/home/<USER>"
	}

	ret.EnableTLS = app.EnableTLS

	if ret.EnableTLS == "enable" {
		cert, err := vdbmodel.CertificateServerCAGetByAppID(ctx, app.AppID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get certificate error", logit.String("app_id", app.AppID))
		} else if cert == nil {
			resource.LoggerTask.Error(ctx, "certificate not found", logit.String("app_id", app.AppID))
		} else {
			pkPem, err := tls.DecryptAES(cert.CertPrivateKey, conf.TLSConfIns.EncryptPass)
			if err != nil {
				resource.LoggerTask.Error(ctx, "decrypt cert private key error", logit.String("app_id", app.AppID))
			}
			ret.CertData = cert.CertData
			ret.CertPrivateKey = pkPem
		}
	}

	return ret
}

func getFirstZoneName(app *vdbmodel.Application) string {
	if strings.Contains(app.Azone, "+") {
		return strings.Split(app.Azone, "+")[0]
	}
	return app.Azone
}

func GetMasterDeployParams(ctx context.Context, app *vdbmodel.Application, master *vdbmodel.Master,
	TDE *vdbmodel.Tde) *DeployNodeOfAllTypeParams {
	ret := &DeployNodeOfAllTypeParams{
		AppID:              app.AppID,
		ClusterID:          app.AppID,
		ServerID:           master.MasterID,
		MaxDiskSize:        master.DiskSize,
		XmasterEndPoint:    util.GetXmasterEndpoint(),
		EngineType:         InstanceEngineValueMaster,
		Kernel:             master.Engine,
		KernelMajorVersion: master.EngineVersion,
		AppType:            app.Type,
		BaseDir:            master.Basedir,
		XagentHost:         master.FloatingIP,
		XagentPort:         master.XagentPort,
		PackageVersion:     util.GetVersion(app),
		NodeIP:             master.FloatingIP,
		NodePort:           master.Port,
		UserID:             app.UserID,
		VpcID:              app.VpcID,
		UseNewAgent:        true,
		Entity:             master.MasterID,
	}
	if len(ret.BaseDir) == 0 {
		ret.BaseDir = "/home/<USER>"
	}

	if TDE != nil {
		ret.EnableEnCryption = "true"
	} else {
		ret.EnableEnCryption = "false"
	}

	firstZone := getFirstZoneName(app)
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get zone map failed", logit.Error("error", err))
		ret.TabletLeaderPreferAz = firstZone
		return ret
	}

	azone, found := zoneMapper(firstZone, true)
	if !found {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", firstZone))
		ret.TabletLeaderPreferAz = firstZone
	} else {
		ret.TabletLeaderPreferAz = azone
	}
	return ret
}

func GetTdeInfo(ctx context.Context, app *vdbmodel.Application) (*vdbmodel.Tde, error) {
	// 获取集群tde信息
	TDEs, err := vdbmodel.TDEGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get tde error", logit.Error("error", err))
		return nil, err
	}

	if len(TDEs) > 1 {
		// TDE配置数量不符合预期
		resource.LoggerTask.Warning(ctx, "len(TDEs) > 1", logit.String("appId", app.AppID))
		return nil, errors.New("tde config error")
	}

	if len(TDEs) == 0 {
		return nil, nil
	}
	TDE := TDEs[0]

	decryptParams := kms.DecryptKeyParams{
		UserID:      app.UserID,
		MasterKeyID: TDE.MasterKeyID,
		TdeKey:      TDE.TdeKey,
	}

	tdeKey, err := kms.Instance().DecryptKey(ctx, &decryptParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "decrypt key failed.", logit.Error("err", err))
		return nil, err
	}
	TDE.TdeKey = tdeKey

	return TDE, nil
}

func GetEachEntityDeployParams(ctx context.Context, app *vdbmodel.Application, isforce bool) (map[string]*DeployNodeOfAllTypeParams, error) {
	ret := make(map[string]*DeployNodeOfAllTypeParams)
	// 存储 NodeFixID => serverID 的map
	fakeDeleteNodeFixIdMap := make(map[string]string, 0)
	TDE, err := GetTdeInfo(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get tde error", logit.Error("error", err))
		return nil, err
	}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				fakeDeleteNodeFixIdMap[node.NodeFixID] = node.NodeID
			}
			if !isforce && node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}

			ret[node.NodeID] = GetNodeDeployParams(app, cluster, node, TDE)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				fakeDeleteNodeFixIdMap[proxy.NodeFixID] = proxy.ProxyID
			}
			if !isforce && proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}

			ret[proxy.ProxyID] = GetProxyDeployParams(ctx, app, itf, proxy)
		}
	}
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
			fakeDeleteNodeFixIdMap[master.NodeFixID] = master.MasterID
		}
		if !isforce && master.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}

		ret[master.MasterID] = GetMasterDeployParams(ctx, app, master, TDE)

	}
	//	 如果tofakedelete状态的节点中有跟即将部署节点一样NodeFixID的节点，说明是继承来的，所以需要继承他的包版本
	for _, deployInfo := range ret {
		if deployInfo.SamePkgVersionServerID != "" {
			continue
		}
		if serverID, ok := fakeDeleteNodeFixIdMap[deployInfo.NodeFixID]; ok {
			resource.LoggerTask.Trace(ctx, "get the old tofake delete node", logit.String("server_id", serverID))
			deployInfo.SamePkgVersionServerID = serverID
		}
	}

	return ret, nil
}

func GetMasterAddrsList(ctx context.Context, app *vdbmodel.Application) string {
	masterAddrs := "list://"

	if app.Type == vdbmodel.AppTypeStandalone {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				masterAddr := fmt.Sprintf("%s:%d", "127.0.0.1", node.Port+1) // 单节点模式下，master服务端口为proxy服务端口+1
				masterAddrs += masterAddr
				return masterAddrs
			}
		}
	}

	// 分布式模式下，获取所有master的IP和端口
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToDelete ||
			master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
			continue
		}

		masterAddr := fmt.Sprintf("%s:%d", master.IP, master.Port)
		masterAddrs += masterAddr + ","
	}

	//strings.TrimSuffix(masterAddrs, ",")
	len := len(masterAddrs)
	if masterAddrs[len-1] == ',' {
		masterAddrs = masterAddrs[:len-1]
	}
	resource.LoggerTask.Trace(ctx, "masterAddrs", logit.String("masterAddrs", masterAddrs))

	return masterAddrs
}

func ProcessDeployAllForNewCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	err = deployall(ctx, teu.TaskID, app, false)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deployall fail", logit.String("appId", teu.Entity),
			logit.Error("Error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

// ProcessDeployAll 用来给变配任务用，这个方法会使用整个APP中最老的版本部署所有包
func ProcessDeployAll(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return deployall(ctx, teu.TaskID, app, true)
}

// needFixVersion : 是否需要版本固定，如果false就会默认用所有包的最新版，true的话会根据是否有继承的节点来判断用什么版本
func deployall(ctx context.Context, taskID string, app *vdbmodel.Application, needFixVersion bool) error {
	deployParamMap, err := GetEachEntityDeployParams(ctx, app, false)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deploy params fail", logit.String("appId", app.AppID))
		return err
	}

	masterAddrs := GetMasterAddrsList(ctx, app)
	g := gtask.Group{Concurrent: 200}
	for serverId, params := range deployParamMap {
		serverId := serverId
		params := params
		params.TaskID = taskID
		params.MasterAddrs = masterAddrs
		if needFixVersion {
			if params.SamePkgVersionServerID == "" {
				params.SamePkgVersionServerID = SamePkgVersionServerIDUseAppVersion
			} else {
				resource.LoggerTask.Trace(ctx, "use app ver , but this node has old node, skip", logit.String("params", base_utils.Format(params)))
			}
		}
		g.Go(func() error {
			var deployErr error
			start := time.Now()
			resource.LoggerTask.Trace(ctx, "begin to deploy", logit.String("nodeId", serverId), logit.Time("start", start))

			deployErr = DeployNodeOfAllTypeNew(ctx, params)

			end := time.Now()
			resource.LoggerTask.Trace(ctx, "end to deploy", logit.String("nodeId", serverId),
				logit.Time("start", start), logit.Time("end", end), logit.Duration("cost", end.Sub(start)), logit.Error("err", deployErr))
			return deployErr
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy failed", logit.Error("err", err))
		return err
	}

	return nil
}

func IsDefaultPkg(pkgName string, defaultPkgs []string) bool {
	for _, pkg := range defaultPkgs {
		if pkg == pkgName {
			return true
		}
	}
	return false
}

func GetToUpgradePkgs(ctx context.Context, deployParams *DeployNodeOfAllTypeParams,
	upgradeParam *iface.UpgradeParam) ([]*vdbmodel.Package, error) {
	var toDeployPkgInfos []*vdbmodel.Package

	pkgName := upgradeParam.PkgName
	pkgID := upgradeParam.PkgID

	if upgradeParam.Type == vdbmodel.UpgradeTypeApp && !IsDefaultPkg(pkgName, DefaultPkgs) {
		if pkgName != "mochow" {
			return nil, errors.New("only default pkg can be upgrade")
		}

		pkgFirstName := "mochow"
		pkgMidName := deployParams.EngineType
		if deployParams.AppType == vdbmodel.AppTypeStandalone {
			pkgMidName = "standalone"
		}

		pkgLastName := upgradeParam.Version

		pkgName = pkgFirstName + "-" + pkgMidName
		pkgID = pkgFirstName + "-" + pkgMidName + "-" + pkgLastName
	}

	destPkg, err := vdbmodel.PkgGetByPkgCond(ctx, "name = ? and package_id = ?", pkgName, pkgID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err),
			logit.String("pkgName", pkgName), logit.String("pkgID", pkgID))
		return nil, err
	}

	toDeployPkgInfos = append(toDeployPkgInfos, destPkg)

	var pkgsRequire []string
	pkgsRequire = append(pkgsRequire, pkgName)

	// 维护这个节点的版本记录
	if err := vdbmodel.UpgradeEntityPackegeRecord(ctx, deployParams.ServerID, pkgsRequire, toDeployPkgInfos, "task-"+deployParams.TaskID); err != nil {
		resource.LoggerTask.Warning(ctx, "update app version record failed", logit.Error("err", err))
		return nil, err
	}
	if err := vdbmodel.UpgradeEntityPackegeRecord(ctx, deployParams.AppID, pkgsRequire, toDeployPkgInfos, "task-"+deployParams.TaskID); err != nil {
		resource.LoggerTask.Warning(ctx, "update app version record failed", logit.Error("err", err))
		return nil, err
	}

	resource.LoggerTask.Trace(ctx, "get upgrade pkg record success", logit.String("toDeployPkgInfos", base_utils.Format(toDeployPkgInfos)))

	return toDeployPkgInfos, err
}

func UpgradeNodeOfAllTypeNew(ctx context.Context, deployParams *DeployNodeOfAllTypeParams, upgradeParam *iface.UpgradeParam) error {
	deployParams.ForceRestart = true
	if upgradeParam.PkgName == "xagent" {
		deployParams.ForceRestart = false
	}
	pkgs, err := GetToUpgradePkgs(ctx, deployParams, upgradeParam)
	if err != nil {
		return err
	}
	xagentReq, err := GetXagentRequest(ctx, deployParams, pkgs)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get xagent request failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "package manager xagent req", logit.String("req", base_utils.Format(xagentReq)))
	aCtx := xagent.Instance().DoAsync(ctx, xagentReq)
	resp, err := aCtx.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy node failed", logit.Error("err", err),
			logit.String("resp", base_utils.Format(resp)), logit.String("xagentreq", base_utils.Format(xagentReq)))
		return err
	}
	resource.LoggerTask.Trace(ctx, "deploy node success", logit.String("resp", base_utils.Format(resp)))
	return nil
}
