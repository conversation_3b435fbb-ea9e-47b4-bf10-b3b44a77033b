/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessCheckDataNodeTablet(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeCluster {
		resource.LoggerTask.Notice(ctx, "only check datanode status in cluster mode")
		return nil
	}

	for {
		complete := true
		if err := checkDataNodeTablet(ctx, app); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "datanode tablet check failed", logit.Error("err", err))
		}
		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			continue
		}
	}
}

func checkDataNodeTablet(ctx context.Context, app *vdbmodel.Application) error {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	//对所有的datanode节点进行检查
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			port := node.Port
			if app.Type == vdbmodel.AppTypeStandalone {
				port = node.Port + 2
			}
			req := mochow.CheckDataNodeTabletPerfectionRequest{
				Token: "default_token",
				NodeAddr: mochow.NodeID{
					IP:   int32(util.LittleEndianInetAtoN(node.IP)),
					Port: int32(port),
				},
			}
			rsp, err := mochow.CheckDataNodeTabletPerfection(ctx, masterServer, &req)
			if err != nil {
				resource.LoggerTask.Error(ctx, "check data node tablet failed", logit.Error("error", err),
					logit.String("node_id :", base_utils.Format(node.NodeID)))
				return err
			}

			resource.LoggerTask.Trace(ctx, "check datanode tablet perfection", logit.String("rsp :", base_utils.Format(rsp)))

			if rsp.Status.Code != 0 {
				resource.LoggerTask.Error(ctx, "datanode tablet is not perfect", logit.Int32("status_code", rsp.Status.Code),
					logit.String("node_id :", base_utils.Format(node.NodeID)))
				return fmt.Errorf(rsp.Status.Msg)
			}
		}
	}

	return nil
}

func checkDatanodeStatusIsAlive(ctx context.Context, app *vdbmodel.Application) error {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return err
	}

	listReq := mochow.ListDataNodeRequest{
		Token: "default_token",
	}

	listRsp, err := mochow.ListDataNode(ctx, masterServer, &listReq)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list datanode failed", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "list datanode", logit.String("rsp :", base_utils.Format(listRsp)))

	if listRsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list datanode failed", logit.Int32("status_code", listRsp.Status.Code))
		return fmt.Errorf(listRsp.Status.Msg)
	}

	if len(listRsp.NodeList) == 0 {
		resource.LoggerTask.Error(ctx, "datanode list is empty", logit.String("rsp :", base_utils.Format(listRsp)))
		return fmt.Errorf("datanode list is empty")
	}

	for _, dn := range listRsp.NodeList {
		resource.LoggerTask.Trace(ctx, "check datanode", logit.String("datanode :", base_utils.Format(dn)))
		if !dn.IsAlive {
			resource.LoggerTask.Error(ctx, "datanode is not alive", logit.String("datanode_info", base_utils.Format(dn)))
			return fmt.Errorf("datanode is not alive")
		}
	}

	return nil
}
