/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/25 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file scs_conf.go
 * <AUTHOR>
 * @date 2022/07/25 15:16:56
 * @brief vdb conf
 *
 **/

package conf

import (
	"crypto/ecdsa"
	"crypto/tls"
	"crypto/x509"
	"log"

	"icode.baidu.com/baidu/gdp/env"
)

// VdbConf definiton
type VdbConf struct {
	IDCCODE        int64  `toml:"IdcCode"`
	PackageVersion string `toml:"PackageVersion"`
	NoahBns        string `toml:"NoahBns"`
	NoahEndpoint   string `toml:"NoahEndpoint"`
	AzEntrance     string `toml:"AzEntrance"`
}

// XmasterHAConf definiton
type XmasterHAConf struct {
	DefaultMonitorRules string `toml:"DefaultMonitorRules"`
	IdcName             string `toml:"IdcName"`
	XmasterEndpoint     string `toml:"XmasterEndpoint"`
}

type UserConf struct {
	UserID string
}

type NeedAllocVipBlbConf struct {
	SpecialUser []UserConf
}

type TLSConf struct {
	Country            string `toml:"Country"`
	Province           string `toml:"Province"`
	Locality           string `toml:"Locality"`
	Organization       string `toml:"Organization"`
	OrganizationalUnit string `toml:"OrganizationalUnit"`
	RootCAName         string `toml:"RootCAName"`
	InterCAName        string `toml:"InterCAName"`
	EncryptPass        string `toml:"EncryptPass"`
}

// ResizeConf definiton
type ResizeConf struct {
	AllowReplaceNodeWhenCdsResizeFails string `toml:"AllowReplaceNodeWhenCdsResizeFails"`
	ResizeTimeoutInSeconds             int    `toml:"ResizeTimeoutInSeconds"`
}

// VdbMainConf definition
var VdbMainConf *VdbConf
var XmasterHAConfIns *XmasterHAConf
var NeedAllocVipBlbConfIns *NeedAllocVipBlbConf
var TLSConfIns *TLSConf
var ResizeConfIns *ResizeConf
var RootCert *x509.Certificate
var RootPrivateKey *ecdsa.PrivateKey

// MustLoadScsConf retrun vdb main conf
func MustLoadVdbConf() error {
	certPath := env.ConfDir() + "/vdb/root.pem"
	privateKeyPath := env.ConfDir() + "/vdb/pkey.pem"
	cert, err := tls.LoadX509KeyPair(certPath, privateKeyPath)
	if err != nil {
		log.Fatal(err)
	}
	x509Cert, err := x509.ParseCertificate(cert.Certificate[0])
	if err != nil {
		return err
	}
	RootCert = x509Cert
	RootPrivateKey = cert.PrivateKey.(*ecdsa.PrivateKey)
	if err := LoadConf("vdb", "main", &VdbMainConf); err != nil {
		return err
	}

	if err := LoadConf("vdb", "xmaster_ha", &XmasterHAConfIns); err != nil {
		return err
	}

	if err := LoadConf("vdb", "need_alloc_vip_blb_user", &NeedAllocVipBlbConfIns); err != nil {
		return err
	}

	if err := LoadConf("vdb", "tls", &TLSConfIns); err != nil {
		return err
	}

	if err := LoadConf("vdb", "resize", &ResizeConfIns); err != nil {
		return err
	}

	return nil
}
