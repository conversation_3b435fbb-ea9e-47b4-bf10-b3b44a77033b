--port={{.port}}
--master_address=list://{{.master1_ip}}:{{.master1_port}},{{.master2_ip}}:{{.master2_port}},{{.master3_ip}}:{{.master3_port}}
--token=default_token
--admin_token=default_token
--bvar_dump_interval=60
--raft_dir=local:///mnt/vdb/data
--snapshot_file_name=snapshot.data
--auto_add_datanode_in_heartbeat=false
--min_log_level=0
--comlog_enable_async=false
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=20480
--comlog_quota_day=0
--comlog_quota_hour=0
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000
--raft_election_timeout_ms=10000
--snapshot_interval_s=86400
--monitor_scheduler_check_interval_s=60
--heartbeat_scheduler_check_interval_s=60
--balance_scheduler_check_interval_s=60
--index_scheduler_check_interval_s=10
--node_picker_refresh_interval_s=30
--scheduler_delay_start_s=300
--datanode_dead_timeout_s=180
--deadnode_auto_drop_timeout_s=86400
--proxy_dead_timeout_s=180
--tablet_scheduler_scan_all_interval_s=86400
--master_metric_refresh_interval_s=5
--leader_balance_distance_threshold=10
--max_tranfer_leader_balance_per_cycle=100
--balance_task_timeout_s=7200
--max_running_balance_task_per_dest_node=5
--max_running_balance_task_per_src_node=20
--balance_min_node_replica_num_diff=0.05
--cluster_safemode_threshold=5
