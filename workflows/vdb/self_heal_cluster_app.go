/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), self-healing
*/

/*
DESCRIPTION
进行标准版集群故障节点自愈

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"SelfHealingParams": [
		{
			"ShardID": 133,
			"NodeShortIDList": [12345, 12346]
		}
	]
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/applyresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/cds"
	checksubnets "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/check_subnets"
	checkversion "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/check_version"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/delresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/deploy"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	mountdisk "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/mount_disk"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/pushflag"
	restartmochow "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/restart_mochow"
	updatemasteraddr "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/newagent/update_master_addr"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/opmonitor"
	securitygroup "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/security_group"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/specification"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowSelfHealCluster                               = "vdb-self-heal-cluster-app"
	StepSelfHealClusterBuildMeta                          = "vdb-self-heal-cluster-build-meta"
	StepSelfHealClusterCheckVersion                       = "vdb-self-heal-cluster-check-version"
	StepSelfHealClusterFillSpec                           = "vdb-self-heal-cluster-fill-spec"
	StepSelfHealClusterCheckSubnetsEnoughIps              = "vdb-self-heal-cluster-check-subnets-enough-ips"
	StepSelfHealClusterCheckDataNodeStatus                = "vdb-self-heal-cluster-check-datanode-status"
	StepSelfHealClusterApplyResources                     = "vdb-self-heal-cluster-apply-resources"
	StepSelfHealClusterUpdateSecurityGroups               = "vdb-self-heal-cluster-update-security-groups"
	StepSelfHealClusterDeploy                             = "vdb-self-heal-cluster-apply-deploy"
	StepSelfHealClusterStopMochow                         = "vdb-self-heal-cluster-stop-mochow"
	StepSelfHealClusterGetVolumeID                        = "vdb-self-heal-cluster-get-volumeid"
	StepSelfHealClusterDeleteCds                          = "vdb-self-heal-cluster-delete-cds"
	StepSelfHealClusterDetachCdsForToDeleteNode           = "vdb-self-heal-cluster-detach-cds-for-todelete-node"
	StepSelfHealClusterAttachCdsForToCreateNode           = "vdb-self-heal-cluster-attach-cds-for-tocreate-node"
	StepSelfHealClusterMountDisk                          = "vdb-self-heal-cluster-mount-disk"
	StepSelfHealClusterStartMochow                        = "vdb-self-heal-cluster-start-mochow"
	StepSelfHealClusterMetaDelNodes                       = "vdb-self-heal-cluster-meta-del-nodes"
	StepSelfHealClusterMetaAddNodes                       = "vdb-self-heal-cluster-meta-add-nodes"
	StepSelfHealClusterSetRs                              = "vdb-self-heal-cluster-set-rs"
	StepSelfHealClusterPushFlag                           = "vdb-self-heal-cluster-push-flag"
	StepSelfHealClusterUpdateMasterAddr                   = "vdb-self-heal-cluster-update-master_addr"
	StepSelfHealClusterDeleteOldNodes                     = "vdb-self-heal-cluster-del-old-nodes"
	StepSelfHealClusterUpdateAppTopologyInXmaster         = "vdb-self-heal-cluster-update-app-topo-in-xmaster"
	StepSelfHealClusterInitOpMonitor                      = "vdb-self-heal-cluster-init-op-monitor"
	StepSelfHealClusterSuccessCallback                    = "vdb-self-heal-cluster-succ-cb"
	StepSelfHealClusterRollbackReleaseResources           = "vdb-self-heal-cluster-rollback-release-resource"
	StepSelfHealClusterRollbackBuildMeta                  = "vdb-self-heal-cluster-rollback-build-meta"
	StepSelfHealClusterRollbackCallback                   = "vdb-self-heal-cluster-rollback-cb"
	StepSelfHealClusterRollbackUpdateAppTopologyInXmaster = "vdb-self-heal-cluster-rollback-update-app-topo-in-xmaster"
)

func init() {
	// Step-1 构建元数据，将需要删除的节点改为ToDelete、新加节点ToCreate
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterBuildMeta,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForClusterSelfHeal,
		SuccessNextStep: StepSelfHealClusterCheckVersion,
		ErrorNextStep:   StepSelfHealClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterCheckVersion,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     checkversion.CheckMochowVersion,
		SuccessNextStep: StepSelfHealClusterFillSpec,
		ErrorNextStep:   StepSelfHealClusterCheckVersion,
	})

	// Step-2 将规格信息填入Cluster表或Interface表；
	// 同分片的node应当规格相同，Port相同，使用同样的部署集
	// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterFillSpec,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepSelfHealClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepSelfHealClusterFillSpec,
	})
	// Step-3 检查子网ip是否充足
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterCheckSubnetsEnoughIps,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepSelfHealClusterCheckDataNodeStatus,
		ErrorNextStep:   StepSelfHealClusterCheckSubnetsEnoughIps,
	})

	// Step-4 检查datanode状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterCheckDataNodeStatus,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     mochow.ProcessCheckDataNodeStatus,
		SuccessNextStep: StepSelfHealClusterApplyResources,
		ErrorNextStep:   StepSelfHealClusterCheckDataNodeStatus},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-5 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterApplyResources,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepSelfHealClusterUpdateSecurityGroups,
		ErrorNextStep:   StepSelfHealClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateSecurityGroups,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: StepSelfHealClusterDeploy,
		ErrorNextStep:   StepSelfHealClusterUpdateSecurityGroups,
	})

	// Step-7 部署相关包
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDeploy,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepSelfHealClusterStopMochow,
		ErrorNextStep:   StepSelfHealClusterDeploy},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-7.0 单节点实例，停mochow-standalone服务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterStopMochow,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     restartmochow.ProcessStopMochow,
		SuccessNextStep: StepSelfHealClusterGetVolumeID,
		ErrorNextStep:   StepSelfHealClusterStopMochow,
	})

	// Step-7.1 单节点实例，bcc的数据盘id写入db
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterGetVolumeID,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     cds.ProcessGetDataCds,
		SuccessNextStep: StepSelfHealClusterDeleteCds,
		ErrorNextStep:   StepSelfHealClusterGetVolumeID,
	})

	// Step-7.2 单节点实例，将bcc的数据盘卸载并删掉
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDeleteCds,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     cds.ProcessDeleteDataCds,
		SuccessNextStep: StepSelfHealClusterDetachCdsForToDeleteNode,
		ErrorNextStep:   StepSelfHealClusterDeleteCds,
	})

	// Step-7.3 单节点实例，卸载故障节点的数据盘
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDetachCdsForToDeleteNode,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     cds.ProcessDetachCds,
		SuccessNextStep: StepSelfHealClusterAttachCdsForToCreateNode,
		ErrorNextStep:   StepSelfHealClusterDetachCdsForToDeleteNode,
	})
	// Step-7.4 单节点实例，将故障节点的数据盘挂载到新bcc资源上
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterAttachCdsForToCreateNode,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     cds.ProcessAttachCds,
		SuccessNextStep: StepSelfHealClusterMountDisk,
		ErrorNextStep:   StepSelfHealClusterAttachCdsForToCreateNode,
	})

	// Step-7.5 单节点实例，下发mount_disk请求到新节点xagent
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMountDisk,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     mountdisk.ProcessMountCds,
		SuccessNextStep: StepSelfHealClusterStartMochow,
		ErrorNextStep:   StepSelfHealClusterMountDisk,
	})

	// Step-7.6 单节点实例，起mochow-standalone服务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterStartMochow,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     restartmochow.ProcessStartMochow,
		SuccessNextStep: StepSelfHealClusterMetaDelNodes,
		ErrorNextStep:   StepSelfHealClusterStartMochow,
	})

	// Step-8 集群版实例：master删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMetaDelNodes,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     mochow.ProcessDropNode,
		SuccessNextStep: StepSelfHealClusterMetaAddNodes,
		ErrorNextStep:   StepSelfHealClusterMetaDelNodes,
	})

	// Step-9 集群版实例：master添加节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMetaAddNodes,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     mochow.ProcessAddNode,
		SuccessNextStep: StepSelfHealClusterSetRs,
		ErrorNextStep:   StepSelfHealClusterMetaAddNodes,
	})

	// Step-10 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSetRs,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepSelfHealClusterPushFlag,
		ErrorNextStep:   StepSelfHealClusterSetRs,
	})

	// Step-11 推送flag让bcm不再push数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterPushFlag,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealClusterUpdateMasterAddr,
		ErrorNextStep:   StepSelfHealClusterPushFlag,
	})

	// 如果master发生故障，则更新master的地址
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateMasterAddr,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     updatemasteraddr.ProcessUpdateMasterAddr,
		SuccessNextStep: StepSelfHealClusterDeleteOldNodes,
		ErrorNextStep:   StepSelfHealClusterUpdateMasterAddr,
	})

	// Step-12 删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDeleteOldNodes,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepSelfHealClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepSelfHealClusterDeleteOldNodes,
	})

	// Step-13 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepSelfHealClusterInitOpMonitor,
		ErrorNextStep:   StepSelfHealClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterInitOpMonitor,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepSelfHealClusterSuccessCallback,
		ErrorNextStep:   StepSelfHealClusterInitOpMonitor,
	})

	// Step-14 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSuccessCallback,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSelfHealClusterSuccessCallback,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackReleaseResources,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepSelfHealClusterRollbackBuildMeta,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackBuildMeta,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepSelfHealClusterRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepSelfHealClusterRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackCallback,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
