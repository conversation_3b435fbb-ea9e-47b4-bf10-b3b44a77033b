/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* pause_cluster_app.go - File Description */
/*
Modification History
--------------------
2022/5/23, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
)

const (
	WorkflowPauseClusterApp     = "vdb-pause-cluster-app"
	StepPauseClusterUnbindAllRs = "vdb-pause-cluster-app-unbind-all-rs"
	StepPauseClusterCallbacks   = "vdb-pause-cluster-app-callbacks"
)

func init() {
	// Step-1 摘除所有Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseClusterUnbindAllRs,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     blb.ProcessUnbindAllProxys,
		SuccessNextStep: StepPauseClusterCallbacks,
		ErrorNextStep:   StepPauseClusterUnbindAllRs,
	})

	// Step-2 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseClusterCallbacks,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     callback.ProcessPauseCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPauseClusterCallbacks,
	})
}
