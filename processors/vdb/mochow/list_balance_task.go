/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:44
**/

package mochow

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/mochow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ListBalanceTaskNum(ctx context.Context, app *vdbmodel.Application) (int, error) {
	masterServer, err := getMasterLeader(ctx, app, vdbmodel.NodeOrProxyStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get master error", logit.Error("error", err))
		return 0, err
	}

	req := mochow.ListBalanceTaskRequest{
		Token: "default_token",
	}

	rsp, err := mochow.ListBalanceTask(ctx, masterServer, &req)
	if err != nil {
		resource.LoggerTask.Error(ctx, "call list balance task failed", logit.Error("error", err))
		return 0, err
	}

	resource.LoggerTask.Trace(ctx, "list balance task", logit.String("rsp :", base_utils.Format(rsp)))

	if rsp.Status.Code != 0 {
		resource.LoggerTask.Error(ctx, "list balance task return error", logit.Int32("status_code", rsp.Status.Code))
		return 0, fmt.Errorf(rsp.Status.Msg)
	}

	return len(rsp.Tasks), nil
}
