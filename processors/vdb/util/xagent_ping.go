/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* xagent_ping.go */
/*
modification history
--------------------
2023/01/01 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package util

import (
	"context"
	"errors"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	livenessProbeTimeout = 30
)

var (
	ErrCallXagentFail           = errors.New("call xagent fail")
	ErrXagentCallRedisIOTimeout = errors.New("ping redis io timeout")
)

type livenessProbeParams struct {
	Meta *meta `json:"meta"`
}

type meta struct {
	Host        string `json:"host"`
	Port        int    `json:"port"`
	AccountName string `json:"account_name"`
	Password    string `json:"password"`
}

func LivenessProbe(ctx context.Context, xagentAddr *xagent.Addr, target *vdbmodel.Node, password string) (bool, error) {
	encryptPwd := ""
	if password != "" {
		encryptPwd, _ = crypto_utils.EncryptKey(password)
		resource.LoggerTask.Trace(ctx, "get encryptkey pwd", logit.String("pwd", password),
			logit.String("new pwd", encryptPwd))
	}
	pingReq := xagent.Request{
		Addr:   xagentAddr,
		Action: "livenessprobe",
		Params: livenessProbeParams{Meta: &meta{
			Host:     target.IP,
			Port:     target.Port,
			Password: encryptPwd,
		}},
		Product: vdbmodel.ProductVDB,
	}

	resp, err := xagent.Instance().Do(ctx, &pingReq)
	// 调用xagent失败
	if err != nil {
		resource.LoggerTask.Trace(ctx, "call xagent fail", logit.Error("err", err))
		if strings.Contains(err.Error(), "timeout") {
			return false, ErrXagentCallRedisIOTimeout
		}
		return false, ErrCallXagentFail
	}
	strResp := base_utils.Format(resp.Result)
	resource.LoggerTask.Trace(ctx, "ping via xagent success", logit.String("xagentAddr", base_utils.Format(xagentAddr)),
		logit.String("target", target.NodeID), logit.String("resp", strResp))

	return true, nil
}
