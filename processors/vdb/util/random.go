/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/04/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file random.go
 * <AUTHOR>
 * @date 2023/04/24 15:19:34
 * @brief
 *
 **/

package util

import (
	"math/rand"
)

// GetRandomNumber 随机返回1-10之间的数字
func GetRandomNumber() int {
	// 生成1-10之间的随机数
	num := rand.Intn(10) + 1
	return num
}
