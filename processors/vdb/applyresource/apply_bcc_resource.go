/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package applyresource

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func getClusterDeploySetID(appID string) string {
	return appID + "-" + "datanode"
}

func getItfDeploySetID(appID string, interfaceID string) string {
	itfIDChunks := strings.Split(interfaceID, appID)
	if len(itfIDChunks) != 2 {
		return interfaceID
	}
	return appID + "-proxy" + strings.ReplaceAll(itfIDChunks[1], "-", "_")
}

func getMasterDeploySetID(appID string) string {
	return appID + "-" + "master"
}

func getCustomerDeploySetIDs(appDeploySetIds string) []string {
	ret := []string{}
	for _, did := range strings.Split(appDeploySetIds, ",") {
		if len(appDeploySetIds) != 0 {
			ret = append(ret, did)
		}
	}
	return ret
}

func fillClusterToRequestsMap(aZoneReqMap map[string]*bccresource.CreateBccResourceParams, app *vdbmodel.Application) {
	internalSecurityGroupID := app.SecurityGroupID
	if len(app.InternalSecurityGroupID) != 0 {
		internalSecurityGroupID = app.InternalSecurityGroupID
	}

	if len(internalSecurityGroupID) == 0 && privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		internalSecurityGroupID = "fake_sg"
	}

	for _, cluster := range app.Clusters {
		clusterItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[node.Azone]; !found {
				aZoneReqMap[node.Azone] = &bccresource.CreateBccResourceParams{
					AppID:                app.AppID,
					UserID:               app.UserID,
					Product:              app.Product,
					ImageID:              getImageID(app),
					VpcID:                app.VpcID,
					CustomerDeploySetIDs: getCustomerDeploySetIDs(app.DeploySetIDs),
					LogicalZone:          node.LogicZone,
					Azone:                node.Azone,
					StoreType:            cluster.StoreType,
					DataDiskStorageType:  app.DiskType,
					InstanceType:         app.Type,
				}
			}
			if _, found := clusterItemMap[node.Azone]; !found {
				aZoneReqMap[node.Azone].Items = append(aZoneReqMap[node.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      cluster.AvailableVolume,
						CPUCount:             cluster.CPU,
						MemoryCapacityInGB:   cluster.MemSize,
						RootDiskCapacityInGB: cluster.SysDiskSize,
						DataDiskCapacityInGB: int(cluster.DiskSize),
					},
					Subnet:          node.SubnetID,
					Engine:          cluster.Engine,
					Count:           1,
					DeploySetID:     getClusterDeploySetID(app.AppID),
					SecurityGroupID: internalSecurityGroupID,
					EntityIDs:       []string{node.NodeID},
				})
				clusterItemMap[node.Azone] = &(aZoneReqMap[node.Azone].Items[len(aZoneReqMap[node.Azone].Items)-1])
			} else {
				clusterItemMap[node.Azone].Count++
				clusterItemMap[node.Azone].EntityIDs = append(clusterItemMap[node.Azone].EntityIDs, node.NodeID)
			}
		}
	}
}

func getImageID(app *vdbmodel.Application) string {
	imageID, _ := util.GetImageIDAndVersion(app.ImageID)
	return imageID
}

func fillInterfaceToRequestsMap(aZoneReqMap map[string]*bccresource.CreateBccResourceParams, app *vdbmodel.Application) {
	securityGroupID := app.SecurityGroupID
	if len(securityGroupID) == 0 && privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		securityGroupID = "fake_sg"
	}
	for _, itf := range app.Interfaces {
		itfItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}
		for _, proxy := range itf.Proxies {
			if proxy.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[proxy.Azone]; !found {
				aZoneReqMap[proxy.Azone] = &bccresource.CreateBccResourceParams{
					AppID:   app.AppID,
					UserID:  app.UserID,
					Product: app.Product,
					ImageID: getImageID(app),
					VpcID:   app.VpcID,
					// CustomerDeploySetIDs
					CustomerDeploySetIDs: getCustomerDeploySetIDs(app.DeploySetIDs),
					LogicalZone:          proxy.LogicZone,
					Azone:                proxy.Azone,
					StoreType:            itf.StoreType,
					DataDiskStorageType:  app.DiskType,
					InstanceType:         app.Type,
				}
			}
			if _, found := itfItemMap[proxy.Azone]; !found {
				aZoneReqMap[proxy.Azone].Items = append(aZoneReqMap[proxy.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      itf.AvailableVolume,
						CPUCount:             itf.CPU,
						MemoryCapacityInGB:   itf.MemSize,
						RootDiskCapacityInGB: itf.SysDiskSize,
						DataDiskCapacityInGB: int(itf.DiskSize),
					},
					Subnet:          proxy.SubnetID,
					Engine:          itf.Engine,
					Count:           1,
					DeploySetID:     getItfDeploySetID(app.AppID, itf.InterfaceID),
					SecurityGroupID: securityGroupID,
					EntityIDs:       []string{proxy.ProxyID},
				})
				itfItemMap[proxy.Azone] = &(aZoneReqMap[proxy.Azone].Items[len(aZoneReqMap[proxy.Azone].Items)-1])
			} else {
				itfItemMap[proxy.Azone].Count++
				itfItemMap[proxy.Azone].EntityIDs = append(itfItemMap[proxy.Azone].EntityIDs, proxy.ProxyID)
			}
		}
	}
}

func fillMasterToRequestsMap(aZoneReqMap map[string]*bccresource.CreateBccResourceParams, app *vdbmodel.Application) {
	internalSecurityGroupID := app.SecurityGroupID
	if len(app.InternalSecurityGroupID) != 0 {
		internalSecurityGroupID = app.InternalSecurityGroupID
	}

	if len(internalSecurityGroupID) == 0 && privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		internalSecurityGroupID = "fake_sg"
	}

	for _, master := range app.Masters {
		if master.Status != vdbmodel.NodeOrProxyStatusToCreate {
			continue
		}
		clusterItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}

		if _, found := aZoneReqMap[master.Azone]; !found {
			aZoneReqMap[master.Azone] = &bccresource.CreateBccResourceParams{
				AppID:                app.AppID,
				UserID:               app.UserID,
				Product:              app.Product,
				ImageID:              getImageID(app),
				VpcID:                app.VpcID,
				CustomerDeploySetIDs: getCustomerDeploySetIDs(app.DeploySetIDs),
				LogicalZone:          master.LogicZone,
				Azone:                master.Azone,
				StoreType:            master.StoreType,
				DataDiskStorageType:  app.DiskType,
				InstanceType:         app.Type,
			}
		}
		if _, found := clusterItemMap[master.Azone]; !found {
			aZoneReqMap[master.Azone].Items = append(aZoneReqMap[master.Azone].Items, bccresource.CreateBccResourceParamsItem{
				Specification: specification.Specification{
					AvailableVolume:      master.AvailableVolume,
					CPUCount:             master.CPU,
					MemoryCapacityInGB:   master.MemSize,
					RootDiskCapacityInGB: master.SysDiskSize,
					DataDiskCapacityInGB: int(master.DiskSize),
				},
				Subnet:          master.SubnetID,
				Engine:          master.Engine,
				Count:           1,
				DeploySetID:     getMasterDeploySetID(app.AppID),
				SecurityGroupID: internalSecurityGroupID,
				EntityIDs:       []string{master.MasterID},
			})
			clusterItemMap[master.Azone] = &(aZoneReqMap[master.Azone].Items[len(aZoneReqMap[master.Azone].Items)-1])
		} else {
			clusterItemMap[master.Azone].Count++
			clusterItemMap[master.Azone].EntityIDs = append(clusterItemMap[master.Azone].EntityIDs, master.MasterID)
		}

	}
}

func getReousrcePriority(ctx context.Context, app *vdbmodel.Application) string {
	var priority string = "normal"
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				priority = "high"
				return priority
			}
		}
	}

	return priority
}

func saveReousrceOrderIds(ctx context.Context, aZoneOrderMap map[string]string, app *vdbmodel.Application) error {
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = vdbmodel.ApplicationGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	for aZone, orderID := range aZoneOrderMap {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				if node.Status == vdbmodel.NodeOrProxyStatusToCreate && node.Azone == aZone {
					node.ResourceOrderID = orderID
				}
			}
		}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxies {
				if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate && proxy.Azone == aZone {
					proxy.ResourceOrderID = orderID
				}
			}
		}
		for _, master := range app.Masters {
			if master.Status == vdbmodel.NodeOrProxyStatusToCreate && master.Azone == aZone {
				master.ResourceOrderID = orderID
			}

		}
	}
	return vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
}

func getNodeFinder(app *vdbmodel.Application) func(eid string) (*vdbmodel.Node, error) {
	nodesMap := make(map[string]*vdbmodel.Node)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			nodesMap[node.NodeID] = node
		}
	}
	return func(eid string) (*vdbmodel.Node, error) {
		node, found := nodesMap[eid]
		if !found {
			return nil, fmt.Errorf("node %s not found", eid)
		}
		return node, nil
	}
}

func getProxyFinder(app *vdbmodel.Application) func(eid string) (*vdbmodel.Proxy, error) {
	proxysMap := make(map[string]*vdbmodel.Proxy)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			proxysMap[proxy.ProxyID] = proxy
		}
	}
	return func(eid string) (*vdbmodel.Proxy, error) {
		proxy, found := proxysMap[eid]
		if !found {
			return nil, fmt.Errorf("node %s not found", eid)
		}
		return proxy, nil
	}
}

func getMasterNodeFinder(app *vdbmodel.Application) func(eid string) (*vdbmodel.Master, error) {
	nodesMap := make(map[string]*vdbmodel.Master)
	for _, master := range app.Masters {
		nodesMap[master.MasterID] = master
	}
	return func(eid string) (*vdbmodel.Master, error) {
		node, found := nodesMap[eid]
		if !found {
			return nil, fmt.Errorf("master %s not found", eid)
		}
		return node, nil
	}
}

func fillResources(ctx context.Context, servers []bccresource.BccResources, nodeFinder func(eid string) (*vdbmodel.Node, error),
	proxyFinder func(eid string) (*vdbmodel.Proxy, error),
	masterNodeFinder func(eid string) (*vdbmodel.Master, error)) error {
	resource.LoggerTask.Trace(ctx, "fill resources", logit.String("servers :", base_utils.Format(servers)))
	for i := range servers {
		server := &servers[i]
		if node, err := nodeFinder(server.EntityID); err == nil {
			node.ResourceID = server.ID
			node.IP = server.FixIP
			node.FloatingIP = server.FloatingIP
			node.RootPassword = server.RootPassword
			node.IPv6 = server.FixIPv6
			node.HostName = server.Name
			continue
		}

		if master, err := masterNodeFinder(server.EntityID); err == nil {
			master.ResourceID = server.ID
			master.IP = server.FixIP
			master.FloatingIP = server.FloatingIP
			master.RootPassword = server.RootPassword
			master.IPv6 = server.FixIPv6
			master.HostName = server.Name
			continue
		}

		if proxy, err := proxyFinder(server.EntityID); err == nil {
			proxy.ResourceID = server.ID
			proxy.IP = server.FixIP
			proxy.FloatingIP = server.FloatingIP
			proxy.RootPassword = server.RootPassword
			proxy.IPv6 = server.FixIPv6
			proxy.HostName = server.Name
			continue
		}
		return cerrs.Errorf("entity %s not found", server.EntityID)
	}
	return nil
}

func formatBccMetaData(ctx context.Context, server *bccresource.BccResources) string {
	r := ""
	rss, err := json.Marshal(&server.MetaData)
	if err != nil {
		resource.LoggerTask.Error(ctx, "marshal server meta data failed", logit.Error("error", err))
		return ""
	}
	r = string(rss)

	return r
}

func getPortFromBccMetaData(ctx context.Context, server *bccresource.BccResources) ([]string, error) {
	if val, ok := server.MetaData["port"]; !ok || val == "" {
		return nil, cerrs.Errorf("parse port failed: port not found")
	}

	ports := strings.Split(server.MetaData["port"], "|")
	if len(ports) <= 1 {
		return nil, cerrs.Errorf("parse port failed: port num is not enough in metadata")
	}
	return ports, nil

}

func getServerPortFromBccMetaData(ctx context.Context, server *bccresource.BccResources) (int, error) {
	ports, err := getPortFromBccMetaData(ctx, server)
	if err != nil {
		return -1, err
	}
	return strconv.Atoi(ports[0])
}

func getXagentPortFromBccMetaData(ctx context.Context, server *bccresource.BccResources) (int, error) {
	ports, err := getPortFromBccMetaData(ctx, server)
	if err != nil {
		return -1, err
	}
	return strconv.Atoi(ports[len(ports)-1])
}

func fillAdaptorBccResources(ctx context.Context, servers []bccresource.BccResources, nodeFinder func(eid string) (*vdbmodel.Node, error),
	proxyFinder func(eid string) (*vdbmodel.Proxy, error),
	masterNodeFinder func(eid string) (*vdbmodel.Master, error)) error {
	resource.LoggerTask.Trace(ctx, "fill adaptor bcc resources", logit.String("servers :", base_utils.Format(servers)))
	for i := range servers {
		server := &servers[i]
		serverPort, err := getServerPortFromBccMetaData(ctx, server)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get server port from bcc meta data failed", logit.String("server :", base_utils.Format(server)), logit.Error("error", err))
			return err
		}

		xagentPort, err := getXagentPortFromBccMetaData(ctx, server)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get xagent port from bcc meta data failed", logit.String("server :", base_utils.Format(server)), logit.Error("error", err))
			return err
		}

		if node, err := nodeFinder(server.EntityID); err == nil {
			node.ResourceID = server.ID
			node.IP = server.FixIP
			node.FloatingIP = server.FloatingIP
			node.RootPassword = server.RootPassword
			node.IPv6 = server.FixIPv6
			node.HostName = server.Name
			node.Properties = formatBccMetaData(ctx, server)
			node.Port = serverPort
			node.XagentPort = xagentPort
			continue
		}

		if master, err := masterNodeFinder(server.EntityID); err == nil {
			master.ResourceID = server.ID
			master.IP = server.FixIP
			master.FloatingIP = server.FloatingIP
			master.RootPassword = server.RootPassword
			master.IPv6 = server.FixIPv6
			master.HostName = server.Name
			master.Properties = formatBccMetaData(ctx, server)
			master.Port = serverPort
			master.XagentPort = xagentPort
			continue
		}

		if proxy, err := proxyFinder(server.EntityID); err == nil {
			proxy.ResourceID = server.ID
			proxy.IP = server.FixIP
			proxy.FloatingIP = server.FloatingIP
			proxy.RootPassword = server.RootPassword
			proxy.IPv6 = server.FixIPv6
			proxy.HostName = server.Name
			proxy.Properties = formatBccMetaData(ctx, server)
			proxy.Port = serverPort
			proxy.XagentPort = xagentPort
			continue
		}
		return cerrs.Errorf("entity %s not found", server.EntityID)
	}
	return nil
}

// Process 申请bcc资源
// 1. 遍历所有cluster、interface、master中需要新建的node、proxy、master; 生成bcc创建请求(对应CreateBccResourceParams)
// 2. 调用 bccresource.CreateBccResources 发送创建请求
// 3. 调用 bccresource.ShowBccResources 查询资源
// 4. 将资源信息写入到X1数据库中
func ProcessApplyBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	aZoneReqMap, aZoneOrderMap := generateRequest(app)

	resource.LoggerTask.Trace(ctx, "generateRequest", logit.String("aZoneReqMap :", base_utils.Format(aZoneReqMap)),
		logit.String("aZoneOrderMap :", base_utils.Format(aZoneOrderMap)))

	err = applyResource(ctx, teu, aZoneReqMap, app, aZoneOrderMap, param, err)
	if err != nil {
		return err
	}

	if err := queryResource(ctx, teu, aZoneOrderMap, app); err != nil {
		resource.LoggerTask.Error(ctx, "query bcc resources failed", logit.Error("error", err))
		return err
	}
	return nil
}

func applyResource(ctx context.Context, teu *workflow.TaskExecUnit, aZoneReqMap map[string]*bccresource.CreateBccResourceParams,
	app *vdbmodel.Application, aZoneOrderMap map[string]string, param *iface.TaskParameters, err error) error {

	var reqErr error
	var errReq *bccresource.CreateBccResourceParams
	for aZone, request := range aZoneReqMap {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("zone %s request is %+v", aZone, *request))
		var orderID string
		var err error
		orderID, err = loadSucceededOrder(ctx, app.AppID, teu.TaskID, aZone)
		if err == nil && len(orderID) != 0 {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("zone %s has succeeded order %s", aZone, orderID))
			aZoneOrderMap[aZone] = orderID
			continue
		}
		request.Retry = getRetry(teu.TaskBatchID)
		request.TaskID = teu.TaskID

		orderID, err = bccresource.BccResourceOp().CreateBccResources(ctx, request)

		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("apply for bcc resources of azone %s failed", aZone), logit.Error("error", err))
			reqErr = err
			errReq = request
			break
		}
		if err := saveSucceededOrder(ctx, app.AppID, teu.TaskID, aZone, orderID); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("save bcc resource order id of azone %s failed", aZone), logit.Error("error", err))
		}
		aZoneOrderMap[aZone] = orderID
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("azone orderid map is %+v", aZoneOrderMap))
	if err := saveReousrceOrderIds(ctx, aZoneOrderMap, app); err != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("save resources orders into db failed, order: %+v", aZoneOrderMap), logit.Error("error", err))
		return err
	}
	if reqErr != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("send bcc resources request failed, request: %+v", errReq),
			logit.Error("error", err), logit.Int("error_code", cerrs.Code(reqErr)))
		return reqErr
	}
	return nil
}

func generateRequest(app *vdbmodel.Application) (map[string]*bccresource.CreateBccResourceParams, map[string]string) {
	aZoneReqMap := make(map[string]*bccresource.CreateBccResourceParams)
	aZoneOrderMap := make(map[string]string)
	fillClusterToRequestsMap(aZoneReqMap, app)
	fillInterfaceToRequestsMap(aZoneReqMap, app)
	fillMasterToRequestsMap(aZoneReqMap, app)
	return aZoneReqMap, aZoneOrderMap
}

func queryResource(ctx context.Context, teu *workflow.TaskExecUnit, aZoneOrderMap map[string]string, app *vdbmodel.Application) error {
	aZoneOrderResultMap := make(map[string][]bccresource.BccResources)
	for azone, orderID := range aZoneOrderMap {
		for {
			var err error

			var servers []bccresource.BccResources

			servers, err = bccresource.BccResourceOp().ShowBccResourcesByOrder(ctx, &bccresource.ShowBccResourcesParams{
				UserID:  app.UserID,
				OrderID: orderID,
			})

			if err != nil {
				if cerrs.Code(err) == bccresource.CODE_BCC_ORDER_IN_OPERATION {
					resource.LoggerTask.Notice(ctx, fmt.Sprintf("order %s in operation, try next", orderID))
				} else if cerrs.Code(err) == bccresource.CODE_BCC_ORDER_ERROR {
					resource.LoggerTask.Error(ctx, fmt.Sprintf("order %s is error", orderID), logit.Error("error", err))
					if err2 := deleteSucceededOrder(ctx, app.AppID, teu.TaskID, azone); err2 != nil {
						resource.LoggerTask.Warning(ctx, fmt.Sprintf("delete bcc resource order id of azone %s from cache failed", azone),
							logit.Error("error", err2))
						return err2
					}
					return err
				} else {
					resource.LoggerTask.Warning(ctx, fmt.Sprintf("order %s unknown status, try next", orderID), logit.Error("error", err))
				}
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(2 * time.Second):
					continue
				}
			} else {
				aZoneOrderResultMap[azone] = servers
				break
			}
		}
	}
	if err := saveResources(ctx, app, aZoneOrderResultMap); err != nil {
		return err
	}
	return nil
}

func saveResources(ctx context.Context, app *vdbmodel.Application, aZoneOrderResultMap map[string][]bccresource.BccResources) error {
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = vdbmodel.ApplicationGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	nodeFinder := getNodeFinder(app)
	proxyFinder := getProxyFinder(app)
	masterNodeFinder := getMasterNodeFinder(app)

	for azone, servers := range aZoneOrderResultMap {
		if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
			if err := fillAdaptorBccResources(ctx, servers, nodeFinder, proxyFinder, masterNodeFinder); err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("fill %s adaptor resource error", azone), logit.Error("error", err))
				return err
			}
		} else {
			if err := fillResources(ctx, servers, nodeFinder, proxyFinder, masterNodeFinder); err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("fill %s resource error", azone), logit.Error("error", err))
				return err
			}
		}
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func getStep(batchID string) string {
	chunks := strings.Split(batchID, "|")
	if len(chunks) > 1 {
		return chunks[1]
	}
	return ""
}

func saveSucceededOrder(ctx context.Context, appID string, taskID string, zone string, orderID string) error {
	key := "success_resource_order:" + appID + ":" + taskID + ":" + zone
	if err := resource.RedisClient.SetNX(ctx, key, orderID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func loadSucceededOrder(ctx context.Context, appID string, taskID string, zone string) (string, error) {
	key := "success_resource_order:" + appID + ":" + taskID + ":" + zone
	orderID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return orderID, nil
}

func deleteSucceededOrder(ctx context.Context, appID string, taskID string, zone string) error {
	key := "success_resource_order:" + appID + ":" + taskID + ":" + zone
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func getRetry(batchID string) int {
	if strings.Contains(batchID, "|") {
		if strings.Contains(strings.Split(batchID, "|")[1], "__") {
			if retry, err := strconv.Atoi(strings.Split(strings.Split(batchID, "|")[1], "__")[1]); err == nil {
				return retry
			}
		}
	}
	return 0
}
