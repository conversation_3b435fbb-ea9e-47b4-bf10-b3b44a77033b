# Service的名字，必选，需自定义修改
Name = "nova"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 1000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 5000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 5000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 0

[Strategy]
    ## 资源使用策略，非必选，默认使用 RoundRobin
    ## RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
    Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "**************"
        Port = 8774
    [[Resource.Manual.minicloud]]
        Host = "**************"
        Port = 8774
    [[Resource.Manual.onlinebj]]
        Host = "bccproxy.bj.bce-internal.baidu.com"
        Port = 18774
    [[Resource.Manual.onlinebd]]
        Host = "bccproxy.bdbl.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.onlinefsh]]
        Host = "bccproxy.fsh.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.onlinegz]]
        Host = "bccproxy.gz.bce-internal.baidu.com"
        Port = 18774
    [[Resource.Manual.onlinehkg]]
        Host = "bccproxy.hkg.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.onlinesu]]
        Host = "bccproxy.su.bce-internal.baidu.com"
        Port = 18774
    [[Resource.Manual.onlinewh]]
        Host = "bccproxy.fwh.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.onlineyq]]
        Host = "bccproxy.yq.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.onlinecd]]
        Host = "bccproxy.cd.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.onlinenj]]
        Host = "bccproxy.nj.bce.baidu-int.com"
        Port = 18774
    [[Resource.Manual.preonline]]
        Host = "**************"
        Port = 8774
    [[Resource.Manual.bjtest]]
        Host = "bccproxy.bj.bce-internal.baidu.com"
        Port = 18774
    [[Resource.Manual.bdtest]]
        Host = "bccproxy.bdbl.bce.baidu-int.com"
        Port = 18774

# 其他专有配置，如mysql、redis等都有专有配置
[Nova]
    Product = "vdb"