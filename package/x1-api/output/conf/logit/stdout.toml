# 用于打印进程的 stdout 日志
# logit 文档：https://gdp.baidu-int.com/api/baidu/gdp/logit/

# 日志文件名称 
# 建议不要调整 FileName 的值，这样在各个 PaaS 上时，监控系统可以采用统一的采集规则
FileName="{gdp.LogDir}/std/stdout.log"

# 日志切分规则,可选参数，默认为1hour
# 可选值和切分的文件后缀如下：
# 1hour -> .2020072714
# no 不切分
# 1day -> .20200727
# 1min -> .202007271452
# 5min -> .202007271450
# 10min -> .202007271450
# 30min -> .202007271430
# 若上述默认规则不满足，也可以自定义，详见 baidu/gdp/extension ：writer
RotateRule="1hour"

# 日志文件保留个数，可选参数
# 是 notice、wf 文件的分别的个数
# 默认48个，若为-1，日志文件将不清理
MaxFileNum=24

# 日志异步队列大小，可选参数
# 默认值 4096，若为-1，则队列大小为0
BufferSize=4096

# 日志编码的对象池名称，可选参数
# 默认为 default_text（普通文本编码）
# 可选值：default_json (JSON 编码，会将 HTML 字符转义)
# 可选值：default_json_html (JSON 编码，不会将 HTML 字符转义)
# 可通过 RegisterEncoderPool 自定义
EncoderPool="default_text"

# 不需要 配置 [[Dispatch]] 部分
