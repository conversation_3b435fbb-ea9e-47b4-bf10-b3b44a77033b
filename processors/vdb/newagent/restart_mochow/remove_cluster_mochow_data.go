package restartmochow

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

const (
	DataDir = "/mnt/data"
)

type removeMochowDataXagentRequest struct {
	TargetDir string `json:"target_dir"`
}

func ProcessRemoveClusterMochowData(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "stop mochow params", logit.String("params :", base_utils.Format(param)),
		logit.String("nodeID", base_utils.Format(teu.Entity)))

	appID := param.AppID
	nodeID := teu.Entity

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	engine := util.GetEngineByID(ctx, app, nodeID)
	if engine == "" {
		resource.LoggerTask.Error(ctx, "engine get by id failed", logit.String("nodeID", nodeID))
		return errors.New("engine get by id failed")
	}

	hostIP := ""
	port := int32(0)
	switch engine {
	case vdbmodel.EngineVDBDataNode:
		node, _, err := buildmeta.FindNodeByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}

		hostIP = node.FloatingIP
		port = int32(node.XagentPort)

	case vdbmodel.EngineVDBProxy:
		proxy, err := buildmeta.FindProxyByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "proxy not found", logit.Error("error", err))
			return err
		}

		hostIP = proxy.FloatingIP
		port = int32(proxy.XagentPort)

	case vdbmodel.EngineVDBMaster:
		master, err := buildmeta.FindMasterByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "master not found", logit.Error("error", err))
			return err
		}

		hostIP = master.FloatingIP
		port = int32(master.XagentPort)
	}

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: hostIP,
			Port: port,
		},
		Action: "remove_mochow_data",
		Params: &removeMochowDataXagentRequest{
			TargetDir: DataDir,
		},
		Product: vdbmodel.ProductVDB,
	}

	_, err = xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "stop node mochow fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)), logit.Error("err", err))
		return err
	}

	return nil
}
