[[SpecificationList]]
EngineList=["proxy"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
    [SpecificationList.Specification]
    AvailableVolume=4
    Name="vdb.p.small"
    CPUCount=2
    MemoryCapacityInGB=4
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["master"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
    [SpecificationList.Specification]
    AvailableVolume=4
    Name="vdb.m.small"
    CPUCount=2
    MemoryCapacityInGB=4
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["master"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
    [SpecificationList.Specification]
    AvailableVolume=8
    Name="vdb.m.medium"
    CPUCount=4
    MemoryCapacityInGB=8
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=24

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
    [SpecificationList.Specification]
    AvailableVolume=4
    Name="vdb.fdm.small"
    CPUCount=2
    MemoryCapacityInGB=4
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
    [SpecificationList.Specification]
    AvailableVolume=4
    Name="vdb.dc.small"
    CPUCount=2
    MemoryCapacityInGB=4
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
    [SpecificationList.Specification]
    AvailableVolume=8
    Name="vdb.dc.medium"
    CPUCount=4
    MemoryCapacityInGB=8
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=24

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=16
    Name="vdb.dc.large"
    CPUCount=8
    MemoryCapacityInGB=16
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=48

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=32
    Name="vdb.dc.2xlarge"
    CPUCount=16
    MemoryCapacityInGB=32
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=96

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=64
    Name="vdb.dc.4xlarge"
    CPUCount=32
    MemoryCapacityInGB=64
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=192

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=16
    Name="vdb.dm.small"
    CPUCount=2
    MemoryCapacityInGB=16
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=48

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=32
    Name="vdb.dm.medium"
    CPUCount=4
    MemoryCapacityInGB=32
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=96

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=64
    Name="vdb.dm.large"
    CPUCount=8
    MemoryCapacityInGB=64
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=192

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=128
    Name="vdb.dm.2xlarge"
    CPUCount=16
    MemoryCapacityInGB=128
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=372

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=256
    Name="vdb.dm.4xlarge"
    CPUCount=32
    MemoryCapacityInGB=256
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=768

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone"]
    [SpecificationList.Specification]
    AvailableVolume=4
    Name="vdb.fdm.small"
    CPUCount=2
    MemoryCapacityInGB=4
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=8
    Name="vdb.db.small"
    CPUCount=2
    MemoryCapacityInGB=8
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=16
    Name="vdb.db.medium"
    CPUCount=4
    MemoryCapacityInGB=16
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=24

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=32
    Name="vdb.db.large"
    CPUCount=8
    MemoryCapacityInGB=32
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=48

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=64
    Name="vdb.db.2xlarge"
    CPUCount=16
    MemoryCapacityInGB=64
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=96

[[SpecificationList]]
EngineList=["datanode"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone","cluster"]
    [SpecificationList.Specification]
    AvailableVolume=128
    Name="vdb.db.4xlarge"
    CPUCount=32
    MemoryCapacityInGB=128
    RootDiskCapacityInGB=40
    DataDiskCapacityInGB=192