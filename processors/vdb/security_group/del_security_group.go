package securitygroup

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// (1) 仅删除短 ID 的安全组
// (2) 先删除 BCC，再进行删除安全组，因为 BCC 是异步删除，如果删除安全组失败，则通过旁路进行检查并删除
// (3) 若删除安全组成功，则加个 "_deleted" 后缀
func ProcessDelSecurityGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// get app
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	if len(app.SecurityGroupID) == 0 {
		return nil
	}

	// delete app sg(Ignore errors)
	sgComponent := security_group_openapi.Instance()
	if strings.HasPrefix(app.SecurityGroupID, "g-") && !strings.HasSuffix(app.SecurityGroupID, "_deleted") {
		err = sgComponent.DeleteSecurityGroup(ctx, &security_group_openapi.DeleteSecurityGroupParams{
			UserID:          app.UserID,
			SecurityGroupID: app.SecurityGroupID,
		})
		if err == nil {
			app.SecurityGroupID = app.SecurityGroupID + "_deleted"
		} else {
			resource.LoggerTask.Warning(ctx, "delete sg fail", logit.String("appId", app.AppID), logit.String("SecurityGroupID", app.SecurityGroupID),
				logit.Error("error", err))
		}
	}

	// delete app internal sg(Ignore errors)
	if strings.HasPrefix(app.InternalSecurityGroupID, "g-") && !strings.HasSuffix(app.InternalSecurityGroupID, "_deleted") {
		err = sgComponent.DeleteSecurityGroup(ctx, &security_group_openapi.DeleteSecurityGroupParams{
			UserID:          app.UserID,
			SecurityGroupID: app.InternalSecurityGroupID,
		})
		if err == nil {
			app.InternalSecurityGroupID = app.InternalSecurityGroupID + "_deleted"
		} else {
			resource.LoggerTask.Warning(ctx, "delete sg fail", logit.String("appId", app.AppID), logit.String("InternalSecurityGroupID", app.InternalSecurityGroupID),
				logit.Error("error", err))
		}
	}

	return saveSecurityGroup(ctx, app)
}
