# Service的名字，必选，需自定义修改
Name = "neutron_endpoint"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 1000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 5000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 10000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 2

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
    Host = "logical-vpc.internal-qasandbox.baidu-int.com"
    Port = 80
    [[Resource.Manual.minicloud]]
    Host = "**************"
    Port = 9696
    [[Resource.Manual.onlinebj]]
        Host = "bcc.bj.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinebd]]
        Host = "bcc.bd.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinefsh]]
        Host = "bcc.fsh.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinegz]]
        Host = "bcc.gz.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinehkg]]
        Host = "bcc.hkg.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinesu]]
        Host = "bcc.su.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinewh]]
        Host = "bcc.fwh.baidubce.com"
        Port = 80
    [[Resource.Manual.onlineyq]]
        Host = "bcc.yq.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinecd]]
        Host = "bcc.cd.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinenj]]
        Host = "bcc.nj.baidubce.com"
        Port = 80
    [[Resource.Manual.preonline]]
        Host = "**************"
        Port = 19696
    [[Resource.Manual.bjtest]]
        Host = "bcc.bj.baidubce.com"
        Port = 80
    [[Resource.Manual.bdtest]]
        Host = "bcc.bd.baidubce.com"
        Port = 80

#其他专有配置，如mysql、redis等都有专有配置
[Endpoint]
    ResourceID = "38a382d468db4740837fbdca0a5e14bf"
    EncryptKey = "bSXXzkGxedhvD2+p"
    Product = "vdb"
    [Endpoint.IDC_SPEC.bdtest]
        ResourceID = "38a382d468db4740837fbdca0a5e14bf"
