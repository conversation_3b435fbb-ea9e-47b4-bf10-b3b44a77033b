package tls

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/utils/conf"
)

const (
	EnvJSONDir   = "/home/<USER>"
	ProxyWorkDir = "/home/<USER>/mochow-proxy"
)

type XagentRequest struct {
	EnvDir         string `json:"env_dir"`
	WorkDir        string `json:"work_dir"`
	EnableTLS      string `json:"enbale_tls"`
	CertData       string `json:"cert_data"`
	CertPrivateKey string `json:"cert_private_key"`
}

func ProcessApplyCertificate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	certDataString := ""
	certPrivateKeyString := ""
	if app.EnableTLS == "enable" {
		cert, err := vdbmodel.CertificateServerCAGetByAppID(ctx, app.AppID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get certificate fail", logit.String("appId", app.AppID),
				logit.Error("dbError", err))
			return err
		}
		if cert != nil {
			pkPem, err := DecryptAES(cert.CertPrivateKey, conf.TLSConfIns.EncryptPass)
			if err != nil {
				resource.LoggerTask.Error(ctx, "decrypt cert private key error", logit.String("app_id", app.AppID))
				return err
			}
			certDataString = cert.CertData
			certPrivateKeyString = pkPem
		}
	}

	reqList := make([]*xagent.AsyncRequest, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "update_cert",
				Params: &XagentRequest{
					EnvDir:         EnvJSONDir,
					WorkDir:        ProxyWorkDir,
					EnableTLS:      app.EnableTLS,
					CertData:       certDataString,
					CertPrivateKey: certPrivateKeyString,
				},
				Product:    vdbmodel.ProductVDB,
				TimeoutSec: 180,
			}
			reqList = append(reqList, req)
		}
	}

	g := gtask.Group{Concurrent: 5}
	for _, req := range reqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update cert fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update cert failed", logit.Error("err", err))
		return err
	}

	return nil
}
