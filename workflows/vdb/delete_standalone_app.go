/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/02/22 <EMAIL> create
 *
 **************************************************************************/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/delresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/dns"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/endpoint"
	securitygroup "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/security_group"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowDeleteStandaloneApp                 = "vdb-delete-standalone-app"
	StepDeleteStandaloneAppDeleteAppFromXmaster = "vdb-delete-standalone-app-delete-app-from-xmaster"
	StepDeleteStandaloneAppDeleteResource       = "vdb-delete-standalone-app-delete-resource"
	StepDeleteStandaloneAppDeleteDNS            = "vdb-delete-standalone-app-delete-dns"
	StepDeleteStandaloneAppDeleteEndpoint       = "vdb-delete-standalone-app-delete-endpoint"
	StepDeleteStandaloneAppDeleteBlb            = "vdb-delete-standalone-app-delete-Blb"
	StepDeleteStandaloneAppDeleteSecuritygroup  = "vdb-delete-standalone-app-delete-securitygroup"
	StepDeleteStandaloneAppCallbacks            = "vdb-delete-standalone-app-callbacks"
)

func init() {
	// 注册删除VDB standalone实例
	// 从xmaster删除集群
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppDeleteAppFromXmaster,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     xmaster.ProcessDeleteAppFromXmaster,
		SuccessNextStep: StepDeleteStandaloneAppDeleteResource,
		ErrorNextStep:   StepDeleteStandaloneAppDeleteAppFromXmaster,
	})

	// Step-1 删除Bcc、cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppDeleteResource,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     delresource.ProcessDelBccResources,
		SuccessNextStep: StepDeleteStandaloneAppDeleteDNS,
		ErrorNextStep:   StepDeleteStandaloneAppDeleteResource},

		workflow.WithMaxReentry(2, StepDeleteStandaloneAppDeleteDNS))

	// 删除 Dns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppDeleteDNS,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     dns.ProcessDeleteAppDomain,
		SuccessNextStep: StepDeleteStandaloneAppDeleteEndpoint,
		ErrorNextStep:   StepDeleteStandaloneAppDeleteDNS},

		workflow.WithMaxReentry(2, StepDeleteStandaloneAppDeleteEndpoint))

	// 删除 Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppDeleteEndpoint,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     endpoint.ProcessDeleteRwEndpoint,
		SuccessNextStep: StepDeleteStandaloneAppDeleteBlb,
		ErrorNextStep:   StepDeleteStandaloneAppDeleteEndpoint},

		workflow.WithMaxReentry(2, StepDeleteStandaloneAppDeleteBlb))

	// 删除 Blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppDeleteBlb,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     blb.ProcessDelBLB,
		SuccessNextStep: StepDeleteStandaloneAppDeleteSecuritygroup,
		ErrorNextStep:   StepDeleteStandaloneAppDeleteBlb},

		workflow.WithMaxReentry(2, StepDeleteStandaloneAppDeleteSecuritygroup))

	// 删除安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppDeleteSecuritygroup,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     securitygroup.ProcessDelSecurityGroup,
		SuccessNextStep: StepDeleteStandaloneAppCallbacks,
		ErrorNextStep:   StepDeleteStandaloneAppDeleteSecuritygroup},

		workflow.WithMaxReentry(2, StepDeleteStandaloneAppCallbacks))

	// 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteStandaloneAppCallbacks,
		Workflow:        WorkflowDeleteStandaloneApp,
		StepProcess:     callback.ProcessDeleteCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteStandaloneAppCallbacks,
	})
}
