/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package resize

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	ResizeFlag = "resized"
)

func ProcessResizeNodeCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster
	find := false

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusModifying {
				targetNode = node
				targetCluster = cluster
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target node", logit.String("nodeID", param.TargetNodeID))
		return errors.New("can not find target node")
	}

	if targetCluster.ActualDiskSize >= int(targetCluster.DiskSize) {
		resource.LoggerTask.Notice(ctx, "disk size no need resize cds", logit.String("nodeID", param.TargetNodeID))
		return nil
	}

	err = resizeDataNodeCds(ctx, app, targetCluster, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "resize cds error", logit.String("node", base_utils.Format(targetNode)), logit.Error("error", err))
		return err
	}

	return nil
}

func resizeDataNodeCds(ctx context.Context, app *vdbmodel.Application, cluster *vdbmodel.Cluster, node *vdbmodel.Node) error {
	toResizeVolume, err := getToResizeCds(ctx, app, cluster, node)
	if err != nil {
		return err
	}
	if len(toResizeVolume) == 0 {
		resource.LoggerTask.Notice(ctx, "no find need resize cds", logit.String("node", base_utils.Format(node)))
		return nil
	}

	for {
		select {
		case <-ctx.Done():
			return cerrs.ErrorTaskManual.Errorf("resize datanode %s cds timeout", node.NodeID)
		case <-time.After(1 * time.Second):
			// pass
		}
		op, err := doResizeCds(ctx, app, cluster, node, toResizeVolume)
		if err != nil {
			return err
		}
		if op == "retry" {
			continue
		}
		node.TempFlags = base_utils.FlagAdd(node.TempFlags, ResizeFlag)
		break
	}
	return nil
}

func doResizeCds(ctx context.Context, app *vdbmodel.Application, cluster *vdbmodel.Cluster, node *vdbmodel.Node, volumeID string) (string, error) {
	volumes, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
		VmID:      node.ResourceID,
		UserID:    app.UserID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cds info failed", logit.Error("error", err))
		return "", err
	}
	if len(volumes) == 0 {
		resource.LoggerTask.Warning(ctx, "get cds info failed, no volumes")
		return "", err
	}
	for _, volume := range volumes {
		if volume.Id == volumeID {
			if volume.Status != "InUse" {
				resource.LoggerTask.Notice(ctx, "cds is not in use, try next", logit.String("volume", base_utils.Format(volume)))
				return "retry", nil
			}
			if int64(volume.DiskSizeInGB) < cluster.DiskSize {
				err := bccresource.BccResourceOp().ResizeCds(ctx, &bccresource.ResizeCdsParam{
					VolumeID:           volumeID,
					UserID:             app.UserID,
					TargetDataDiskSize: int(cluster.DiskSize),
				})
				if err != nil {
					resource.LoggerTask.Warning(ctx, "send resize cds failed", logit.Error("error", err))
					return "", cerrs.ErrorTaskManual.Wrap(err)
				}
				resource.LoggerTask.Notice(ctx, "send resize cds success", logit.String("volume", base_utils.Format(volume)))
				if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
					resource.LoggerTask.Notice(ctx, "private dbstack adaptor env, skip check", logit.String("volume", base_utils.Format(volume)))
					return "", nil
				}

				return "retry", nil

			}
			if int64(volume.DiskSizeInGB) == cluster.DiskSize {
				resource.LoggerTask.Notice(ctx, "cds is already resize", logit.String("volume", base_utils.Format(volume)))
				return "", nil
			}
			// 理论上不会出现这个问题
			resource.LoggerTask.Warning(ctx, "cds is bigger than cluster", logit.String("volume", base_utils.Format(volume)))
			return "", errors.New("cds is bigger than cluster")
		}
	}
	resource.LoggerTask.Warning(ctx, "cds not found", logit.String("volumeId", volumeID))
	return "", fmt.Errorf("cds not found, cdsId %s", volumeID)
}

func getToResizeCds(ctx context.Context, app *vdbmodel.Application, cluster *vdbmodel.Cluster, node *vdbmodel.Node) (string, error) {
	vmInfo, err := bccresource.BccResourceOp().GetBccVmInfo(ctx, &bccresource.GetBccVmInfoRequest{
		VmID:      node.ResourceID,
		UserID:    app.UserID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get vm info failed", logit.Error("error", err))
		return "", err
	}
	resource.LoggerTask.Trace(ctx, "get vm info success", logit.String("vmInfo", base_utils.Format(vmInfo)))

	for _, volume := range vmInfo.Volumes {
		// 找到数据盘，当前数据盘大于目标数据盘，不可以使用cds resize
		if !volume.IsSystemVolume {
			if int64(volume.DiskSizeInGB) > cluster.DiskSize {
				resource.LoggerTask.Notice(ctx, "disk shrink, can not use cds resize")
				return "", nil
			}
			return volume.VolumeId, nil
		}
	}
	return "", errors.New("can not find data disk")
}

func ProcessResizeStandaloneCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			targetNode = node
			targetCluster = cluster
			break OuterLoop
		}
	}

	if targetCluster.ActualDiskSize >= int(targetCluster.DiskSize) {
		resource.LoggerTask.Notice(ctx, "disk size no need resize cds", logit.String("nodeID", targetNode.NodeID))
		return nil
	}

	err = resizeDataNodeCds(ctx, app, targetCluster, targetNode)
	if err != nil {
		resource.LoggerTask.Error(ctx, "resize cds error", logit.String("node", base_utils.Format(targetNode)), logit.Error("error", err))
		return err
	}

	return nil
}
