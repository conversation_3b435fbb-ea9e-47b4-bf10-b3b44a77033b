[EngineConfig]
EngineRunInterval = 300
EngineRunningTimeout = 10000
EngineLockTTL = 11000

[ScheduleConfig]
ScheduleRunInterval = 300
ScheduleRunningTimeout = 10000
ScheduleLockTTL = 11000

[WorkerConfig]
WorkerMaxReportErrorTime = 60000
WorkerHighPriorityCount = 50
WorkerMediumPriorityCount = 500
WorkerLowPriorityCount = 50
WorkerRunInterval = 300

[RetryConfig]
Level1RetryCount = 10
Level1RetryInterval = 3000
Level2RetryCount = 70
Level2RetryInterval = 10000
Level3RetryCount = 100
Level3RetryInterval = 60000
ManualRetryCount = 110

[HTTPServerConfig]
HTTPServerRunMode = "release"
HTTPServerPort = 7134