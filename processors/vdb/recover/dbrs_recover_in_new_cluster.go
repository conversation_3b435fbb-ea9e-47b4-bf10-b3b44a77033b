/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/16 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessDbrsRecoverInNewCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	appID := param.AppID

	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	appBackupID := param.RevoverParam.AppBackupID
	srcAppID := param.RevoverParam.SrcAppID

	// 执行数据恢复任务
	if err := processDbrsRecoverClusterTask(ctx, teu.TaskID, srcAppID, app, appBackupID); err != nil {
		resource.LoggerTask.Warning(ctx, "process recover in new cluster fail", logit.String("appID", teu.Entity),
			logit.Error("error", err))
		return err
	}

	return nil
}
