/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2021/12/29, by shangshuai02(<EMAIL>), first version
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/errors"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

// ProcessInitAppBLB 初始化appBLB
// 1. 创建BLB；如果app是ipv6的同时创建ipv6的BLB
// 2. 创建BLB Listener，Listener以及Backend均为app的port
func ProcessInitAppBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 1. 创建BLB并Save
	if err := createAppBLB(ctx, app); err != nil {
		return err
	}

	// 2. 创建BLB ListenPort，Listner以及Backend均为app的port
	if err := createAppListener(ctx, app, vdbmodel.BLBTypeApp); err != nil {
		return err
	}

	// 3. 创建blb ip group
	if err := createAppIPGroup(ctx, app, vdbmodel.BLBTypeApp); err != nil {
		return err
	}

	// 4. 创建ipgroup policy
	if err := createAppIPGroupPolicy(ctx, app, vdbmodel.BLBTypeApp); err != nil {
		return err
	}

	// 5. 创建监听器policy（绑定ipgroup）
	if err := createAppListenerPolicy(ctx, app, vdbmodel.BLBTypeApp); err != nil {
		return err
	}

	// 6. haproxy实现的blb,解析并保持port信息
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		if err := updateAppHaproxyPort(ctx, app); err != nil {
			return err
		}
	}

	return nil
}

// createBLB 创建BLB并Save
func createAppBLB(ctx context.Context, app *vdbmodel.Application) error {
	// 获取待创建的BLB列表
	var newBLBParams []*blbv2.CreateBLBParams
	var newBlbList []*vdbmodel.BLB
	var userID string
	var vpcID string
	var subnetID string

	/*
	   2024/03/14 appbuilder过来的用户无法做区分，因此所有用户都创建vip
	*/
	allocateVip := true
	/*
		userNeedAllocVipFromConfig := conf.NeedAllocVipBlbConfIns
		for _, specialUserID := range userNeedAllocVipFromConfig.SpecialUser {
			if app.UserID == specialUserID.UserID {
				allocateVip = true
			}
		}
	*/

	resource.LoggerTask.Trace(ctx, fmt.Sprintf("UserID=%s allocateVip=%t", app.UserID, allocateVip))

	// user master logicZone
	logicZone, err := getLogicZone(ctx, app)
	if err != nil {
		return err
	}

	// user master physicalZone
	physicalZone, err := getPhysicalZone(ctx, app)
	if err != nil {
		return err
	}

	for _, b := range app.BLBs {
		if len(b.BlbID) != 0 {
			continue
		}

		// 设置创建 blb 的环境参数
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
			vpcID = b.VpcID
			subnetID = b.SubnetID
			if b.RsRange == vdbmodel.BLBRsRangeAZone {
				logicZone = b.MasterAZ
			}
		} else {
			userID = b.ResourceUserID
			vpcID = b.ResourceVpcID
			subnetID = b.ResourceSubnetID
			// 使用资源池账号(资源账号或混合云管账号) 的 logicZone
			logicZone, err = getLogicZoneByPhysicalZone(ctx, userID, physicalZone)
			if err != nil {
				return err
			}
		}

		vpcExchange := &bccresource.ExchangeIDParams{
			ObjectType:  bcc.ExchangeIDObjectTypeVpc,
			InstanceIds: []string{vpcID},
			UserID:      userID,
		}
		vpcMapping, err := bccresource.BccResourceOp().ExchangeProductLongIDToShort(ctx, vpcExchange)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "vpcid long2short fail",
				logit.String("appId", app.AppID),
				logit.String("params", base_utils.Format(vpcExchange)),
			)
			return err
		}
		var vpcShortID string
		for idx := range vpcMapping {
			if vpcMapping[idx].ID == vpcID {
				vpcShortID = vpcMapping[idx].UUID
			}
		}

		subnetExchange := &bccresource.ExchangeIDParams{
			ObjectType:  bcc.ExchangeIDObjectTypeSubnet,
			InstanceIds: []string{subnetID},
			UserID:      userID,
		}
		subnetMapping, err := bccresource.BccResourceOp().ExchangeProductLongIDToShort(ctx, subnetExchange)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "subnetid long2short fail",
				logit.String("appId", app.AppID),
				logit.String("params", base_utils.Format(subnetExchange)),
			)
			return err
		}
		var subnetShortID string
		for idx := range subnetMapping {
			if subnetMapping[idx].ID == subnetID {
				subnetShortID = subnetMapping[idx].UUID
			}
		}

		///公有云当前blb分配vip，会在blb的global集群中创建，无法指定az
		layer4MasterAz := logicZone

		if allocateVip && !(privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix) {
			layer4MasterAz = ""
		}

		if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
			vpcShortID = vpcShortID + "-" + env.IDC()
		}

		newBLBParams = append(newBLBParams, &blbv2.CreateBLBParams{
			VpcID:          vpcShortID,
			SubnetID:       subnetShortID,
			Type:           vdbmodel.BLBTypeApp,
			Layer4MasterAz: layer4MasterAz,
			Name:           b.Name,
			AllocateVip:    allocateVip,
		})
		newBlbList = append(newBlbList, b)
	}

	if len(newBLBParams) == 0 {
		return nil
	}

	// 创建BLB
	createBlbParams := &blbv2.CreateBatchBLBParams{
		UserID:    userID,
		Product:   app.Product,
		BLBParams: newBLBParams,
	}
	blbList, err := blbv2.Instance().CreateBatchAppBLB(ctx, createBlbParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create blb fail",
			logit.String("appId", app.AppID),
			logit.String("params", base_utils.Format(createBlbParams)),
		)
		return errors.CreateBlbFail.Wrap(err)
	}

	// 更新数据库
	for k, b := range blbList {
		newBlbList[k].BlbID = b.BLBID
		newBlbList[k].Vip = ""
		newBlbList[k].Ovip = b.Address
		newBlbList[k].Ipv6 = ""
		newBlbList[k].Status = vdbmodel.BLBStatusAvailable
		newBlbList[k].Vip = b.UnderlayVip
	}
	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	if err := vdbmodel.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

// createListener 创建Listener
func createAppListener(ctx context.Context, app *vdbmodel.Application, blbType string) error {
	var BLBIDs []string
	var userID string

	for _, b := range app.BLBs {
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b.BlbID)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}

	totalCount := 0
	for _, blb := range BLBIDs {
		createListenerParams := &blbv2.CommonListenerParams{
			UserID:       userID,
			BlbID:        blb,
			ListenerPort: app.Port,
			Scheduler:    "RoundRobin",
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processCreateAppListener(ctx, createListenerParams)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app listeners",
			logit.String("appId", app.AppID),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doCreateAppListener", err))
		return err
	}

	return nil
}

// processCreateAppListener will check and create app blb listener
func processCreateAppListener(ctx context.Context, params *blbv2.CommonListenerParams) error {
	// desc listener
	listener, err := blbv2.Instance().DescAppTCPListener(ctx, params)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		resource.LoggerTask.Warning(ctx, "get app tcp listener failed,", logit.Error("err", err))
		return err
	}
	// listener exist
	if len(listener) > 0 {
		return nil
	}
	// create listener
	if err = blbv2.Instance().CreateAppTCPListener(ctx, params); err != nil {
		resource.LoggerTask.Warning(ctx, "create app tcp listener failed,", logit.Error("err", err))
		return err
	}
	return nil
}

// createAppIPGroup 创建ip group
func createAppIPGroup(ctx context.Context, app *vdbmodel.Application, blbType string) error {
	var BLBIDs []*vdbmodel.BLB
	var userID string

	for _, b := range app.BLBs {
		// 过滤已创建完成的 ip_group
		if len(b.IPGroupID) != 0 {
			continue
		}

		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	// 创建IPGroup
	BLBIDs2IPGroups := make(map[string]string, 0)
	for _, blb := range BLBIDs {
		b := blb
		createIPGroupParams := &blbv2.CommonBLBParams{
			UserID: userID,
			ElbID:  b.BlbID,
		}
		id, err := blbv2.Instance().CreateAppIPGroup(ctx, createIPGroupParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur create app ip group", logit.Error("err", err))
			return err
		}
		BLBIDs2IPGroups[b.BlbID] = id
	}

	// 更新blb model, 将ipGroup写入数据库
	for _, blb := range BLBIDs {
		blb.IPGroupID = BLBIDs2IPGroups[blb.BlbID]
	}
	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()

	if err := vdbmodel.BLBsSave(ctx, BLBIDs); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

// createAppIPGroupPolicy 创建ip group policy
func createAppIPGroupPolicy(ctx context.Context, app *vdbmodel.Application, blbType string) error {
	var BLBIDs []*vdbmodel.BLB
	var userID string
	for _, b := range app.BLBs {
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	// 创建IPGroup policy
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, blb := range BLBIDs {
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return subprocessCreateAppIPGroupPolicy(ctx, userID, blb.BlbID, blb.IPGroupID)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
			logit.String("appId", app.AppID),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("docreateAppIPGroupPolicy", err))
		return err
	}

	return nil
}

// createAppListenerPolicy 创建blb listener policy
func createAppListenerPolicy(ctx context.Context, app *vdbmodel.Application, blbType string) error {
	var BLBIDs []*vdbmodel.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}

	// 创建监听器 policy
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, blb := range BLBIDs {
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return subprocessCreateAppPolicys(ctx, userID, blb.BlbID, app.Port, blb.IPGroupID)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
			logit.String("appId", app.AppID),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("docreateAppIPGroupPolicy", err))
		return err
	}

	return nil
}

func subprocessCreateAppPolicys(ctx context.Context, userID string, blbID string, port int, IPGroupID string) error {
	// check
	listPolicysParams := &blbv2.ListAppPolicysParams{
		UserID: userID,
		BlbID:  blbID,
		Port:   port,
		Type:   "TCP",
	}
	policysList, err := blbv2.Instance().ListAppPolicys(ctx, listPolicysParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur list app policys",
			logit.String("blbID", blbID),
			logit.Int("port", port),
			logit.Error("ListAppPolicys", err))
		return err
	}

	if len(policysList) == 1 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("blb=%s policy is exist", blbID))
		return nil
	}

	// app policy
	appPolicy := &blbv2.AppPolicyVos{
		IPGroupID: IPGroupID,
		Priority:  100,
		Desc:      "",
		RuleList: []*blbv2.RuleList{
			{
				Key:   "*",
				Value: "*",
			},
		},
	}
	createPolicyParams := &blbv2.CreateAppPolicysParams{
		UserID:       userID,
		BlbID:        blbID,
		ListenerPort: port,
		Type:         "TCP",
		GroupType:    "ip",
		AppPolicyVos: []*blbv2.AppPolicyVos{appPolicy},
	}

	return blbv2.Instance().CreateAppPolicys(ctx, createPolicyParams)
}

func checkAppBLBStatus(ctx context.Context, app *vdbmodel.Application) error {
	var BLBIDs []*vdbmodel.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}
		if b.Type == vdbmodel.BLBTypeApp {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	for _, blb := range BLBIDs {
		count := 0
	for1:
		for {
			// 获取应用型BLB详情
			getParams := blbv2.CommonBLBParams{
				UserID: userID,
				ElbID:  blb.BlbID,
			}
			resp, err := blbv2.Instance().GetAppBLBDetail(ctx, &getParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get app blb faield", logit.Error("error", err))
				return err
			}

			switch resp.Status {
			case "avaiable":
				break for1
			case "creating":
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("blb %s status creating", blb.BlbID))
				count++
			default:
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("blb %s status not in creating or avaiable, create failed.", blb.BlbID))
				return errors.CreateBlbFail
			}
			select {
			case <-time.After(5 * time.Second):
				if count > 3 {
					resource.LoggerTask.Warning(ctx, fmt.Sprintf("blb %s create timeout", blb.BlbID))
					return errors.CreateBlbFail
				}
			}
		}
	}
	return nil
}

func subprocessCreateAppIPGroupPolicy(ctx context.Context, userID string, blbID string, IPGroupID string) error {
	// check(没有 ipgroup 详情接口，通过 ipgroup 列表查询)
	listIPGroupParams := &blbv2.CommonBLBParams{
		UserID: userID,
		ElbID:  blbID,
	}
	ipgroupList, err := blbv2.Instance().ListAppIPGroup(ctx, listIPGroupParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur list app ip group",
			logit.String("blbID", blbID),
			logit.Error("ListAppIPGroup", err))
		return err
	}

	var isIPGroupPolicyExist bool
	isIPGroupPolicyExist = false
	for _, ipgroup := range ipgroupList {
		if ipgroup.ID == IPGroupID && len(ipgroup.BackendPolicyList) == 1 {
			isIPGroupPolicyExist = true
		}
	}

	if isIPGroupPolicyExist {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("blb=%s ipgroup=%s ip_group_policy is exist", blbID, IPGroupID))
		return nil
	}

	// create
	createIPGroupPolicyParams := &blbv2.CommonIPGroupParams{
		UserID:    userID,
		BlbID:     blbID,
		IPGroupID: IPGroupID,
		Type:      "TCP",
	}
	return blbv2.Instance().CreateAppIPGroupPolicy(ctx, createIPGroupPolicyParams)
}

func updateAppHaproxyPort(ctx context.Context, app *vdbmodel.Application) error {
	if len(app.BLBs) == 0 {
		return nil
	}

	blb := app.BLBs[0]

	sp := strings.Split(blb.BlbID, "-")
	if len(sp) != 3 {
		resource.LoggerTask.Warning(ctx, "elbId format error", logit.String("elbId", blb.BlbID))
		return fmt.Errorf("elbId format error %s", blb.BlbID)
	}
	port, err := strconv.Atoi(sp[2])
	if err != nil {
		resource.LoggerTask.Warning(ctx, "elbId format error", logit.String("elbId", blb.BlbID))
		return fmt.Errorf("elbId format error %s", blb.BlbID)
	}

	app.Port = port

	return vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
}
