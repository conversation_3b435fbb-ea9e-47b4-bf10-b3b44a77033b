package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/opmonitor"
)

const (
	WorkflowCleanDeletedInstanceBns     = "vdb-clean-deleted-instance-bns"
	StepCleanDeletedInstanceBnsCleanBns = "vdb-clean-deleted-instance-bns-clean-bns"

	WorkflowCleanDeletedClusterBns     = "vdb-clean-deleted-cluster-bns"
	StepCleanDeletedClusterBnsCleanBns = "vdb-clean-deleted-cluster-bns-clean-bns"

	WorkflowCollectDataUsageForConsole    = "vdb-collect-data-usage-for-console"
	StepCollectDataUsageForConsoleCollect = "vdb-collect-data-usage-for-console-collect"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCleanDeletedInstanceBnsCleanBns,
		Workflow:        WorkflowCleanDeletedInstanceBns,
		StepProcess:     opmonitor.ProcessCleanToDeleteInstanceOpmonitorInstanceBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCleanDeletedInstanceBnsCleanBns},
		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, workflow.FinalStepError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCleanDeletedClusterBnsCleanBns,
		Workflow:        WorkflowCleanDeletedClusterBns,
		StepProcess:     opmonitor.ProcessCleanDeletedOpmonitorBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCleanDeletedClusterBnsCleanBns},
		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, workflow.FinalStepError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCollectDataUsageForConsoleCollect,
		Workflow:        WorkflowCollectDataUsageForConsole,
		StepProcess:     opmonitor.ProcessCollectDataUsageForConsole,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError},
		workflow.WithStepTimeout(5*time.Minute))
}
