/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:45
**/

package xmaster

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessUpdateAppTopologyInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app has not init in xmaster, skip update",
			logit.String("appId", app.AppID))
		return nil
	}

	if err := util.UpdateXMasterApplicationTopology(ctx, app.AppID); err != nil {
		resource.LoggerTask.Error(ctx, "update xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if err := util.SetXMasterDefaultMonitorStrategy(ctx, app.AppID); err != nil {
		resource.LoggerTask.Error(ctx, "set xmaster app monitor strategy failed", logit.Error("error", err))
		return err
	}
	if err := util.SetXMasterMonitorSwitch(ctx, app.AppID, true); err != nil {
		resource.LoggerTask.Error(ctx, "set xmaster app monitor switch failed", logit.Error("error", err))
		return err
	}

	if err := util.SetXMasterTaskFakeSwitch(ctx, teu.Entity, false); err != nil {
		resource.LoggerTask.Error(ctx, "switch on xmaster task fake switch failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessUpdateAppTopologyInXmasterForScale(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app has not init in xmaster, skip update",
			logit.String("appId", app.AppID))
		return nil
	}

	if err := util.UpdateXMasterApplicationTopology(ctx, app.AppID); err != nil {
		resource.LoggerTask.Error(ctx, "update xmaster app topology for shard scale in failed", logit.Error("error", err))
		return err
	}

	return nil
}
