/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/02, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Redis实现的Backend接口
*/

package backend

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/redis"
)

const (
	workerStatusKey string = "worker-status-key"
)

type redisBackend struct {
	redisClient redis.Client
}

func (rb *redisBackend) Report(ctx context.Context, taskExecRet *TaskExecResult) error {
	val, err := json.Marshal(taskExecRet)
	if err != nil {
		return errors.Wrap(err, "json marshal error")
	}
	return errors.Wrap(rb.redisClient.HSet(ctx, taskExecRet.TaskBatchID, taskExecRet.TaskExecUnitID, string(val)).Err(), "HSET error")
}

func (rb *redisBackend) GetReports(ctx context.Context, batchID string) ([]TaskExecResult, error) {
	hGetAll, err := rb.redisClient.HGetAll(ctx, batchID).Result()
	if err != nil && err != redis.ErrNil {
		return nil, errors.Wrap(err, "HGETALL error")
	}
	ret := make([]TaskExecResult, len(hGetAll))
	idx := 0
	for _, val := range hGetAll {
		if err := json.Unmarshal([]byte(val), &ret[idx]); err != nil {
			return nil, errors.Wrap(err, "json unmarshal error")
		}
		idx++
	}
	return ret, nil
}

func (rb *redisBackend) DeleteReports(ctx context.Context, batchID string) error {
	return errors.Wrap(rb.redisClient.Del(ctx, batchID).Err(), "DEL error")
}

func (rb *redisBackend) DeleteReport(ctx context.Context, taskExecRet *TaskExecResult) error {
	return errors.Wrap(rb.redisClient.HDel(ctx, taskExecRet.TaskBatchID, taskExecRet.TaskExecUnitID).Err(), "HDEL error")
}

func (rb *redisBackend) ReportWorkerStatus(ctx context.Context, workerStatus *WorkerStatus) error {
	return errors.Wrap(rb.redisClient.HSet(ctx, workerStatusKey, workerStatus.WorkerID, workerStatus.Status).Err(), "HSET error")
}

func (rb *redisBackend) GetWorkerStatus(ctx context.Context) ([]WorkerStatus, error) {
	hGetAll, err := rb.redisClient.HGetAll(ctx, workerStatusKey).Result()
	if err != nil {
		return nil, errors.Wrap(err, "HGETALL error")
	}
	ret := make([]WorkerStatus, len(hGetAll))
	idx := 0
	for key, val := range hGetAll {
		ret[idx].WorkerID = key
		ret[idx].Status = val
		idx++
	}
	return ret, nil
}

func NewRedisBackend(redisClient redis.Client) Backend {
	return &redisBackend{
		redisClient: redisClient,
	}
}
