package deploy

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
)

// redis ping 测试配置
const (
	pingTimeoutSec = 300

	InstanceEngineKey           = "ENGINE"
	InstanceEngineValueDataNode = "datanode"
	InstanceEngineValueProxy    = "proxy"
	InstanceEngineValueMaster   = "master"
)

// deploy 配置
var (
	configWaitReady = &deploy.ConfigWaitReady{
		Timeout:    15 * time.Minute,
		RetryDelay: 500 * time.Millisecond,
	}

	configWaitTask = &deploy.ConfigWaitTask{
		Timeout:             15 * time.Minute,
		RetryDelayOnFail:    500 * time.Millisecond,
		RetryDelayOnRunning: 500 * time.Millisecond,
	}
)
