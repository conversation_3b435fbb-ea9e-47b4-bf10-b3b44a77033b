/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *2023/11/13 <EMAIL>
 *
 **************************************************************************/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/backup"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/delresource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/dns"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/endpoint"
	securitygroup "icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/security_group"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowDeleteClusterApp                 = "vdb-delete-cluster-app"
	StepDeleteClusterAppDeleteAppFromXmaster = "vdb-delete-cluster-app-delete-app-from-xmaster"
	StepDeleteClusterAppDeleteResource       = "vdb-delete-cluster-app-delete-resource"
	StepDeleteClusterAppDeleteDNS            = "vdb-delete-cluster-app-delete-dns"
	StepDeleteClusterAppDeleteEndpoint       = "vdb-delete-cluster-app-delete-endpoint"
	StepDeleteClusterAppDeleteBlb            = "vdb-delete-cluster-app-delete-Blb"
	StepDeleteClusterAppDeleteBackupPolicy   = "vdb-delete-cluster-app-delete-backup-policy"
	StepDeleteClusterAppDeleteSecuritygroup  = "vdb-delete-cluster-app-delete-securitygroup"
	StepDeleteClusterAppCallbacks            = "vdb-delete-cluster-app-callbacks"
)

func init() {
	// 注册删除SCS实例
	// 从xmaster删除集群
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteAppFromXmaster,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     xmaster.ProcessDeleteAppFromXmaster,
		SuccessNextStep: StepDeleteClusterAppDeleteResource,
		ErrorNextStep:   StepDeleteClusterAppDeleteAppFromXmaster,
	})

	// Step-1 删除instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteResource,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     delresource.ProcessDelBccResources,
		SuccessNextStep: StepDeleteClusterAppDeleteDNS,
		ErrorNextStep:   StepDeleteClusterAppDeleteResource},

		workflow.WithMaxReentry(2, StepDeleteClusterAppDeleteDNS))

	// 删除 Dns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteDNS,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     dns.ProcessDeleteAppDomain,
		SuccessNextStep: StepDeleteClusterAppDeleteEndpoint,
		ErrorNextStep:   StepDeleteClusterAppDeleteDNS},

		workflow.WithMaxReentry(2, StepDeleteClusterAppDeleteEndpoint))

	// 删除 Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteEndpoint,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     endpoint.ProcessDeleteRwEndpoint,
		SuccessNextStep: StepDeleteClusterAppDeleteBlb,
		ErrorNextStep:   StepDeleteClusterAppDeleteEndpoint},

		workflow.WithMaxReentry(2, StepDeleteClusterAppDeleteBlb))

	// 删除 Blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteBlb,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     blb.ProcessDelBLB,
		SuccessNextStep: StepDeleteClusterAppDeleteBackupPolicy,
		ErrorNextStep:   StepDeleteClusterAppDeleteBlb},

		workflow.WithMaxReentry(2, StepDeleteClusterAppDeleteBackupPolicy))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteBackupPolicy,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     backup.ProcessDeleteBackupPolicy,
		SuccessNextStep: StepDeleteClusterAppDeleteSecuritygroup,
		ErrorNextStep:   StepDeleteClusterAppDeleteBackupPolicy},

		workflow.WithMaxReentry(2, StepDeleteClusterAppDeleteSecuritygroup))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppDeleteSecuritygroup,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     securitygroup.ProcessDelSecurityGroup,
		SuccessNextStep: StepDeleteClusterAppCallbacks,
		ErrorNextStep:   StepDeleteClusterAppDeleteSecuritygroup},

		workflow.WithMaxReentry(2, StepDeleteClusterAppCallbacks))

	// 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteClusterAppCallbacks,
		Workflow:        WorkflowDeleteClusterApp,
		StepProcess:     callback.ProcessDeleteCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteClusterAppCallbacks,
	})
}
