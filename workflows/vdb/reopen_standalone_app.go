/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* reopen_standalone_app.go - File Description */
/*
Modification History
--------------------
2024/02/27, by Fangjinxia, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
)

const (
	WorkflowReopenStandalone     = "vdb-reopen-standalone-app"
	StepReopenStandaloneRebindRs = "vdb-reopen-standalone-app-rebind-rs"
	StepReopenStandaloneCallback = "vdb-reopen-standalone-app-cb"
)

func init() {
	// Step-1 重新挂载所有rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenStandaloneRebindRs,
		Workflow:        WorkflowReopenStandalone,
		StepProcess:     blb.ProcessRebindStandaloneRs,
		SuccessNextStep: StepReopenStandaloneCallback,
		ErrorNextStep:   StepReopenStandaloneRebindRs,
	})

	// Step-2 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenStandaloneCallback,
		Workflow:        WorkflowReopenStandalone,
		StepProcess:     callback.ProcessReOpenCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepReopenStandaloneCallback,
	})
}
