/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2025/02/25 zeng<PERSON>gy<PERSON>@baidu.com Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dbrs"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func loadDbrsRestoreServiceID(ctx context.Context, taskID string, appID string) (string, error) {
	key := "dbrs_restore_:" + taskID + ":" + appID
	restoreServiceID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return restoreServiceID, nil
}

func saveDbrsRestoreServiceID(ctx context.Context, taskID string, appID string, restoreServiceID string) error {
	key := "dbrs_restore_:" + taskID + ":" + appID
	if err := resource.RedisClient.SetNX(ctx, key, restoreServiceID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteDbrsRestoreServiceID(ctx context.Context, taskID string, appID string) error {
	key := "dbrs_restore_:" + taskID + ":" + appID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processDbrsRecoverClusterTask(ctx context.Context, taskID string,
	sourceAppID string, app *vdbmodel.Application, appBackupID string) error {
	// 尝试从 缓存 中获取 restoreServiceID
	restoreServiceID, err := loadDbrsRestoreServiceID(ctx, taskID, app.AppID)
	if err == nil && len(restoreServiceID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have create restoreServiceID: %s", restoreServiceID))
	}

	// 请求 DBRS 创建恢复任务
	dbrsComponent := dbrs.DbrsResourceOp()
	if restoreServiceID == "" {
		createRestoreParams := dbrs.CreateRestoreParams{
			DataType:           "vdb",
			SourceAppID:        sourceAppID,
			SourceDataBackupID: appBackupID,
			DestAppID:          app.AppID,
			DbaasType:          "vdb",
		}

		restoreServiceID, err = dbrsComponent.CreateBackupRestore(ctx, &createRestoreParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "create dbrs backup restore fail", logit.Error("err", err))
			return err
		}

		if err := saveDbrsRestoreServiceID(ctx, taskID, app.AppID, restoreServiceID); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("save restoreServiceID fail"), logit.Error("error", err))
		}
	}

	// 查询 DBRS 恢复任务状态
	for {
		queryParams := dbrs.QueryRestoreParams{
			DataType:                   "vdb",
			DataBackupRestoreServiceID: restoreServiceID,
		}
		restoreServiceStatus, err := dbrsComponent.QueryBackupRestore(ctx, &queryParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "query dbrs backup restore fail", logit.Error("err", err))
			return err
		}

		if restoreServiceStatus == "doing" {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(10 * time.Second):
				continue
			}
		}

		if restoreServiceStatus == "failed" {
			resource.LoggerTask.Warning(ctx, "dbrs restore result is failed")
			return errors.New("dbrs restore result is failed")
		}

		if restoreServiceStatus == "success" {
			break
		}
	}

	return nil
}
