/* Copyright 2025 Baidu Inc. All Rights Reserved. */
/* switch_cluster_entrance.go - File Description */
/*
Modification History
--------------------
2025/3/7, by z<PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/blb"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
)

const (
	WorkflowSwitchClusterEntrance      = "vdb-switch-cluster-entrance"
	StepSwitchClusterEntranceBuildMeta = "vdb-switch-cluster-entrance-build-meta"
	StepUnbindFirstClusterRs           = "vdb-switch-cluster-entrance-unbind-first-cluster-rs"
	StepUnbindSecondClusterRs          = "vdb-switch-cluster-entrance-unbind-second-cluster-rs"
	StepbindSecondClusterNewRs         = "vdb-switch-cluster-entrance-bind-second-cluster-new-rs"
	StepbindFirstClusterNewRs          = "vdb-switch-cluster-entrance-bind-first-cluster-new-rs"
	StepSwitchClusterEntranceCallbacks = "vdb-switch-cluster-entrance-callbacks"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchClusterEntranceBuildMeta,
		Workflow:        WorkflowSwitchClusterEntrance,
		StepProcess:     buildmeta.ProcessBuildMetaForSwitchEntrance,
		SuccessNextStep: StepUnbindFirstClusterRs,
		ErrorNextStep:   StepSwitchClusterEntranceBuildMeta,
	})

	// Step-2 摘除第一集群的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnbindFirstClusterRs,
		Workflow:        WorkflowSwitchClusterEntrance,
		StepProcess:     blb.ProcessSwitchEntranceUnbindFirstAppAllProxys,
		SuccessNextStep: StepUnbindSecondClusterRs,
		ErrorNextStep:   StepUnbindFirstClusterRs,
	})

	// Step-3 摘除第二集群的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnbindSecondClusterRs,
		Workflow:        WorkflowSwitchClusterEntrance,
		StepProcess:     blb.ProcessSwitchEntranceUnbindSecondAppAllProxys,
		SuccessNextStep: StepbindSecondClusterNewRs,
		ErrorNextStep:   StepUnbindSecondClusterRs,
	})

	// Step-4 重新挂载第二个集群的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepbindSecondClusterNewRs,
		Workflow:        WorkflowSwitchClusterEntrance,
		StepProcess:     blb.ProcessSwitchEntranceRebindSecondRsToFirstBlb,
		SuccessNextStep: StepbindFirstClusterNewRs,
		ErrorNextStep:   StepbindSecondClusterNewRs,
	})

	// Step-5 重新挂载第一个集群的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepbindFirstClusterNewRs,
		Workflow:        WorkflowSwitchClusterEntrance,
		StepProcess:     blb.ProcessSwitchEntranceRebindFirstRsToSecondBlb,
		SuccessNextStep: StepSwitchClusterEntranceCallbacks,
		ErrorNextStep:   StepbindFirstClusterNewRs,
	})

	// Step-6 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchClusterEntranceCallbacks,
		Workflow:        WorkflowSwitchClusterEntrance,
		StepProcess:     callback.ProcessSwitchEntranceCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSwitchClusterEntranceCallbacks,
	})
}
