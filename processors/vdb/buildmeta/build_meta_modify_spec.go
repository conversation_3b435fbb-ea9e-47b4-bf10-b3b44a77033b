/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
VDB纵向扩缩容过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessBuildMetaForModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusModifying
	for _, cluster := range app.Clusters {
		cluster.DestSpec = param.DataNodeSpec
		for _, node := range cluster.Nodes {
			node.Status = vdbmodel.NodeOrProxyStatusToModify
		}
	}
	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForModifyNodeSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

OutLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusToModify {
				node.Status = vdbmodel.NodeOrProxyStatusModifying
				break OutLoop
			}
		}
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForSwitchEntrance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	firstAppID := param.SwitchEntranceParam.FirstAppID
	SecondAppID := param.SwitchEntranceParam.SecondAppID

	firstApp, err := vdbmodel.ApplicationGetByAppID(ctx, firstAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get first app fail", logit.String("appId", firstAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	SecondApp, err := vdbmodel.ApplicationGetByAppID(ctx, SecondAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get second app fail", logit.String("appId", SecondAppID),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	firstApp.Status = vdbmodel.AppStatusModifying
	SecondApp.Status = vdbmodel.AppStatusModifying

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{firstApp}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{SecondApp}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}
