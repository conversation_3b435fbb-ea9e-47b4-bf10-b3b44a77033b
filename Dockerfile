# 若没有特殊的需求，也可以使用 alpine 镜像，更精简
# https://ku.baidu-int.com/d/PAZzscWj9ewBg_
# FROM iregistry.baidu-int.com/baidu-base/alpine:latest

FROM iregistry.baidu-int.com/baidu-base/centos:7.5

# https://cloud.baidu-int.com/icloud/Appspace/最佳实践/使用GDP创建应用/

# 复制 output 下所有文件到工作目录，不包括 output 目录本身
COPY --chown=work output/ /home/<USER>/

# 为 CNAP 生成一个 port.conf
RUN echo -e "LISTEN_PORT : 8080\nPORT_COUNT: 20">> /home/<USER>/conf/port.conf

EXPOSE 8080

#  进入应用的根目录
WORKDIR /home/<USER>

CMD ["bin/x1-task-manager"]
