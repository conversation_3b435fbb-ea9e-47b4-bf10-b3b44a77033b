/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/02 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	ProxyNodeWorkDir = "/home/<USER>/mochow-proxy"
)

func doRestartProxyNode(ctx context.Context, proxy *vdbmodel.Proxy) error {
	mochowDir := ProxyNodeWorkDir

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: proxy.FloatingIP,
			Port: int32(proxy.XagentPort),
		},
		Action: "restart",
		Params: &restartXagentRequest{
			WorkDir: mochowDir,
		},
		Product: vdbmodel.ProductVDB,
	}

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "restart mochow-prxoy fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
			logit.Error("err", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "restart mochow-proxy success", logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)))

	return nil
}
