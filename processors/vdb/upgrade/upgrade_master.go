/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package upgrade

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	apisdkiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

func ProcessUpgradeAllMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("app %s ProcessUpgradeAllMaster", appID))

	for {
		complete := true
		if err := CheckSubTask(ctx, teu); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "subtask status check failed", logit.Error("err", err))
		} else {
			if err := createUpgradeMasterSubTask(ctx, teu, appID); err != nil {
				complete = false
				resource.LoggerTask.Notice(ctx, "create upgrade master sub tasks failed", logit.Error("err", err))
			}
		}

		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			continue
		}
	}
}

func CheckSubTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	if subTasksStatus != taskIface.SubTasksStatusSuccess {
		resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
		return fmt.Errorf("sub tasks status is not success")
	}

	return nil
}

func createUpgradeMasterSubTask(ctx context.Context, teu *workflow.TaskExecUnit, appID string) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	//寻找是否还有未修改的节点
	var TgtMaster *vdbmodel.Master
	find := false
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToUpgrade {
			TgtMaster = master
			find = true
		}
	}

	if !find {
		resource.LoggerTask.Notice(ctx, "all master is updated")
		return nil
	}

	param, err := apisdkiface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	subParams := param
	subParams.TargetNodeID = TgtMaster.MasterID
	subParams.TargetNodeEngine = vdbmodel.EngineVDBMaster

	err = resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowUpgradeNode,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "-" + TgtMaster.MasterID,
		Entity:     teu.Entity,
		Parameters: subParams,
	}})
	if err != nil {
		resource.LoggerTask.Error(ctx, "create sub tasks error", logit.Error("error", err))
		return err
	}

	return fmt.Errorf("sub tasks is created ")
}
