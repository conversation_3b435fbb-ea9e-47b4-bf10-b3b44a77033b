package mountdisk

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	DataDiskTargetMountDir = "/mnt"
)

type XagentRequest struct {
	VolumeID       string `json:"volume_id"`
	TargetMountDir string `json:"target_mount_dir"`
}

func ProcessMountCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to mount disk in non-standalone mode")
		return nil
	}

	toCreateNodes := make([]*vdbmodel.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			toCreateNodes = append(toCreateNodes, node)
		}
	}

	if len(toCreateNodes) == 0 {
		resource.LoggerTask.Notice(ctx, "no tocreate node found")
		return errors.New("tocreate node not found")
	}
	if len(toCreateNodes) > 1 {
		resource.LoggerTask.Error(ctx, "too many tocreate nodes.", logit.String("toCreateNodes", base_utils.Format(toCreateNodes)))
		return errors.New("too many tocreate nodes")
	}
	volumeID := toCreateNodes[0].VolumeID

	if volumeID == "" {
		resource.LoggerTask.Error(ctx, "get volume id in tocreate node error", logit.String("appId", app.AppID),
			logit.Error("error", err))
		return err
	}

	reqList := make([]*xagent.AsyncRequest, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != vdbmodel.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "mount_disk",
				Params: &XagentRequest{
					VolumeID:       volumeID,
					TargetMountDir: DataDiskTargetMountDir,
				},
				Product: vdbmodel.ProductVDB,
			}
			reqList = append(reqList, req)
		}
	}

	g := gtask.Group{Concurrent: 5}
	for _, req := range reqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "mount disk fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "mount disk failed", logit.Error("err", err))
		return err
	}

	return nil
}
