package dns

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dns"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessDeleteAppDomain 删除app域名
func ProcessDeleteAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return fmt.Errorf("DeleteAppDomain fail : teu is nilptr")
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	enableIpv6 := false
	if app.IPType == vdbmodel.Ipv6 {
		enableIpv6 = true
	}

	if len(app.BLBs) == 0 {
		resource.LoggerTask.Warning(ctx, "no blb", logit.String("appId", teu.Entity))
	}
	bindIP := ""
	bindIPV6 := ""
	for _, blb := range app.BLBs {
		if len(blb.EndpointIP) != 0 {
			bindIP = blb.EndpointIP
			enableIpv6 = false
			continue
		}
		if blb.IPType == vdbmodel.Ipv4 {
			bindIP = blb.Ovip
		}
		if blb.IPType == vdbmodel.Ipv6 {
			bindIPV6 = blb.Ipv6
		}
	}

	// 删除域名在公网环境的解析
	err = DeleteAppInetDomain(ctx, app.Domain, bindIP, app.UserID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete inet domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	deleteDomainParam := dns.DeleteDomainParam{
		UserID:     app.UserID,
		VpcID:      app.VpcID,
		BindIp:     bindIP,
		Domain:     app.Domain,
		EnableIpv6: enableIpv6,
		BindIpIpv6: bindIPV6,
	}
	err = dns.Instance().DeleteDomain(ctx, &deleteDomainParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	// todo actionrecordmodel ?
	return nil
}

func DeleteAppInetDomain(ctx context.Context, domain string, bindIP string, userID string) (err error) {
	parts := strings.Split(domain, ".")
	if len(parts) <= 3 {
		resource.LoggerTask.Warning(ctx, "delete inet domain faield invalid domain",
			logit.String("app.Domain", domain))
		return nil
	}
	parts = parts[:len(parts)-3]
	name := strings.Join(parts, ".")

	deleteInetDomainParam := &dns.DeleteInetDomainParam{
		Domain: name,
		Eip:    bindIP,
		UserID: userID,
	}
	if err := dns.Instance().DeleteInetDomain(ctx, deleteInetDomainParam); err != nil {
		resource.LoggerTask.Warning(ctx, "delete inet domain failed",
			logit.String("app.Domain", domain), logit.Error("ComponentError", err))
		return nil
	}

	return nil
}
