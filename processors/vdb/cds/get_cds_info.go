package cds

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessSetDataCds 填充数据盘volumeid到db
// 1. 遍历tocreate/todelete node，获取bcc资源id
// 2. 调用bcc接口获取数据盘volume id
func ProcessGetDataCds(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.Type != vdbmodel.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "no need to set cds volume id in non-standalone mode")
		return nil
	}

	err = fillToCreateNodeVolumeID(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "fill tocreate node volume id error", logit.String("appId", app.AppID),
			logit.Error("error", err))
		return err
	}
	err = fillToDeleteNodeVolumeID(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "fill todelete node volume id error", logit.String("appId", app.AppID),
			logit.Error("error", err))
		return err
	}

	return nil
}

func fillToCreateNodeVolumeID(ctx context.Context, app *vdbmodel.Application) error {
	toCreateNode, err := getToCreateNode(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get tocreate node error", logit.String("appId", app.AppID), logit.Error("error", err))
		return err
	}

	bccResourceID := toCreateNode.ResourceID
	volumeID := toCreateNode.VolumeID
	if volumeID != "" {
		resource.LoggerTask.Notice(ctx, "volume id has been set", logit.String("volumeID", volumeID),
			logit.String("bccResourceID", bccResourceID))
		return nil
	}

	volumeIds, err := getVolumeAttachments(ctx, bccResourceID, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cds for bcc resource failed", logit.Error("error", err))
		return err
	}

	if len(volumeIds) == 0 {
		resource.LoggerTask.Warning(ctx, "no volume found")
		return errors.New("volume not found")
	}
	if len(volumeIds) > 1 {
		resource.LoggerTask.Warning(ctx, "too many cds volumes for bcc resource")
		return errors.New("too many cds volumes for bcc resource")
	}

	toCreateNode.VolumeID = volumeIds[0]
	if err := vdbmodel.NodesSave(ctx, []*vdbmodel.Node{toCreateNode}); err != nil {
		resource.LoggerTask.Error(ctx, "save node error", logit.Error("error", err))
		return err
	}

	return nil
}

func fillToDeleteNodeVolumeID(ctx context.Context, app *vdbmodel.Application) error {
	toDeleteNode, err := getToDeleteNode(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get todelete node error", logit.String("appId", app.AppID),
			logit.Error("error", err))
		return err
	}

	bccResourceID := toDeleteNode.ResourceID
	volumeID := toDeleteNode.VolumeID
	if volumeID != "" {
		resource.LoggerTask.Notice(ctx, "volume id has been set", logit.String("volumeID", volumeID),
			logit.String("bccResourceID", bccResourceID))
		return nil
	}

	volumeIds, err := getVolumeAttachments(ctx, bccResourceID, app.UserID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cds for bcc resource failed", logit.Error("error", err))
		return err
	}

	if len(volumeIds) == 0 {
		resource.LoggerTask.Warning(ctx, "no volume found")
		return errors.New("volume not found")
	}
	if len(volumeIds) > 1 {
		resource.LoggerTask.Warning(ctx, "too many cds volumes for bcc resource")
		return errors.New("too many cds volumes for bcc resource")
	}

	toDeleteNode.VolumeID = volumeIds[0]
	if err := vdbmodel.NodesSave(ctx, []*vdbmodel.Node{toDeleteNode}); err != nil {
		resource.LoggerTask.Error(ctx, "save node error", logit.Error("error", err))
		return err
	}

	return nil
}

// getCdsVolume 获取cds volume id
func getVolumeAttachments(ctx context.Context, bccResourceID string, userID string) ([]string, error) {
	resp, err := bccresource.BccResourceOp().GetBccVmInfo(ctx, &bccresource.GetBccVmInfoRequest{
		VmID:      bccResourceID,
		UserID:    userID,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get bcc resource info failed", logit.Error("error", err))
		return nil, err
	}

	nonSystemVolumes := make([]string, 0)
	for _, volume := range resp.Volumes {
		if volume.IsSystemVolume {
			continue
		}
		nonSystemVolumes = append(nonSystemVolumes, volume.VolumeId)
	}

	return nonSystemVolumes, nil
}
