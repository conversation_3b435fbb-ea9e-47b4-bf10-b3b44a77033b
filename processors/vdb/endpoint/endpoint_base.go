/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2022/06/27, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
服务网卡
*/

package endpoint

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbV2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	endpointComponent "icode.baidu.com/baidu/scs/x1-base/component/neutron/endpoint"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

// 创建服务发布点, 并记录到数据库中
func createServicePublishEndpoint(ctx context.Context, app *vdbmodel.Application, roGroupID string) (err error) {
	var newBlbList []*vdbmodel.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.BlbID) == 0 {
			continue
		}

		// 如果 ServicePublishEndpoint 非空，则无需再次创建
		if len(b.ServicePublishEndpoint) != 0 {
			continue
		}

		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}

		var params blbV2.CreatePublishEndpointParams
		params.UserID = userID
		// 大小写字母、数字长度1-65
		params.ServiceName = "vdb"
		params.ElbID = b.BlbID
		params.AllowUserId = app.UserID
		service, err := blbV2.Instance().CreatePublishEndpoint(ctx, &params)
		if err != nil {
			return err
		}
		b.ServicePublishEndpoint = service
		newBlbList = append(newBlbList, b)
	}
	resource.LoggerTask.Notice(ctx, "execute ApplyServicePublishEndpoint func ok",
		logit.String("appid", app.AppID),
		logit.String("blb", base_utils.Format(newBlbList)),
	)
	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	// 更新数据库
	if len(newBlbList) > 0 {
		if err := vdbmodel.BLBsSave(ctx, newBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}

// 创建服务网卡, 并记录到数据库中
func createEndpoint(ctx context.Context, app *vdbmodel.Application, roGroupID string) error {
	var newBlbList []*vdbmodel.BLB
	for _, b := range app.BLBs {
		if len(b.BlbID) == 0 {
			continue
		}

		// 如果 b.EndpointId 非空，则无需再次创建
		if len(b.EndpointID) != 0 {
			continue
		}

		var params endpointComponent.CreateEndpointParams
		// 用户的 vpc 等信息
		params.VpcID = b.VpcID
		params.SubnetID = b.SubnetID
		params.Service = b.ServicePublishEndpoint
		params.UserID = app.UserID
		params.Product = vdbmodel.ProductVDB

		endpointID, IPAddr, err := endpointComponent.Instance().CreateEndpoint(ctx, &params)
		if err != nil {
			return err
		}
		b.EndpointID = endpointID
		b.EndpointIP = IPAddr
		newBlbList = append(newBlbList, b)
	}
	resource.LoggerTask.Notice(ctx, "execute ApplyEndpoint func ok",
		logit.String("appid", app.AppID),
		logit.String("blb", base_utils.Format(newBlbList)),
	)

	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForVdbModeModify(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for vdbmodel modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	// 更新数据库
	if len(newBlbList) > 0 {
		if err := vdbmodel.BLBsSave(ctx, newBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}

// 删除服务发布点
func deleteServicePublishEndpoint(ctx context.Context, app *vdbmodel.Application, roGroupID string, onlyEntrance bool) (err error) {
	var newBlbList []*vdbmodel.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.ServicePublishEndpoint) == 0 {
			continue
		}

		if len(b.ResourceUserID) == 0 {
			userID = app.UserID
		} else {
			userID = b.ResourceUserID
		}

		var params blbV2.DeletePublishEndpointParams
		params.UserID = userID
		params.Service = b.ServicePublishEndpoint

		err := blbV2.Instance().DeletePublishEndpoint(ctx, &params)
		if err != nil {
			return err
		}
		b.ServicePublishEndpoint = ""
		newBlbList = append(newBlbList, b)
	}
	// 无需删除服务网卡
	if len(newBlbList) == 0 {
		return nil
	}

	// 更新数据库
	if err := vdbmodel.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

// 删除服务网卡
func deleteEndpoint(ctx context.Context, app *vdbmodel.Application, roGroupID string, onlyEntrance bool) error {
	var newBlbList []*vdbmodel.BLB
	for _, b := range app.BLBs {
		if len(b.EndpointID) == 0 {
			continue
		}

		var params endpointComponent.CommonEndpointParams
		params.UserID = app.UserID
		params.EndpointID = b.EndpointID

		err := endpointComponent.Instance().DeleteEndpoint(ctx, &params)
		if err != nil {
			return err
		}

		b.EndpointID = ""
		b.EndpointIP = ""
		newBlbList = append(newBlbList, b)
	}

	// 无需删除服务网卡
	if len(newBlbList) == 0 {
		return nil
	}

	// 更新数据库
	if err := vdbmodel.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}
