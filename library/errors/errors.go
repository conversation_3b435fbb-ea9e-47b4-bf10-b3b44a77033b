package errors

import "icode.baidu.com/baidu/scs/x1-base/common/cerrs"

var (
	CreateBlbFail      = cerrs.NewConst(cerrs.CODE_USER_BASE+1, "create blb fail", true, nil)
	DeleteBlbFail      = cerrs.NewConst(cerrs.CODE_USER_BASE+2, "delete blb fail", true, nil)
	CreateListenerFail = cerrs.NewConst(cerrs.CODE_USER_BASE+3, "create listener fail", true, nil)
	BindRsFail         = cerrs.NewConst(cerrs.CODE_USER_BASE+4, "bind rs fail", true, nil)
	UnBindRsFail       = cerrs.NewConst(cerrs.CODE_USER_BASE+5, "unbind rs fail", true, nil)
	UnbindEIPFail      = cerrs.NewConst(cerrs.CODE_USER_BASE+10, "unbind eip fail", true, nil)
	BackupInProgress   = cerrs.NewConst(cerrs.CODE_USER_BASE+11, "backup in progress", false, nil)
	XmasterQueryFail   = cerrs.NewConst(cerrs.CODE_USER_BASE+12, "xmaster query fail", false, nil)
	XmasterOpFail      = cerrs.NewConst(cerrs.CODE_USER_BASE+13, "xmaster op fail", true, nil)
)
