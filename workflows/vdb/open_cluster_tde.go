/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* open_cluster_tde.go - the controller of open cluster tde  */
/*
modification history
--------------------
2024/09/2, by <PERSON><PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
执行节点升级

Parameters
{
	"AppID": "vdb-bj-nxewpztnsreg",
	"UpgradeParam":  ..
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/callback"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/mochow"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/tde"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/xmaster"
)

const (
	WorkflowOpenClusterTDE             = "vdb-open-cluster-tde"
	StepOpenClusterTDEBuildMeta        = "vdb-open-cluster-tde-build-meta"
	StepOpenClusterTDECloseHa          = "vdb-open-cluster-tde-close-ha"
	StepOpenClusterTDEMasterSubTasks   = "vdb-open-cluster-tde-master-sub-tasks"
	StepOpenClusterTDEDatanodeSubTasks = "vdb-open-cluster-tde-datanode-sub-tasks"
	StepOpenClusterTDERecoverHa        = "vdb-open-cluster-tde-recover-ha"
	StepOpenClusterTDECallback         = "vdb-open-cluster-tde-cb"

	StepOpenClusterTDERollback          = "vdb-open-cluster-tde-rollback"
	StepOpenClusterTDERollbackRecoverHa = "vdb-open-cluster-tde-rollback-recover-ha"

	WorkflowOpenNodeTDE      = "vdb-open-node-tde"
	StepOpenNodeTDEBuildMeta = "vdb-open-node-tde-build-meta"
	StepOpenNodeTDEUpdate    = "vdb-open-node-tde-update"
	StepOpenNodeTDECheck     = "vdb-open-node-tde-check"
	StepOpenNodeTDECallback  = "vdb-open-node-tde-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// Step-0 构建元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDEBuildMeta,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     buildmeta.ProcessBuildMetaForOpenClusterTDE,
		SuccessNextStep: StepOpenClusterTDECloseHa,
		ErrorNextStep:   StepOpenClusterTDEBuildMeta,
	})

	// Step-1 关闭HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDECloseHa,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     xmaster.ProcessCloseAppHaStatusInXmaster,
		SuccessNextStep: StepOpenClusterTDEMasterSubTasks,
		ErrorNextStep:   StepOpenClusterTDECloseHa,
	})

	// Step-2 执行master节点的更新
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDEMasterSubTasks,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     tde.ProcessOpenAllMasterTDE,
		SuccessNextStep: StepOpenClusterTDEDatanodeSubTasks,
		ErrorNextStep:   StepOpenClusterTDEMasterSubTasks},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 执行datanode节点的更新
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDEDatanodeSubTasks,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     tde.ProcessOpenAllDatanodeTDE,
		SuccessNextStep: StepOpenClusterTDERecoverHa,
		ErrorNextStep:   StepOpenClusterTDEDatanodeSubTasks},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-4 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDERecoverHa,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: StepOpenClusterTDECallback,
		ErrorNextStep:   StepOpenClusterTDERecoverHa,
	})

	// Step-5 更新成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDECallback,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     callback.ProcessOpenTDESuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepOpenClusterTDECallback,
	})

	/////////////////////////////////////////////////////////
	// errStep-1 恢复集群和节点状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDERollback,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     callback.ProcessOpenClusterTDERollbackMeta,
		SuccessNextStep: StepOpenClusterTDERollbackRecoverHa,
		ErrorNextStep:   StepOpenClusterTDERollback},

		workflow.WithStepTimeout(15*time.Minute))

	// errStep-2 开启HA
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenClusterTDERollbackRecoverHa,
		Workflow:        WorkflowOpenClusterTDE,
		StepProcess:     xmaster.ProcessRecoverAppHaStatusInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepOpenClusterTDERollbackRecoverHa,
	})

	//----------------一个节点的升级流程--------------------------------------------
	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenNodeTDEBuildMeta,
		Workflow:        WorkflowOpenNodeTDE,
		StepProcess:     buildmeta.ProcessBuildMetaForOpenOneNodeTDE,
		SuccessNextStep: StepOpenNodeTDEUpdate,
		ErrorNextStep:   StepOpenNodeTDEBuildMeta,
	})

	// Step-2 更新一个节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenNodeTDEUpdate,
		Workflow:        WorkflowOpenNodeTDE,
		StepProcess:     tde.ProcessUpdateOneNodeTDE,
		SuccessNextStep: StepOpenNodeTDECheck,
		ErrorNextStep:   StepOpenNodeTDEUpdate,
	})

	//Step-3 检查状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenNodeTDECheck,
		Workflow:        WorkflowOpenNodeTDE,
		StepProcess:     mochow.ProcessCheckAllStatus,
		SuccessNextStep: StepOpenNodeTDECallback,
		ErrorNextStep:   StepOpenNodeTDECheck,
	}, workflow.WithStepTimeout(15*time.Minute))

	//Step-4 回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenNodeTDECallback,
		Workflow:        WorkflowOpenNodeTDE,
		StepProcess:     callback.ProcessUpdateOneNodeTDESuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepOpenNodeTDECallback,
	})
}
