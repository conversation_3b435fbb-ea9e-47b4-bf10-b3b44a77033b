package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	zoneComponent "icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// isNodeAvailable
func isNodeAvailable(node *vdbmodel.Node) bool {
	return node.Status == vdbmodel.NodeOrProxyStatusInUse ||
		node.Status == vdbmodel.NodeOrProxyStatusToCreate
}

// 普通blb只支持一个后端端口，因此取某个stand_alone版本的node.port
func getStandaloneBackendPort(ctx context.Context, app *vdbmodel.Application) int32 {
	backendPort := int32(0)

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			backendPort = int32(node.Port)
			return backendPort
		}
	}

	return int32(app.Port)
}

// 普通blb只支持一个后端端口，因此取某个集群版本的proxy.port
func getClusterBackendPort(ctx context.Context, app *vdbmodel.Application) int32 {
	backendPort := int32(0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			backendPort = int32(proxy.Port)
			return backendPort
		}
	}

	return int32(app.Port)
}

func getBackendPort(ctx context.Context, app *vdbmodel.Application) int32 {
	if app.Type == vdbmodel.AppTypeStandalone {
		return getStandaloneBackendPort(ctx, app)
	} else if app.Type == vdbmodel.AppTypeCluster {
		return getClusterBackendPort(ctx, app)
	}
	return int32(app.Port)
}

// 获取 LogicZone, 用于创建 blb 时，指定优先的 BLB 四层实例所在主AZ
func getLogicZone(ctx context.Context, app *vdbmodel.Application) (string, error) {

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node := node
			if isNodeAvailable(node) {
				return node.LogicZone, nil
			}
		}
	}

	resource.LoggerTask.Warning(ctx, "resource get LogicZone failed", logit.String("appId", app.AppID))
	return "", cerrs.ErrNotFound.Errorf("resource app(%s) blb LogicZone not found", app.AppID)
}

// 获取 PhysicalZone
func getPhysicalZone(ctx context.Context, app *vdbmodel.Application) (string, error) {

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node := node

			if isNodeAvailable(node) {
				return node.Azone, nil
			}

		}
	}

	resource.LoggerTask.Warning(ctx, "resource get PhysicalZone failed", logit.String("appId", app.AppID))
	return "", cerrs.ErrNotFound.Errorf("resource app(%s) blb PhysicalZone not found", app.AppID)
}

// 查询特定用户的物理 zone 对应的逻辑 zone
func getLogicZoneByPhysicalZone(ctx context.Context, userID string, physicalZone string) (string, error) {
	var logicZone string

	zoneMapperFunc, err := zoneComponent.ZoneOp().GetZoneMap(ctx, userID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return "", err
	}
	logicZone, found := zoneMapperFunc(physicalZone, false)
	if !found {
		return "", cerrs.ErrNotFound.Errorf("LogicZone not found by userID(%s) physicalZone(%s)", userID, physicalZone)
	}
	return logicZone, nil
}
