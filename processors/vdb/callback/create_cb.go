/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	apiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

// ProcessCreateSuccessCb 创建标准版成功的操作
func ProcessCreateSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := apiface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	if subTasksStatus != taskIface.SubTasksStatusSuccess {
		resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
		return fmt.Errorf("sub tasks status is not success")
	}

	if err := util.RegisterBackupPolicyIfNeeded(ctx, app.AppID); err != nil {
		resource.LoggerTask.Error(ctx, "check and register backup policy failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToCreate {
				node.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToCreate {
				proxy.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToCreate {
			master.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	for _, cluster := range app.Clusters {
		if len(cluster.DestSpec) != 0 && cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
	}

	/*
		新建集群后，更新集群cluster、interface表中规格信息，更新信息主要包括：
		mem_size 	<==> 	actual_mem_size
		disk_size 	<==> 	actual_disk_size
		cpu 		<==> 	actual_cpu
	*/
	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
		cluster.ActualCPU = cluster.CPU
		cluster.ActualMemSize = cluster.MemSize
		cluster.ActualDiskSize = int(cluster.DiskSize)
	}
	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			itf.Spec = itf.DestSpec
		}
		itf.ActualCPU = itf.CPU
		itf.ActualMemSize = itf.MemSize
		itf.ActualDiskSize = int(itf.DiskSize)
	}

	for _, master := range app.Masters {
		if master.DestSpec != master.Spec {
			master.Spec = master.DestSpec
		}
		master.ActualCPU = master.CPU
		master.ActualMemSize = master.MemSize
		master.ActualDiskSize = int(master.DiskSize)
	}

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	if err := util.CreateCloneRecoverTaskIfNeeded(ctx, app, param.CloneNeedSwitchEntrance); err != nil {
		resource.LoggerTask.Error(ctx, "create clone recover task failed", logit.Error("error", err))
		return err
	}

	return nil
}

func GetOrderTasks(ctx context.Context, orderModel *vdbmodel.OrderList) ([]*iface.Task, error) {
	taskIds := strings.Split(orderModel.TaskIDs, ",")
	tasks, err := resource.TaskOperator.RetrieveTasks(ctx, "task_id IN ?", taskIds)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

// ProcessCreateStandaloneErrorCb 创建失败的操作
func ProcessCreateErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusCreateFailed

	if err := vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}
