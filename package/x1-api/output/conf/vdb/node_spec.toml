[[NodeSpecList]]
node_spec = "vdb.fdm.small"
cpu = 2
mem = 4
min_allowed_datanode_num = 3
max_allowed_datanode_num = 3
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "free"
max_vector_data = 450000

[[NodeSpecList]]
node_spec = "vdb.p.small"
cpu = 2
mem = 4
min_allowed_datanode_num = 2
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "proxy"
max_vector_data = 0

[[NodeSpecList]]
node_spec = "vdb.m.small"
cpu = 2
mem = 4
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "master"
max_vector_data = 0

[[NodeSpecList]]
node_spec = "vdb.m.medium"
cpu = 4
mem = 8
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "master"
max_vector_data = 0

[[NodeSpecList]]
node_spec = "vdb.dc.small"
cpu = 2
mem = 4
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "compute"
max_vector_data = 450000

[[NodeSpecList]]
node_spec = "vdb.dc.medium"
cpu = 4
mem = 8
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "compute"
max_vector_data = 900000

[[NodeSpecList]]
node_spec = "vdb.dc.large"
cpu = 8
mem = 16
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 384
default_disk_flavor = 24
node_type = "compute"
max_vector_data = 1800000

[[NodeSpecList]]
node_spec = "vdb.dc.2xlarge"
cpu = 16
mem = 32
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 768
default_disk_flavor = 48
node_type = "compute"
max_vector_data = 3600000

[[NodeSpecList]]
node_spec = "vdb.dc.4xlarge"
cpu = 32
mem = 64
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 1408
default_disk_flavor = 96
node_type = "compute"
max_vector_data = 8000000

[[NodeSpecList]]
node_spec = "vdb.dm.small"
cpu = 2
mem = 16
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 24
node_type = "memory"
max_vector_data = 1800000

[[NodeSpecList]]
node_spec = "vdb.dm.medium"
cpu = 4
mem = 32
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 48
node_type = "memory"
max_vector_data = 3600000

[[NodeSpecList]]
node_spec = "vdb.dm.large"
cpu = 8
mem = 64
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 384
default_disk_flavor = 96
node_type = "memory"
max_vector_data = 8000000

[[NodeSpecList]]
node_spec = "vdb.dm.2xlarge"
cpu = 16
mem = 128
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 768
default_disk_flavor = 192
node_type = "memory"
max_vector_data = 16500000

[[NodeSpecList]]
node_spec = "vdb.dm.4xlarge"
cpu = 32
mem = 256
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 1408
default_disk_flavor = 384
node_type = "memory"
max_vector_data = 32500000

[[NodeSpecList]]
node_spec = "vdb.db.small"
cpu = 2
mem = 8
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 20
node_type = "balanced"
max_vector_data = 900000

[[NodeSpecList]]
node_spec = "vdb.db.medium"
cpu = 4
mem = 16
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 192
default_disk_flavor = 24
node_type = "balanced"
max_vector_data = 1800000

[[NodeSpecList]]
node_spec = "vdb.db.large"
cpu = 8
mem = 32
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 384
default_disk_flavor = 48
node_type = "balanced"
max_vector_data = 3600000

[[NodeSpecList]]
node_spec = "vdb.db.2xlarge"
cpu = 16
mem = 64
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 768
default_disk_flavor = 96
node_type = "balanced"
max_vector_data = 8000000

[[NodeSpecList]]
node_spec = "vdb.db.4xlarge"
cpu = 32
mem = 128
min_allowed_datanode_num = 3
max_allowed_datanode_num = 1024
network_throughput_in_mbps = 1408
default_disk_flavor = 192
node_type = "balanced"
max_vector_data = 16500000