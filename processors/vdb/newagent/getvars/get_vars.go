package getvars

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

type Parameter struct {
	Entity         string
	EntityType     string
	EngineType     string
	App            *vdbmodel.Application
	Cluster        *vdbmodel.Cluster
	Itf            *vdbmodel.Interface
	Node           *vdbmodel.Node
	Proxy          *vdbmodel.Proxy
	Master         *vdbmodel.Master
	PackageVersion string
}

func Instance() GetVarsIface {
	panic("implement me")
}

func (param *Parameter) GetProxyRootPassword(ctx context.Context) string {
	userAuth, err := vdbmodel.UserAuthGetInUse(ctx, param.App.AppID, "root")
	if err != nil {
		return ""
	}
	return userAuth.Password
}

func GetParamters(ctx context.Context, appId string, entity string) (*Parameter, error) {
	var err error
	param := &Parameter{}
	param.App, err = vdbmodel.ApplicationGetByAppID(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load application from db failed", logit.Error("error", err))
		return nil, err
	}

	if err := FillParameterEntityInfo(ctx, param, entity); err != nil {
		return nil, err
	}
	return param, nil
}

func FillParameterEntityInfo(ctx context.Context, param *Parameter, entity string) error {
	var resourceID string
cloop:
	for _, cluster := range param.App.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == entity {
				param.Cluster = cluster
				param.Node = node
				param.Entity = entity
				param.EntityType = "datanode"
				param.EngineType = cluster.Engine
				resourceID = node.ResourceID
				break cloop
			}
		}
	}
	if param.Node == nil {
	iloop:
		for _, itf := range param.App.Interfaces {
			for _, proxy := range itf.Proxies {
				if proxy.ProxyID == entity {
					param.Itf = itf
					param.Proxy = proxy
					param.Entity = entity
					param.EntityType = "proxy"
					param.EngineType = itf.Engine
					resourceID = proxy.ResourceID
					break iloop
				}
			}
		}
	}
	if param.Node == nil {
		for _, master := range param.App.Masters {
			if master.MasterID == entity {
				param.Master = master
				param.Entity = entity
				param.EntityType = "master"
				param.EngineType = master.Engine
				resourceID = master.ResourceID
				break
			}

		}
	}
	if len(resourceID) == 0 {
		resource.LoggerTask.Warning(ctx, "entity not found", logit.String("entity", entity))
		return fmt.Errorf("entity %s not found", entity)
	}

	return nil
}

func (param *Parameter) GetMasterAddrList() string {
	var masterAddrList string = "list://"

	if param.App.Type == vdbmodel.AppTypeStandalone {
		for _, cluster := range param.App.Clusters {
			for _, node := range cluster.Nodes {
				masterAddrList := fmt.Sprintf("%s:%d", "127.0.0.1", node.Port+1) // 单节点模式下，master服务端口为proxy服务端口+1
				masterAddrList += masterAddrList
				return masterAddrList
			}
		}
	}

	for _, master := range param.App.Masters {
		masterAddrList += fmt.Sprintf("%s:%d,", master.IP, master.Port)
	}
	masterAddrList = strings.TrimSuffix(masterAddrList, ",")
	return masterAddrList
}
