/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/02 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/buildmeta"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func loadSnapshotRestoreOldCDSID(ctx context.Context, taskID string, nodeID string) (string, error) {
	key := "snapshot_restore_oldcds:" + taskID + ":" + nodeID
	newCDSID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return newCDSID, nil
}

func saveSnapshotRestoreOldCDSID(ctx context.Context, taskID string, nodeID string, oldCDSID string) error {
	key := "snapshot_restore_oldcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.SetNX(ctx, key, oldCDSID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteSnapshotRestoreOldCDSID(ctx context.Context, taskID string, nodeID string) error {
	key := "snapshot_restore_oldcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processRecoverClusterDeleteOldCDS(ctx context.Context, taskID string, app *vdbmodel.Application, nodeID string) error {
	engine := util.GetEngineByID(ctx, app, nodeID)
	if engine == "" {
		resource.LoggerTask.Error(ctx, "engine get by id failed", logit.String("nodeID", nodeID))
		return errors.New("engine get by id failed")
	}

	resourceID := ""

	switch engine {
	case vdbmodel.EngineVDBDataNode:
		node, _, err := buildmeta.FindNodeByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}

		resourceID = node.ResourceID

	case vdbmodel.EngineVDBProxy:
		resource.LoggerTask.Error(ctx, "wrong engine", logit.String("engine", engine))
		return errors.New("engine not support")

	case vdbmodel.EngineVDBMaster:
		master, err := buildmeta.FindMasterByIDInApp(ctx, app, nodeID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "master not found", logit.Error("error", err))
			return err
		}

		resourceID = master.ResourceID
	}
	// 尝试从 broker-Redis 中获取 newCDS ID
	oldCDSID, err := LoadSnapshotRestoreNewCDSID(ctx, taskID, nodeID)
	if err == nil && len(oldCDSID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have create oldCDSID: %s", oldCDSID))
	}

	// 若无 oldCDSID 则查询 CDS ID
	if oldCDSID == "" {
		volumeList, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
			VmID:      resourceID,
			UserID:    app.UserID,
			IsShortID: false,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cds list faield",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.Error("error", err))
			return err
		}

		for _, volume := range volumeList {
			if volume.Type == "System" {
				continue
			}

			if volume.Name != getNewCDSName(taskID, nodeID) {
				oldCDSID = volume.Id
				break
			}
		}

		if err = saveSnapshotRestoreOldCDSID(ctx, taskID, nodeID, oldCDSID); err != nil {
			resource.LoggerTask.Warning(ctx, "save old cdsid to redis failed",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
				logit.Error("error", err))
		}
	}

	resource.LoggerTask.Notice(ctx, "old cds info",
		logit.String("NodeID:", base_utils.Format(nodeID)),
		logit.String("appID:", base_utils.Format(app.AppID)),
		logit.String("oldCDSID", oldCDSID))

	// (4) 删除旧 CDSID
	if oldCDSID == "" {
		return nil
	}

	for {
		// 获取 CDS 详情
		resp, err := bccresource.BccResourceOp().GetCdsDetail(ctx, &bccresource.GetCdsDetailParam{
			UserID:   app.UserID,
			VolumeID: oldCDSID,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cds detail faield",
				logit.String("NodeID:", base_utils.Format(nodeID)),
				logit.String("appID:", base_utils.Format(app.AppID)),
				logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
				logit.Error("error", err))
			return err
		}

		switch resp.Status {
		case "Available":
			// delete cds
			err = bccresource.BccResourceOp().DeleteCds(ctx, &bccresource.DeleteCdsParam{
				UserID:   app.UserID,
				VolumeID: oldCDSID,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "cds delete failed",
					logit.String("NodeID:", base_utils.Format(nodeID)),
					logit.String("appID:", base_utils.Format(app.AppID)),
					logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
					logit.Error("error", err))
				return err
			}

			if err = deleteSnapshotRestoreOldCDSID(ctx, taskID, nodeID); err != nil {
				resource.LoggerTask.Warning(ctx, "delete old cdsid from redis failed",
					logit.String("NodeID:", base_utils.Format(nodeID)),
					logit.String("appID:", base_utils.Format(app.AppID)),
					logit.Error("error", err))
			}
			return nil
		case "InUse":
			// detach cds
			err = bccresource.BccResourceOp().DetachCds(ctx, &bccresource.DetachCdsParam{
				UserID:    app.UserID,
				VolumeID:  oldCDSID,
				VmID:      resourceID,
				IsShortID: false,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "cds detach failed",
					logit.String("NodeID:", base_utils.Format(nodeID)),
					logit.String("appID:", base_utils.Format(app.AppID)),
					logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
					logit.String("VmID:", base_utils.Format(resourceID)),
					logit.Error("error", err))
				return err
			}
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}
