/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/07/06, by wangbin34
*/

/*
DESCRIPTION
将后端资源信息录入到数据库中
*/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// 记录资源层 vpc 等信息(用户 vpc 和 资源层 vpc 不在同一 vpc 时使用)
func saveResourceInfo(ctx context.Context, app *vdbmodel.Application) error {
	var curBlbList []*vdbmodel.BLB
	env := blbv2.Instance().GetEnv(ctx)

	azone, err := getPhysicalZone(ctx, app)
	if err != nil {
		return err
	}

	ResourceUserID := ""
	ResourceVpcID := ""
	ResourceSubnetID := ""
	if blbv2.Instance().IsUsePrivateResource(ctx, app.UserID) {
		ResourceUserID = env.ResourcePrivateUserId
		ResourceVpcID = env.ResourcePrivateVpcId
		subnetID, found := blbv2.Instance().GetResourcePrivateSubnet(ctx, azone)
		if !found {
			resource.LoggerTask.Warning(ctx, "resource_private get subnet failed",
				logit.String("appId", app.AppID),
				logit.String("azone", azone),
			)
			return cerrs.ErrNotFound.Errorf("resource_private app(%s) subnet not found for azone(%s)", app.AppID, azone)
		}
		ResourceSubnetID = subnetID
	} else {
		ResourceUserID = env.ResourceCloudUserId
		ResourceVpcID = env.ResourceCloudVpcId
		subnetID, found := blbv2.Instance().GetResourceCloudSubnet(ctx, azone)
		if !found {
			resource.LoggerTask.Warning(ctx, "resource_cloud get subnet failed",
				logit.String("appId", app.AppID),
				logit.String("azone", azone),
			)
			return cerrs.ErrNotFound.Errorf("resource_cloud app(%s) subnet not found for azone(%s)", app.AppID, azone)
		}
		ResourceSubnetID = subnetID
	}

	for _, b := range app.BLBs {
		if len(b.BlbID) != 0 {
			continue
		}
		b.ResourceUserID = ResourceUserID
		b.ResourceVpcID = ResourceVpcID
		b.ResourceSubnetID = ResourceSubnetID
		curBlbList = append(curBlbList, b)
	}

	if len(curBlbList) == 0 {
		return nil
	}

	if err := vdbmodel.BLBsSave(ctx, curBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppID))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}
