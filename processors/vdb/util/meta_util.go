package util

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
)

func GetMaxNodeIndex(ctx context.Context, app *vdbmodel.Application) (int, error) {
	index := 0
	for _, cluster := range app.Clusters {
		for _, node := range FetchAllNodesOfCluster(ctx, cluster) {
			chunks := strings.Split(node.NodeID, "-")
			curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
			if err != nil {
				return 0, fmt.Errorf("extract node index from id %s failed, err:%s", node.NodeID, err.Error())
			}
			if curIdx > index {
				index = curIdx
			}
		}
	}
	return index, nil
}

func GetMaxProxyIndex(ctx context.Context, app *vdbmodel.Application) (int, error) {
	index := 0
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			chunks := strings.Split(proxy.ProxyID, "-")
			curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
			if err != nil {
				return 0, fmt.Errorf("extract node index from id %s failed, err:%s", proxy.ProxyID, err.Error())
			}
			if curIdx > index {
				index = curIdx
			}
		}
	}
	return index, nil
}

func GetMaxMasterIndex(ctx context.Context, app *vdbmodel.Application) (int, error) {
	index := 0
	for _, master := range app.Masters {
		chunks := strings.Split(master.MasterID, "-")
		curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
		if err != nil {
			return 0, fmt.Errorf("extract node index from id %s failed, err:%s", master.MasterID, err.Error())
		}
		if curIdx > index {
			index = curIdx
		}

	}
	return index, nil
}

// FindClusterByID
// 遍历app中的cluster，返回要处理（cluster.ClusterID == Id）的shard
func FindClusterByID(ctx context.Context, app *vdbmodel.Application, ID string) (*vdbmodel.Cluster, error) {
	for _, cluster := range app.Clusters {
		if cluster.ClusterID == ID {
			return cluster, nil
		}
	}
	return nil, fmt.Errorf("cannot find cluster by id %s", ID)
}

// FindNodeByID
// 遍历cluster中的node，返回要处理（node.NodeID == Id）的node
func FindNodeByID(ctx context.Context, cluster *vdbmodel.Cluster, ID string) (*vdbmodel.Node, error) {
	for _, node := range cluster.Nodes {
		if node.NodeID == ID {
			return node, nil
		}
	}
	return nil, fmt.Errorf("cannot find node by id %s", ID)
}

func GetMaxInterfaceIdx(ctx context.Context, itfs []*vdbmodel.Interface) (int, error) {
	index := 0
	for _, itf := range itfs {
		chunks := strings.Split(itf.InterfaceID, "-")
		curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
		if err != nil {
			return 0, fmt.Errorf("extract interface index from id %s failed, err:%s", itf.InterfaceID, err.Error())
		}
		if curIdx > index {
			index = curIdx
		}
	}
	return index, nil
}

func GetMaxClusterIdx(ctx context.Context, clusters []*vdbmodel.Cluster) (int, error) {
	index := 0
	for _, cluster := range clusters {
		chunks := strings.Split(cluster.ClusterID, "-")
		curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
		if err != nil {
			return 0, fmt.Errorf("extract interface index from id %s failed, err:%s", cluster.ClusterID, err.Error())
		}
		if curIdx > index {
			index = curIdx
		}
	}
	return index, nil
}
