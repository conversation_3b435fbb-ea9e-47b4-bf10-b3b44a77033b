package restartdatanode

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

const (
	DataNodeWorkDir     = "/home/<USER>/mochow-datanode"
	MonitorAgentWorkDir = "/home/<USER>/monitor-agent"
	StandaloneWorkDir   = "/home/<USER>/mochow-standalone"
)

type XagentRequest struct {
	WorkDir string `json:"work_dir"`
}

func ProcessModifySpecRestartDataNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.ResourceType == "container" {
		resource.LoggerTask.Warning(ctx, "container resource no need restart datanode")
		return nil
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster
	find := false

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeID == param.TargetNodeID && node.Status == vdbmodel.NodeOrProxyStatusModifying {
				targetNode = node
				targetCluster = cluster
				find = true
				break OuterLoop
			}
		}
	}

	if !find {
		resource.LoggerTask.Error(ctx, "can not find target node", logit.String("nodeID", param.TargetNodeID))
		return errors.New("can not find target node")
	}

	if targetCluster.DestSpec == targetCluster.Spec {
		resource.LoggerTask.Trace(ctx, "target cluster spec not change no need restart datanode",
			logit.String("clusterID", targetCluster.ClusterID))
		return nil
	}

	if err := doRestartDataNode(ctx, app, targetNode); err != nil {
		resource.LoggerTask.Error(ctx, "restart datanode error", logit.String("nodeID", param.TargetNodeID))
		return err
	}

	return nil
}

func doRestartDataNode(ctx context.Context, app *vdbmodel.Application, node *vdbmodel.Node) error {
	mochowDir := DataNodeWorkDir
	if app.Type == vdbmodel.AppTypeStandalone {
		mochowDir = StandaloneWorkDir
	}
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: "restart",
		Params: &XagentRequest{
			WorkDir: mochowDir,
		},
		Product: vdbmodel.ProductVDB,
	}

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "restart mochow-datanode fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
			logit.Error("err", err))
		return err
	}

	req = &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: "restart",
		Params: &XagentRequest{
			WorkDir: MonitorAgentWorkDir,
		},
		Product: vdbmodel.ProductVDB,
	}

	_, err = xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "restart monitor-agent fail",
			logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessModifySpecRestartStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.ResourceType == "container" {
		resource.LoggerTask.Warning(ctx, "container resource no need restart mochow")
		return nil
	}

	var targetNode *vdbmodel.Node
	var targetCluster *vdbmodel.Cluster

OuterLoop:
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			targetNode = node
			targetCluster = cluster
			break OuterLoop
		}
	}

	if targetCluster.DestSpec == targetCluster.Spec {
		resource.LoggerTask.Trace(ctx, "target cluster spec not change no need restart datanode",
			logit.String("clusterID", targetCluster.ClusterID))
		return nil
	}

	if err := doRestartDataNode(ctx, app, targetNode); err != nil {
		resource.LoggerTask.Error(ctx, "restart datanode error", logit.String("nodeID", targetNode.NodeID))
		return err
	}

	return nil
}
