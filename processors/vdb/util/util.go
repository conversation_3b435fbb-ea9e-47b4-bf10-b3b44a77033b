package util

import (
	"encoding/binary"
	"fmt"
	"math/big"
	"net"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
)

const (
	BNSUnclearStatus = "unclear"
	BNSClearStatus   = "cleared"
)

func GetVersion(app *vdbmodel.Application) string {
	_, version := GetImageIDAndVersion(app.ImageID)
	return version
}

func InetNtoA(ip int64) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		byte(ip>>24), byte(ip>>16), byte(ip>>8), byte(ip))
}

func InetAtoN(ip string) int64 {
	ret := big.NewInt(0)
	ret.SetBytes(net.ParseIP(ip).To4())
	return ret.Int64()
}

func LittleEndianInetNtoA(ip int64) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		byte(ip), byte(ip>>8), byte(ip>>16), byte(ip>>24))
}

func LittleEndianInetAtoN(ipStr string) int64 {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return 0
	}
	ip = ip.To4()
	if ip == nil {
		return 0
	}
	return int64(binary.LittleEndian.Uint32(ip))
}
