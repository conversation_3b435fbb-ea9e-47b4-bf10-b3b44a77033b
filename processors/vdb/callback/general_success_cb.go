/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"errors"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	apiface "icode.baidu.com/baidu/vdb/x1-api/apisdk/iface"
	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"

	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
	"icode.baidu.com/baidu/vdb/x1-task-manager/processors/vdb/util"
)

func ProcessUpgradeSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if app.Type == vdbmodel.AppTypeStandalone {
				if node.Status == vdbmodel.NodeOrProxyStatusToUpgrade {
					node.Status = vdbmodel.NodeOrProxyStatusInUse
				}
			} else if app.Type == vdbmodel.AppTypeCluster {
				if node.Status == vdbmodel.NodeOrProxyStatusUpgradeSucc {
					node.Status = vdbmodel.NodeOrProxyStatusInUse
				}
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusUpgradeSucc {
				proxy.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusUpgradeSucc {
			master.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func ProcessOpenTDESuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	TDEs, err := vdbmodel.TDEGetByAppID(ctx, app.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get tde error", logit.Error("error", err))
		return err
	}

	if len(TDEs) == 0 || len(TDEs) > 1 {
		resource.LoggerTask.Error(ctx, "tde is not correct, please check it.")
		return errors.New("redis tls conf is not correct")
	}
	for _, tde := range TDEs {
		if tde.Status == vdbmodel.TdeStatusToCreate {
			tde.Status = vdbmodel.TdeStatusInUse
		}
	}

	err = vdbmodel.TdesSave(ctx, TDEs)
	if err != nil {
		errorMessage := "save tde info failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppID)))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusModifySucc {
				node.Status = vdbmodel.NodeOrProxyStatusInUse
			}
		}
	}

	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusModifySucc {
			master.Status = vdbmodel.NodeOrProxyStatusInUse
		}
	}

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func ProcessBackupSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.BackupStatus = vdbmodel.BackupSuccess

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

func ProcessRecoverSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := apiface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	app.Status = vdbmodel.AppStatusRunning

	err = vdbmodel.ApplicationsSave(ctx, []*vdbmodel.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	if !param.RevoverParam.SwitchEntrance {
		return nil
	}

	firstAppID := param.RevoverParam.SrcAppID
	secondAppID := teu.Entity

	if err := util.CreateSwitchEntranceTask(ctx, firstAppID, secondAppID); err != nil {
		resource.LoggerTask.Error(ctx, "create switch entrance task failed", logit.Error("error", err))
		return err
	}

	return nil
}
