/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/vdb/x1-api/model/vdbmodel"
	"icode.baidu.com/baidu/vdb/x1-task-manager/library/resource"
)

// ProcessDeleteToDeleteNodes process node which need delete
func ProcessDeleteToDeleteNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	toDeleteResources := []string{}
	toDeleteNodes := []*vdbmodel.Node{}
	toDeleteProxies := []*vdbmodel.Proxy{}
	toDeleteMasterNodes := []*vdbmodel.Master{}
	needAddtoDeleteNodes := []*vdbmodel.ToDeleteNode{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == vdbmodel.NodeOrProxyStatusToDelete {
				toDeleteNodes = append(toDeleteNodes, node)
				if len(node.ResourceID) != 0 {
					toDeleteResources = append(toDeleteResources, node.ResourceID)
				}
			} else if node.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				toDeleteNodes = append(toDeleteNodes, node)

				needAddtoDeleteNodes = append(needAddtoDeleteNodes, &vdbmodel.ToDeleteNode{
					AppID:        app.AppID,
					NodeID:       node.NodeID,
					ShortID:      node.ID,
					Engine:       vdbmodel.EngineVDBDataNode,
					IP:           node.IP,
					FloatingIP:   node.FloatingIP,
					RootPassword: node.RootPassword,
					UserID:       app.UserID,
					ResourceID:   node.ResourceID,
					DeleteTime:   time.Now(),
					Status:       vdbmodel.ToBeDeleted,
				})

			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxies {
			if proxy.Status == vdbmodel.NodeOrProxyStatusToDelete {
				toDeleteProxies = append(toDeleteProxies, proxy)
				if len(proxy.ResourceID) != 0 {
					toDeleteResources = append(toDeleteResources, proxy.ResourceID)
				}
			} else if proxy.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
				toDeleteProxies = append(toDeleteProxies, proxy)

				needAddtoDeleteNodes = append(needAddtoDeleteNodes, &vdbmodel.ToDeleteNode{
					AppID:        app.AppID,
					NodeID:       proxy.ProxyID,
					ShortID:      proxy.ID,
					Engine:       vdbmodel.EngineVDBProxy,
					IP:           proxy.IP,
					FloatingIP:   proxy.FloatingIP,
					RootPassword: proxy.RootPassword,
					UserID:       app.UserID,
					ResourceID:   proxy.ResourceID,
					DeleteTime:   time.Now(),
					Status:       vdbmodel.ToBeDeleted,
				})

			}
		}
	}
	for _, master := range app.Masters {
		if master.Status == vdbmodel.NodeOrProxyStatusToDelete {
			toDeleteMasterNodes = append(toDeleteMasterNodes, master)
			if len(master.ResourceID) != 0 {
				toDeleteResources = append(toDeleteResources, master.ResourceID)
			}
		} else if master.Status == vdbmodel.NodeOrProxyStatusToFakeDelete {
			toDeleteMasterNodes = append(toDeleteMasterNodes, master)

			needAddtoDeleteNodes = append(needAddtoDeleteNodes, &vdbmodel.ToDeleteNode{
				AppID:        app.AppID,
				NodeID:       master.MasterID,
				ShortID:      master.ID,
				Engine:       vdbmodel.EngineVDBMaster,
				IP:           master.IP,
				FloatingIP:   master.FloatingIP,
				RootPassword: master.RootPassword,
				UserID:       app.UserID,
				ResourceID:   master.ResourceID,
				DeleteTime:   time.Now(),
				Status:       vdbmodel.ToBeDeleted,
			})

		}
	}
	if len(toDeleteResources) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete resource", logit.String("nodes", base_utils.Format(toDeleteResources)))

		err = bccresource.BccResourceOp().DeleteBccResource(ctx, &bccresource.DeleteBccResourceParams{
			InstanceIds: toDeleteResources,
			UserID:      app.UserID,
		})

		if err != nil {
			resource.LoggerTask.Error(ctx, "delete bcc resource failed", logit.Error("error", err))
			return err
		}
	}

	if len(needAddtoDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to add to delete table", logit.String("nodes", base_utils.Format(needAddtoDeleteNodes)))
		err = vdbmodel.ToDeleteNodesSave(ctx, needAddtoDeleteNodes)
		if err != nil {
			resource.LoggerTask.Error(ctx, "add bcc resource to tobedelete table failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := vdbmodel.NodeDeleteMulti(ctx, toDeleteNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteProxies) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("proxies", base_utils.Format(toDeleteProxies)))
		if err := vdbmodel.ProxyDeleteMulti(ctx, toDeleteProxies); err != nil {
			resource.LoggerTask.Error(ctx, "delete proxy meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteMasterNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("Masters", base_utils.Format(toDeleteMasterNodes)))
		if err := vdbmodel.MasterDeleteMulti(ctx, toDeleteMasterNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete master meta failed", logit.Error("error", err))
			return err
		}
	}

	app, err = vdbmodel.ApplicationGetByAppID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	toDeleteCluster := []*vdbmodel.Cluster{}
	for _, cluster := range app.Clusters {
		if len(cluster.Nodes) == 0 {
			toDeleteCluster = append(toDeleteCluster, cluster)
		}
	}
	if len(toDeleteCluster) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("clusters", base_utils.Format(toDeleteCluster)))
		if err := vdbmodel.ClusterDeleteMulti(ctx, toDeleteCluster); err != nil {
			resource.LoggerTask.Error(ctx, "delete cluster meta failed", logit.Error("error", err))
			return err
		}
	}

	toDeleteItf := []*vdbmodel.Interface{}
	for _, itf := range app.Interfaces {
		if len(itf.Proxies) == 0 {
			toDeleteItf = append(toDeleteItf, itf)
		}
	}
	if len(toDeleteItf) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from vdbmodel", logit.String("interfaces", base_utils.Format(toDeleteItf)))
		if err := vdbmodel.InterfaceDeleteMulti(ctx, toDeleteItf); err != nil {
			resource.LoggerTask.Error(ctx, "delete cluster meta failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}
