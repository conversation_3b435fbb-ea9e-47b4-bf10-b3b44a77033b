# Service的名字，必选，需自定义修改
Name = "x1-model"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，ms
# ConnTimeOut = 200
# 写数据超时，ms
# WriteTimeOut = 200
# 读数据超时，ms
# ReadTimeOut = 1000

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
[[Resource.Manual.default]]
Host = "*************"
Port = 3306
[[Resource.Manual.bdtest]]
Host = "smartbns.bcevdb-bmi0000.xdb.all.serv"
Port = 5361
[[Resource.Manual.onlinebj]]
Host = "smartbns.bcebcevdbbj-bmi0000.xdb.all.serv"
Port = 5375
[[Resource.Manual.onlinebd]]
Host = "***********"
Port = 11559
[[Resource.Manual.onlinegz]]
Host = "************"
Port = 11560
[[Resource.Manual.onlinesu]]
Host = "smartbns.bcevdbsu-bmi0000.xdb.all.serv"
Port = 11873
[[Resource.Manual.dbstackadaptorbcc]]
Host = "127.0.0.1"
Port = 6203

# mysql 特有的配置，可选，需自定义修改
[MySQL]
Username    = "root"
Password    = "paastest123"
DBName      = "bce_scs_x1"
DBDriver    = "mysql"
# MaxOpenPerIP 每个 ip 最多连接数，若过小，会出现查询排队等待的情况
# 若为0-则不限制
MaxOpenPerIP= 5
# MaxIdlePerIP 每个 ip 最多连接空闲数，最大可以设置成 和 MaxOpenPerIP 一样
# 若为0-使用 sql 标准库的默认值2
MaxIdlePerIP= 5
# ConnMaxLifeTime 连接复用最长时间，单位 ms
# 若为0-则不限制，连接不会关闭
# 可以根据线上实际情况，设置一个比较大的合适的时间，以减少创建新连接带来的不稳定性
ConnMaxLifeTime = 5000
# 是否sql注释传递logid
LogIDTransport = true
# 若是 DDBS 或者 数据库不支持prepare，DSNParams 需配置上 interpolateParams=true
DSNParams ="charset=utf8&timeout=90s&collation=utf8mb4_unicode_ci&parseTime=true"

[MySQL.IDC_SPEC.bdtest]
DBName   = "bce_vdb"
Username = "bce_vdb_w"
Password = "=QdP9UN=WDJ"
[MySQL.IDC_SPEC.onlinebj]
DBName   = "bce_vdb"
Username = "bce_vdb_w"
Password = "lQExPXiEyxvs="
[MySQL.IDC_SPEC.onlinebd]
DBName   = "bce_vdb"
Username = "bce_vdb_w"
Password = "A+szpguVDxkv7M"
[MySQL.IDC_SPEC.onlinegz]
DBName   = "bce_vdb"
Username = "bce_vdb_w"
Password = "zxehrqxh!ufoaqC"
[MySQL.IDC_SPEC.onlinesu]
DBName   = "bce_vdb"
Username = "bce_vdb_w"
Password = "7kJpOYyV,g2sO3"
[MySQL.IDC_SPEC.dbstackadaptorbcc]
DBName   = "bce_vdb"
Username = "paas_x1_w"
Password = "paas_x1_w_pwd"
DSNParams ="charset=utf8&timeout=90s&collation=utf8mb4_unicode_ci&parseTime=true&interpolateParams=true"

